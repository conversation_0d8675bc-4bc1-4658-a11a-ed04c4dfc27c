import 'package:flutter/material.dart';
import 'package:culture_connect/screens/travel/car_rental/car_rental_list_screen.dart';
import 'package:culture_connect/screens/travel/car_rental/car_rental_details_screen.dart';
import 'package:culture_connect/models/travel/car_rental.dart';

/// Register all car rental-related routes
Map<String, WidgetBuilder> carRentalRoutes = {
  '/travel/car-rental': (context) => const CarRentalListScreen(),
  '/travel/car-rental/details': (context) {
    final carRental = ModalRoute.of(context)!.settings.arguments as CarRental;
    return CarRentalDetailsScreen(carRental: carRental);
  },
};
