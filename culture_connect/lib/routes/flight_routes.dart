import 'package:flutter/material.dart';
import 'package:culture_connect/screens/travel/flight/flight_search_screen.dart';
import 'package:culture_connect/screens/travel/flight/flight_list_screen.dart';
import 'package:culture_connect/screens/travel/flight/flight_details_screen.dart';
import 'package:culture_connect/screens/travel/flight/flight_booking_confirmation_screen.dart';
import 'package:culture_connect/screens/travel/flight/flight_booking_management_screen.dart';
import 'package:culture_connect/screens/travel/flight/flight_status_tracking_screen.dart';
import 'package:culture_connect/models/travel/flight.dart';

/// Register all flight-related routes
Map<String, WidgetBuilder> flightRoutes = {
  '/travel/flights/search': (context) => const FlightSearchScreen(),
  '/travel/flights': (context) => const FlightListScreen(),
  '/travel/flight/details': (context) {
    final flight = ModalRoute.of(context)!.settings.arguments as Flight;
    return FlightDetailsScreen(flight: flight);
  },
  '/travel/flight/booking/confirmation': (context) =>
      const FlightBookingConfirmationScreen(),
  '/travel/flight/booking/management': (context) {
    final bookingReference =
        ModalRoute.of(context)!.settings.arguments as String;
    return FlightBookingManagementScreen(bookingReference: bookingReference);
  },
  '/travel/flight/status': (context) {
    final args =
        ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
    return FlightStatusTrackingScreen(
      flightNumber: args['flightNumber'] as String,
      departureDate: args['departureDate'] as DateTime,
    );
  },
};
