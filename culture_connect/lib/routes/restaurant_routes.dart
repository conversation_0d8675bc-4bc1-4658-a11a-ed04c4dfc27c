import 'package:flutter/material.dart';
import 'package:culture_connect/screens/travel/restaurant/restaurant_list_screen.dart';
import 'package:culture_connect/screens/travel/restaurant/restaurant_details_screen_enhanced.dart';
import 'package:culture_connect/screens/travel/restaurant/restaurant_reservation_screen.dart';
import 'package:culture_connect/screens/travel/restaurant/reservation_confirmation_screen.dart';
import 'package:culture_connect/screens/travel/restaurant/reservation_management_screen.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/models/travel/restaurant_reservation.dart';

/// Register all restaurant-related routes
Map<String, WidgetBuilder> restaurantRoutes = {
  '/travel/restaurants': (context) => const RestaurantListScreen(),
  '/travel/restaurant/details': (context) {
    final restaurant = ModalRoute.of(context)!.settings.arguments as Restaurant;
    return RestaurantDetailsScreenEnhanced(restaurant: restaurant);
  },
  '/travel/restaurant/reservation': (context) {
    final restaurant = ModalRoute.of(context)!.settings.arguments as Restaurant;
    return RestaurantReservationScreen(restaurant: restaurant);
  },
  '/travel/restaurant/reservation/confirmation': (context) {
    final args =
        ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
    final reservation = args['reservation'] as RestaurantReservation;
    final restaurant = args['restaurant'] as Restaurant;
    return ReservationConfirmationScreen(
      reservation: reservation,
      restaurant: restaurant,
    );
  },
  '/travel/restaurant/reservations': (context) =>
      const ReservationManagementScreen(),
};
