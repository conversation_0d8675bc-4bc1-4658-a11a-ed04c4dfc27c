import 'package:flutter/material.dart';
import 'package:culture_connect/screens/travel/private_security/private_security_list_screen.dart';
import 'package:culture_connect/screens/travel/private_security/private_security_details_screen.dart';
import 'package:culture_connect/screens/travel/private_security/private_security_booking_screen.dart';
import 'package:culture_connect/screens/travel/private_security/private_security_booking_confirmation_screen.dart';
import 'package:culture_connect/screens/travel/private_security/private_security_management_screen.dart';
import 'package:culture_connect/models/travel/private_security.dart';

/// Register all private security-related routes
Map<String, WidgetBuilder> privateSecurityRoutes = {
  '/travel/private-security': (context) => const PrivateSecurityListScreen(),
  '/travel/private-security/details': (context) {
    final privateSecurity = ModalRoute.of(context)!.settings.arguments as PrivateSecurity;
    return PrivateSecurityDetailsScreen(privateSecurity: privateSecurity);
  },
  '/travel/private-security/booking': (context) {
    final privateSecurity = ModalRoute.of(context)!.settings.arguments as PrivateSecurity;
    return PrivateSecurityBookingScreen(privateSecurity: privateSecurity);
  },
  '/travel/private-security/booking/confirmation': (context) {
    final args = ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
    return PrivateSecurityBookingConfirmationScreen(
      privateSecurity: args['privateSecurity'] as PrivateSecurity,
      startDate: args['startDate'] as DateTime,
      endDate: args['endDate'] as DateTime,
      personnelCount: args['personnelCount'] as int,
      hasVehicle: args['hasVehicle'] as bool,
      vehicleType: args['vehicleType'] as String?,
      specialInstructions: args['specialInstructions'] as String,
      totalPrice: args['totalPrice'] as double,
      bookingId: args['bookingId'] as String,
    );
  },
  '/travel/private-security/management': (context) => const PrivateSecurityManagementScreen(),
};
