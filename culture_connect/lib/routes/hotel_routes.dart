import 'package:flutter/material.dart';
import 'package:culture_connect/screens/travel/hotel/hotel_list_screen_enhanced.dart';
import 'package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart';
import 'package:culture_connect/models/travel/hotel.dart';

/// Register all hotel-related routes
Map<String, WidgetBuilder> hotelRoutes = {
  '/travel/hotels': (context) => const HotelListScreenEnhanced(),
  '/travel/hotel/details': (context) {
    final hotel = ModalRoute.of(context)!.settings.arguments as Hotel;
    return HotelDetailsScreenEnhanced(hotel: hotel);
  },
};
