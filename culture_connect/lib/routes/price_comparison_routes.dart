import 'package:flutter/material.dart';
import 'package:culture_connect/screens/travel/price_comparison_screen.dart';
import 'package:culture_connect/screens/travel/price_alerts_screen.dart';
import 'package:culture_connect/screens/travel/create_price_alert_screen.dart';
import 'package:culture_connect/models/travel/travel.dart';

/// Register all price comparison-related routes
Map<String, WidgetBuilder> priceComparisonRoutes = {
  '/travel/price-comparison': (context) {
    final travelService = ModalRoute.of(context)!.settings.arguments as TravelService;
    return PriceComparisonScreen(travelService: travelService);
  },
  '/travel/price-alerts': (context) => const PriceAlertsScreen(),
  '/travel/price-alert/create': (context) => const CreatePriceAlertScreen(),
};
