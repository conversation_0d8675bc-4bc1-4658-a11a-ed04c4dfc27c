// Flutter imports
import 'package:flutter/foundation.dart';

/// Processing status for visa applications
enum ProcessingStatus {
  notStarted,
  documentsSubmitted,
  underReview,
  additionalDocumentsRequired,
  interviewScheduled,
  interviewCompleted,
  decisionPending,
  approved,
  rejected,
  onHold,
}

/// Milestone status for processing timeline
enum MilestoneStatus {
  pending,
  inProgress,
  completed,
  delayed,
  skipped,
}

/// Biometric requirement types
enum BiometricType {
  fingerprints,
  photograph,
  iris,
  signature,
  all,
}

/// Interview requirement types
enum InterviewType {
  notRequired,
  required,
  conditional,
  groupInterview,
  videoInterview,
}

/// Health requirement types
enum HealthRequirementType {
  vaccination,
  medicalCertificate,
  healthInsurance,
  covidTest,
  quarantine,
}

/// Financial requirement types
enum FinancialRequirementType {
  bankStatement,
  proofOfFunds,
  sponsorshipLetter,
  employmentLetter,
  taxReturns,
}

/// Insurance requirement types
enum InsuranceRequirementType {
  travelInsurance,
  healthInsurance,
  emergencyMedical,
  repatriation,
  comprehensive,
}

/// Embassy or consulate contact information
@immutable
class EmbassyContactInfo {
  /// Embassy/consulate name
  final String name;

  /// Physical address
  final String address;

  /// City
  final String city;

  /// Country
  final String country;

  /// Phone number
  final String phoneNumber;

  /// Email address
  final String emailAddress;

  /// Website URL
  final String? websiteUrl;

  /// Operating hours
  final String operatingHours;

  /// Emergency contact number
  final String? emergencyContact;

  /// Appointment booking URL
  final String? appointmentUrl;

  /// Visa section contact
  final String? visaSectionContact;

  const EmbassyContactInfo({
    required this.name,
    required this.address,
    required this.city,
    required this.country,
    required this.phoneNumber,
    required this.emailAddress,
    this.websiteUrl,
    required this.operatingHours,
    this.emergencyContact,
    this.appointmentUrl,
    this.visaSectionContact,
  });

  factory EmbassyContactInfo.fromJson(Map<String, dynamic> json) {
    return EmbassyContactInfo(
      name: json['name'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      country: json['country'] as String,
      phoneNumber: json['phoneNumber'] as String,
      emailAddress: json['emailAddress'] as String,
      websiteUrl: json['websiteUrl'] as String?,
      operatingHours: json['operatingHours'] as String,
      emergencyContact: json['emergencyContact'] as String?,
      appointmentUrl: json['appointmentUrl'] as String?,
      visaSectionContact: json['visaSectionContact'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'city': city,
      'country': country,
      'phoneNumber': phoneNumber,
      'emailAddress': emailAddress,
      'websiteUrl': websiteUrl,
      'operatingHours': operatingHours,
      'emergencyContact': emergencyContact,
      'appointmentUrl': appointmentUrl,
      'visaSectionContact': visaSectionContact,
    };
  }
}

/// Document requirement with detailed specifications
@immutable
class DocumentRequirement {
  /// Document type identifier
  final String documentType;

  /// Display name for the document
  final String displayName;

  /// Detailed description of requirements
  final String description;

  /// Whether this document is mandatory
  final bool isMandatory;

  /// Accepted file formats
  final List<String> acceptedFormats;

  /// Maximum file size in MB
  final double maxFileSizeMB;

  /// Specific requirements (e.g., "Color copy", "Notarized")
  final List<String> specificRequirements;

  /// Sample document URL for reference
  final String? sampleDocumentUrl;

  /// Processing notes
  final String? processingNotes;

  /// Validity period requirements
  final String? validityRequirement;

  const DocumentRequirement({
    required this.documentType,
    required this.displayName,
    required this.description,
    required this.isMandatory,
    required this.acceptedFormats,
    required this.maxFileSizeMB,
    required this.specificRequirements,
    this.sampleDocumentUrl,
    this.processingNotes,
    this.validityRequirement,
  });

  factory DocumentRequirement.fromJson(Map<String, dynamic> json) {
    return DocumentRequirement(
      documentType: json['documentType'] as String,
      displayName: json['displayName'] as String,
      description: json['description'] as String,
      isMandatory: json['isMandatory'] as bool,
      acceptedFormats: (json['acceptedFormats'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      maxFileSizeMB: (json['maxFileSizeMB'] as num).toDouble(),
      specificRequirements: (json['specificRequirements'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      sampleDocumentUrl: json['sampleDocumentUrl'] as String?,
      processingNotes: json['processingNotes'] as String?,
      validityRequirement: json['validityRequirement'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'documentType': documentType,
      'displayName': displayName,
      'description': description,
      'isMandatory': isMandatory,
      'acceptedFormats': acceptedFormats,
      'maxFileSizeMB': maxFileSizeMB,
      'specificRequirements': specificRequirements,
      'sampleDocumentUrl': sampleDocumentUrl,
      'processingNotes': processingNotes,
      'validityRequirement': validityRequirement,
    };
  }
}

/// Travel advisory information
@immutable
class TravelAdvisoryInfo {
  /// Advisory level (1-4, where 4 is highest risk)
  final int advisoryLevel;

  /// Advisory title
  final String title;

  /// Detailed advisory message
  final String message;

  /// Last updated timestamp
  final DateTime lastUpdated;

  /// Source of the advisory
  final String source;

  /// Specific risks or concerns
  final List<String> risks;

  /// Recommended precautions
  final List<String> precautions;

  /// Emergency contact information
  final String? emergencyContact;

  /// Advisory URL for more information
  final String? advisoryUrl;

  const TravelAdvisoryInfo({
    required this.advisoryLevel,
    required this.title,
    required this.message,
    required this.lastUpdated,
    required this.source,
    required this.risks,
    required this.precautions,
    this.emergencyContact,
    this.advisoryUrl,
  });

  factory TravelAdvisoryInfo.fromJson(Map<String, dynamic> json) {
    return TravelAdvisoryInfo(
      advisoryLevel: json['advisoryLevel'] as int,
      title: json['title'] as String,
      message: json['message'] as String,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      source: json['source'] as String,
      risks: (json['risks'] as List<dynamic>).map((e) => e as String).toList(),
      precautions: (json['precautions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      emergencyContact: json['emergencyContact'] as String?,
      advisoryUrl: json['advisoryUrl'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'advisoryLevel': advisoryLevel,
      'title': title,
      'message': message,
      'lastUpdated': lastUpdated.toIso8601String(),
      'source': source,
      'risks': risks,
      'precautions': precautions,
      'emergencyContact': emergencyContact,
      'advisoryUrl': advisoryUrl,
    };
  }

  /// Get advisory level color for UI display
  String get advisoryLevelColor {
    switch (advisoryLevel) {
      case 1:
        return 'green'; // Exercise normal precautions
      case 2:
        return 'yellow'; // Exercise increased caution
      case 3:
        return 'orange'; // Reconsider travel
      case 4:
        return 'red'; // Do not travel
      default:
        return 'gray';
    }
  }

  /// Get advisory level description
  String get advisoryLevelDescription {
    switch (advisoryLevel) {
      case 1:
        return 'Exercise normal precautions';
      case 2:
        return 'Exercise increased caution';
      case 3:
        return 'Reconsider travel';
      case 4:
        return 'Do not travel';
      default:
        return 'Unknown advisory level';
    }
  }
}

/// Appointment booking requirements
@immutable
class AppointmentRequirement {
  /// Whether appointment is required
  final bool isRequired;

  /// Advance booking period in days
  final int advanceBookingDays;

  /// Available appointment types
  final List<String> appointmentTypes;

  /// Booking instructions
  final String bookingInstructions;

  /// Online booking URL
  final String? onlineBookingUrl;

  /// Phone booking number
  final String? phoneBookingNumber;

  /// Required documents for appointment
  final List<String> requiredDocuments;

  /// Appointment duration in minutes
  final int appointmentDurationMinutes;

  /// Cancellation policy
  final String? cancellationPolicy;

  /// Rescheduling policy
  final String? reschedulingPolicy;

  const AppointmentRequirement({
    required this.isRequired,
    required this.advanceBookingDays,
    required this.appointmentTypes,
    required this.bookingInstructions,
    this.onlineBookingUrl,
    this.phoneBookingNumber,
    required this.requiredDocuments,
    required this.appointmentDurationMinutes,
    this.cancellationPolicy,
    this.reschedulingPolicy,
  });

  factory AppointmentRequirement.fromJson(Map<String, dynamic> json) {
    return AppointmentRequirement(
      isRequired: json['isRequired'] as bool,
      advanceBookingDays: json['advanceBookingDays'] as int,
      appointmentTypes: (json['appointmentTypes'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      bookingInstructions: json['bookingInstructions'] as String,
      onlineBookingUrl: json['onlineBookingUrl'] as String?,
      phoneBookingNumber: json['phoneBookingNumber'] as String?,
      requiredDocuments: (json['requiredDocuments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      appointmentDurationMinutes: json['appointmentDurationMinutes'] as int,
      cancellationPolicy: json['cancellationPolicy'] as String?,
      reschedulingPolicy: json['reschedulingPolicy'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isRequired': isRequired,
      'advanceBookingDays': advanceBookingDays,
      'appointmentTypes': appointmentTypes,
      'bookingInstructions': bookingInstructions,
      'onlineBookingUrl': onlineBookingUrl,
      'phoneBookingNumber': phoneBookingNumber,
      'requiredDocuments': requiredDocuments,
      'appointmentDurationMinutes': appointmentDurationMinutes,
      'cancellationPolicy': cancellationPolicy,
      'reschedulingPolicy': reschedulingPolicy,
    };
  }
}

/// Processing timeline with milestones
@immutable
class ProcessingTimeline {
  /// Total estimated processing days
  final int totalProcessingDays;

  /// Processing milestones
  final List<ProcessingMilestone> milestones;

  /// Current milestone index
  final int currentMilestoneIndex;

  /// Processing status
  final ProcessingStatus status;

  /// Last updated timestamp
  final DateTime lastUpdated;

  /// Estimated completion date
  final DateTime? estimatedCompletionDate;

  const ProcessingTimeline({
    required this.totalProcessingDays,
    required this.milestones,
    required this.currentMilestoneIndex,
    required this.status,
    required this.lastUpdated,
    this.estimatedCompletionDate,
  });

  factory ProcessingTimeline.fromJson(Map<String, dynamic> json) {
    return ProcessingTimeline(
      totalProcessingDays: json['totalProcessingDays'] as int,
      milestones: (json['milestones'] as List<dynamic>)
          .map((e) => ProcessingMilestone.fromJson(e as Map<String, dynamic>))
          .toList(),
      currentMilestoneIndex: json['currentMilestoneIndex'] as int,
      status: ProcessingStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ProcessingStatus.notStarted,
      ),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      estimatedCompletionDate: json['estimatedCompletionDate'] != null
          ? DateTime.parse(json['estimatedCompletionDate'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalProcessingDays': totalProcessingDays,
      'milestones': milestones.map((e) => e.toJson()).toList(),
      'currentMilestoneIndex': currentMilestoneIndex,
      'status': status.name,
      'lastUpdated': lastUpdated.toIso8601String(),
      'estimatedCompletionDate': estimatedCompletionDate?.toIso8601String(),
    };
  }

  /// Get current milestone
  ProcessingMilestone? get currentMilestone {
    if (currentMilestoneIndex >= 0 &&
        currentMilestoneIndex < milestones.length) {
      return milestones[currentMilestoneIndex];
    }
    return null;
  }

  /// Get progress percentage
  double get progressPercentage {
    if (milestones.isEmpty) return 0.0;
    return (currentMilestoneIndex + 1) / milestones.length;
  }
}

/// Processing milestone
@immutable
class ProcessingMilestone {
  /// Milestone name
  final String name;

  /// Milestone description
  final String description;

  /// Expected duration in days
  final int expectedDurationDays;

  /// Milestone status
  final MilestoneStatus status;

  /// Completion date
  final DateTime? completionDate;

  /// Notes or additional information
  final String? notes;

  const ProcessingMilestone({
    required this.name,
    required this.description,
    required this.expectedDurationDays,
    required this.status,
    this.completionDate,
    this.notes,
  });

  factory ProcessingMilestone.fromJson(Map<String, dynamic> json) {
    return ProcessingMilestone(
      name: json['name'] as String,
      description: json['description'] as String,
      expectedDurationDays: json['expectedDurationDays'] as int,
      status: MilestoneStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MilestoneStatus.pending,
      ),
      completionDate: json['completionDate'] != null
          ? DateTime.parse(json['completionDate'] as String)
          : null,
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'expectedDurationDays': expectedDurationDays,
      'status': status.name,
      'completionDate': completionDate?.toIso8601String(),
      'notes': notes,
    };
  }
}

/// Emergency contact information
@immutable
class EmergencyContactInfo {
  /// Emergency hotline number
  final String hotlineNumber;

  /// 24/7 availability
  final bool isAvailable24x7;

  /// Emergency email
  final String? emergencyEmail;

  /// Emergency services description
  final String servicesDescription;

  /// Response time in hours
  final int responseTimeHours;

  /// Additional emergency contacts
  final List<String> additionalContacts;

  const EmergencyContactInfo({
    required this.hotlineNumber,
    required this.isAvailable24x7,
    this.emergencyEmail,
    required this.servicesDescription,
    required this.responseTimeHours,
    required this.additionalContacts,
  });

  factory EmergencyContactInfo.fromJson(Map<String, dynamic> json) {
    return EmergencyContactInfo(
      hotlineNumber: json['hotlineNumber'] as String,
      isAvailable24x7: json['isAvailable24x7'] as bool,
      emergencyEmail: json['emergencyEmail'] as String?,
      servicesDescription: json['servicesDescription'] as String,
      responseTimeHours: json['responseTimeHours'] as int,
      additionalContacts: (json['additionalContacts'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hotlineNumber': hotlineNumber,
      'isAvailable24x7': isAvailable24x7,
      'emergencyEmail': emergencyEmail,
      'servicesDescription': servicesDescription,
      'responseTimeHours': responseTimeHours,
      'additionalContacts': additionalContacts,
    };
  }
}

/// Biometric requirement information
@immutable
class BiometricRequirement {
  /// Type of biometric required
  final BiometricType type;

  /// Whether biometric is mandatory
  final bool isMandatory;

  /// Description of biometric requirements
  final String description;

  /// Location where biometric can be taken
  final String location;

  /// Cost for biometric service
  final double? cost;

  /// Validity period in months
  final int validityMonths;

  const BiometricRequirement({
    required this.type,
    required this.isMandatory,
    required this.description,
    required this.location,
    this.cost,
    required this.validityMonths,
  });

  factory BiometricRequirement.fromJson(Map<String, dynamic> json) {
    return BiometricRequirement(
      type: BiometricType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => BiometricType.all,
      ),
      isMandatory: json['isMandatory'] as bool,
      description: json['description'] as String,
      location: json['location'] as String,
      cost: (json['cost'] as num?)?.toDouble(),
      validityMonths: json['validityMonths'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'isMandatory': isMandatory,
      'description': description,
      'location': location,
      'cost': cost,
      'validityMonths': validityMonths,
    };
  }
}

/// Interview requirement information
@immutable
class InterviewRequirement {
  /// Type of interview required
  final InterviewType type;

  /// Whether interview is mandatory
  final bool isMandatory;

  /// Interview description
  final String description;

  /// Expected duration in minutes
  final int durationMinutes;

  /// Languages available for interview
  final List<String> availableLanguages;

  /// Preparation guidelines
  final List<String> preparationGuidelines;

  /// Required documents for interview
  final List<String> requiredDocuments;

  const InterviewRequirement({
    required this.type,
    required this.isMandatory,
    required this.description,
    required this.durationMinutes,
    required this.availableLanguages,
    required this.preparationGuidelines,
    required this.requiredDocuments,
  });

  factory InterviewRequirement.fromJson(Map<String, dynamic> json) {
    return InterviewRequirement(
      type: InterviewType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => InterviewType.notRequired,
      ),
      isMandatory: json['isMandatory'] as bool,
      description: json['description'] as String,
      durationMinutes: json['durationMinutes'] as int,
      availableLanguages: (json['availableLanguages'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      preparationGuidelines: (json['preparationGuidelines'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      requiredDocuments: (json['requiredDocuments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'isMandatory': isMandatory,
      'description': description,
      'durationMinutes': durationMinutes,
      'availableLanguages': availableLanguages,
      'preparationGuidelines': preparationGuidelines,
      'requiredDocuments': requiredDocuments,
    };
  }
}

/// Health requirement information
@immutable
class HealthRequirement {
  /// Type of health requirement
  final HealthRequirementType type;

  /// Requirement name
  final String name;

  /// Detailed description
  final String description;

  /// Whether requirement is mandatory
  final bool isMandatory;

  /// Validity period in months
  final int? validityMonths;

  /// Accepted certificates or documents
  final List<String> acceptedDocuments;

  /// Additional notes
  final String? notes;

  const HealthRequirement({
    required this.type,
    required this.name,
    required this.description,
    required this.isMandatory,
    this.validityMonths,
    required this.acceptedDocuments,
    this.notes,
  });

  factory HealthRequirement.fromJson(Map<String, dynamic> json) {
    return HealthRequirement(
      type: HealthRequirementType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => HealthRequirementType.vaccination,
      ),
      name: json['name'] as String,
      description: json['description'] as String,
      isMandatory: json['isMandatory'] as bool,
      validityMonths: json['validityMonths'] as int?,
      acceptedDocuments: (json['acceptedDocuments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'name': name,
      'description': description,
      'isMandatory': isMandatory,
      'validityMonths': validityMonths,
      'acceptedDocuments': acceptedDocuments,
      'notes': notes,
    };
  }
}

/// Financial requirement information
@immutable
class FinancialRequirement {
  /// Type of financial requirement
  final FinancialRequirementType type;

  /// Minimum amount required
  final double minimumAmount;

  /// Currency for the amount
  final String currency;

  /// Requirement description
  final String description;

  /// Whether requirement is mandatory
  final bool isMandatory;

  /// Validity period in months
  final int validityMonths;

  /// Accepted documents
  final List<String> acceptedDocuments;

  /// Additional requirements
  final List<String> additionalRequirements;

  const FinancialRequirement({
    required this.type,
    required this.minimumAmount,
    required this.currency,
    required this.description,
    required this.isMandatory,
    required this.validityMonths,
    required this.acceptedDocuments,
    required this.additionalRequirements,
  });

  factory FinancialRequirement.fromJson(Map<String, dynamic> json) {
    return FinancialRequirement(
      type: FinancialRequirementType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => FinancialRequirementType.bankStatement,
      ),
      minimumAmount: (json['minimumAmount'] as num).toDouble(),
      currency: json['currency'] as String,
      description: json['description'] as String,
      isMandatory: json['isMandatory'] as bool,
      validityMonths: json['validityMonths'] as int,
      acceptedDocuments: (json['acceptedDocuments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      additionalRequirements: (json['additionalRequirements'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'minimumAmount': minimumAmount,
      'currency': currency,
      'description': description,
      'isMandatory': isMandatory,
      'validityMonths': validityMonths,
      'acceptedDocuments': acceptedDocuments,
      'additionalRequirements': additionalRequirements,
    };
  }
}

/// Insurance requirement information
@immutable
class InsuranceRequirement {
  /// Type of insurance required
  final InsuranceRequirementType type;

  /// Minimum coverage amount
  final double minimumCoverage;

  /// Currency for coverage amount
  final String currency;

  /// Requirement description
  final String description;

  /// Whether requirement is mandatory
  final bool isMandatory;

  /// Coverage areas required
  final List<String> coverageAreas;

  /// Accepted insurance providers
  final List<String> acceptedProviders;

  /// Validity period requirements
  final String validityRequirement;

  const InsuranceRequirement({
    required this.type,
    required this.minimumCoverage,
    required this.currency,
    required this.description,
    required this.isMandatory,
    required this.coverageAreas,
    required this.acceptedProviders,
    required this.validityRequirement,
  });

  factory InsuranceRequirement.fromJson(Map<String, dynamic> json) {
    return InsuranceRequirement(
      type: InsuranceRequirementType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => InsuranceRequirementType.travelInsurance,
      ),
      minimumCoverage: (json['minimumCoverage'] as num).toDouble(),
      currency: json['currency'] as String,
      description: json['description'] as String,
      isMandatory: json['isMandatory'] as bool,
      coverageAreas: (json['coverageAreas'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      acceptedProviders: (json['acceptedProviders'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      validityRequirement: json['validityRequirement'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'minimumCoverage': minimumCoverage,
      'currency': currency,
      'description': description,
      'isMandatory': isMandatory,
      'coverageAreas': coverageAreas,
      'acceptedProviders': acceptedProviders,
      'validityRequirement': validityRequirement,
    };
  }
}
