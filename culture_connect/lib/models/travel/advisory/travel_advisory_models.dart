import 'package:cloud_firestore/cloud_firestore.dart';

/// Enum for travel advisory severity levels
enum AdvisorySeverity {
  low,
  moderate,
  high,
  critical,
}

/// Enum for travel advisory types
enum AdvisoryType {
  safety,
  health,
  weather,
  political,
  natural,
  transportation,
  visa,
  currency,
  cultural,
  general,
}

/// Enum for travel advisory status
enum AdvisoryStatus {
  active,
  expired,
  updated,
  cancelled,
}

/// Extension for advisory severity
extension AdvisorySeverityExtension on AdvisorySeverity {
  String get displayName {
    switch (this) {
      case AdvisorySeverity.low:
        return 'Low Risk';
      case AdvisorySeverity.moderate:
        return 'Moderate Risk';
      case AdvisorySeverity.high:
        return 'High Risk';
      case AdvisorySeverity.critical:
        return 'Critical Risk';
    }
  }

  String get description {
    switch (this) {
      case AdvisorySeverity.low:
        return 'Exercise normal precautions';
      case AdvisorySeverity.moderate:
        return 'Exercise increased caution';
      case AdvisorySeverity.high:
        return 'Reconsider travel';
      case AdvisorySeverity.critical:
        return 'Do not travel';
    }
  }

  int get colorValue {
    switch (this) {
      case AdvisorySeverity.low:
        return 0xFF4CAF50; // Green
      case AdvisorySeverity.moderate:
        return 0xFFFF9800; // Orange
      case AdvisorySeverity.high:
        return 0xFFFF5722; // Deep Orange
      case AdvisorySeverity.critical:
        return 0xFFF44336; // Red
    }
  }
}

/// Extension for advisory type
extension AdvisoryTypeExtension on AdvisoryType {
  String get displayName {
    switch (this) {
      case AdvisoryType.safety:
        return 'Safety & Security';
      case AdvisoryType.health:
        return 'Health';
      case AdvisoryType.weather:
        return 'Weather';
      case AdvisoryType.political:
        return 'Political';
      case AdvisoryType.natural:
        return 'Natural Disasters';
      case AdvisoryType.transportation:
        return 'Transportation';
      case AdvisoryType.visa:
        return 'Visa & Documents';
      case AdvisoryType.currency:
        return 'Currency';
      case AdvisoryType.cultural:
        return 'Cultural';
      case AdvisoryType.general:
        return 'General';
    }
  }

  String get iconName {
    switch (this) {
      case AdvisoryType.safety:
        return 'security';
      case AdvisoryType.health:
        return 'local_hospital';
      case AdvisoryType.weather:
        return 'wb_sunny';
      case AdvisoryType.political:
        return 'account_balance';
      case AdvisoryType.natural:
        return 'warning';
      case AdvisoryType.transportation:
        return 'directions_bus';
      case AdvisoryType.visa:
        return 'description';
      case AdvisoryType.currency:
        return 'attach_money';
      case AdvisoryType.cultural:
        return 'language';
      case AdvisoryType.general:
        return 'info';
    }
  }
}

/// Model for travel advisory
class TravelAdvisory {
  final String id;
  final String countryCode;
  final String countryName;
  final AdvisoryType type;
  final AdvisorySeverity severity;
  final AdvisoryStatus status;
  final String title;
  final String description;
  final String? detailedInfo;
  final List<String> affectedRegions;
  final List<String> recommendedActions;
  final DateTime issuedAt;
  final DateTime? expiresAt;
  final DateTime? lastUpdated;
  final String source;
  final String? sourceUrl;
  final Map<String, dynamic> metadata;

  const TravelAdvisory({
    required this.id,
    required this.countryCode,
    required this.countryName,
    required this.type,
    required this.severity,
    required this.status,
    required this.title,
    required this.description,
    this.detailedInfo,
    required this.affectedRegions,
    required this.recommendedActions,
    required this.issuedAt,
    this.expiresAt,
    this.lastUpdated,
    required this.source,
    this.sourceUrl,
    required this.metadata,
  });

  /// Create from JSON
  factory TravelAdvisory.fromJson(Map<String, dynamic> json) {
    return TravelAdvisory(
      id: json['id'] as String,
      countryCode: json['countryCode'] as String,
      countryName: json['countryName'] as String,
      type: AdvisoryType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => AdvisoryType.general,
      ),
      severity: AdvisorySeverity.values.firstWhere(
        (e) => e.toString().split('.').last == json['severity'],
        orElse: () => AdvisorySeverity.low,
      ),
      status: AdvisoryStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => AdvisoryStatus.active,
      ),
      title: json['title'] as String,
      description: json['description'] as String,
      detailedInfo: json['detailedInfo'] as String?,
      affectedRegions: List<String>.from(json['affectedRegions'] as List),
      recommendedActions: List<String>.from(json['recommendedActions'] as List),
      issuedAt: (json['issuedAt'] as Timestamp).toDate(),
      expiresAt: json['expiresAt'] != null
          ? (json['expiresAt'] as Timestamp).toDate()
          : null,
      lastUpdated: json['lastUpdated'] != null
          ? (json['lastUpdated'] as Timestamp).toDate()
          : null,
      source: json['source'] as String,
      sourceUrl: json['sourceUrl'] as String?,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'countryCode': countryCode,
      'countryName': countryName,
      'type': type.toString().split('.').last,
      'severity': severity.toString().split('.').last,
      'status': status.toString().split('.').last,
      'title': title,
      'description': description,
      'detailedInfo': detailedInfo,
      'affectedRegions': affectedRegions,
      'recommendedActions': recommendedActions,
      'issuedAt': Timestamp.fromDate(issuedAt),
      'expiresAt': expiresAt != null ? Timestamp.fromDate(expiresAt!) : null,
      'lastUpdated':
          lastUpdated != null ? Timestamp.fromDate(lastUpdated!) : null,
      'source': source,
      'sourceUrl': sourceUrl,
      'metadata': metadata,
    };
  }

  /// Copy with modifications
  TravelAdvisory copyWith({
    String? id,
    String? countryCode,
    String? countryName,
    AdvisoryType? type,
    AdvisorySeverity? severity,
    AdvisoryStatus? status,
    String? title,
    String? description,
    String? detailedInfo,
    List<String>? affectedRegions,
    List<String>? recommendedActions,
    DateTime? issuedAt,
    DateTime? expiresAt,
    DateTime? lastUpdated,
    String? source,
    String? sourceUrl,
    Map<String, dynamic>? metadata,
  }) {
    return TravelAdvisory(
      id: id ?? this.id,
      countryCode: countryCode ?? this.countryCode,
      countryName: countryName ?? this.countryName,
      type: type ?? this.type,
      severity: severity ?? this.severity,
      status: status ?? this.status,
      title: title ?? this.title,
      description: description ?? this.description,
      detailedInfo: detailedInfo ?? this.detailedInfo,
      affectedRegions: affectedRegions ?? this.affectedRegions,
      recommendedActions: recommendedActions ?? this.recommendedActions,
      issuedAt: issuedAt ?? this.issuedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      source: source ?? this.source,
      sourceUrl: sourceUrl ?? this.sourceUrl,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if advisory is currently active
  bool get isActive {
    if (status != AdvisoryStatus.active) return false;
    if (expiresAt != null && DateTime.now().isAfter(expiresAt!)) return false;
    return true;
  }

  /// Check if advisory is critical
  bool get isCritical => severity == AdvisorySeverity.critical;

  /// Check if advisory affects specific region
  bool affectsRegion(String region) {
    return affectedRegions.isEmpty ||
        affectedRegions
            .any((r) => r.toLowerCase().contains(region.toLowerCase()));
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TravelAdvisory &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() =>
      'TravelAdvisory(id: $id, country: $countryName, type: $type, severity: $severity)';
}

/// Model for document requirement changes
class DocumentRequirementChange {
  final String id;
  final String countryCode;
  final String countryName;
  final String documentType;
  final String changeType; // 'added', 'removed', 'modified'
  final String description;
  final String? previousRequirement;
  final String? newRequirement;
  final DateTime effectiveDate;
  final DateTime announcedAt;
  final String source;
  final bool isUrgent;
  final Map<String, dynamic> metadata;

  const DocumentRequirementChange({
    required this.id,
    required this.countryCode,
    required this.countryName,
    required this.documentType,
    required this.changeType,
    required this.description,
    this.previousRequirement,
    this.newRequirement,
    required this.effectiveDate,
    required this.announcedAt,
    required this.source,
    required this.isUrgent,
    required this.metadata,
  });

  /// Create from JSON
  factory DocumentRequirementChange.fromJson(Map<String, dynamic> json) {
    return DocumentRequirementChange(
      id: json['id'] as String,
      countryCode: json['countryCode'] as String,
      countryName: json['countryName'] as String,
      documentType: json['documentType'] as String,
      changeType: json['changeType'] as String,
      description: json['description'] as String,
      previousRequirement: json['previousRequirement'] as String?,
      newRequirement: json['newRequirement'] as String?,
      effectiveDate: (json['effectiveDate'] as Timestamp).toDate(),
      announcedAt: (json['announcedAt'] as Timestamp).toDate(),
      source: json['source'] as String,
      isUrgent: json['isUrgent'] as bool,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'countryCode': countryCode,
      'countryName': countryName,
      'documentType': documentType,
      'changeType': changeType,
      'description': description,
      'previousRequirement': previousRequirement,
      'newRequirement': newRequirement,
      'effectiveDate': Timestamp.fromDate(effectiveDate),
      'announcedAt': Timestamp.fromDate(announcedAt),
      'source': source,
      'isUrgent': isUrgent,
      'metadata': metadata,
    };
  }

  /// Check if change is effective now
  bool get isEffective => DateTime.now().isAfter(effectiveDate);

  /// Check if change is upcoming (within 30 days)
  bool get isUpcoming {
    final now = DateTime.now();
    final thirtyDaysFromNow = now.add(const Duration(days: 30));
    return effectiveDate.isAfter(now) &&
        effectiveDate.isBefore(thirtyDaysFromNow);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DocumentRequirementChange &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Model for advisory subscription
class AdvisorySubscription {
  final String id;
  final String userId;
  final List<String> countryCodes;
  final List<AdvisoryType> advisoryTypes;
  final List<AdvisorySeverity> severityLevels;
  final bool emailNotifications;
  final bool pushNotifications;
  final bool smsNotifications;
  final DateTime createdAt;
  final DateTime? lastNotified;
  final bool isActive;
  final Map<String, dynamic> preferences;

  const AdvisorySubscription({
    required this.id,
    required this.userId,
    required this.countryCodes,
    required this.advisoryTypes,
    required this.severityLevels,
    required this.emailNotifications,
    required this.pushNotifications,
    required this.smsNotifications,
    required this.createdAt,
    this.lastNotified,
    required this.isActive,
    required this.preferences,
  });

  /// Create from JSON
  factory AdvisorySubscription.fromJson(Map<String, dynamic> json) {
    return AdvisorySubscription(
      id: json['id'] as String,
      userId: json['userId'] as String,
      countryCodes: List<String>.from(json['countryCodes'] as List),
      advisoryTypes: (json['advisoryTypes'] as List)
          .map((e) => AdvisoryType.values.firstWhere(
                (type) => type.toString().split('.').last == e,
                orElse: () => AdvisoryType.general,
              ))
          .toList(),
      severityLevels: (json['severityLevels'] as List)
          .map((e) => AdvisorySeverity.values.firstWhere(
                (severity) => severity.toString().split('.').last == e,
                orElse: () => AdvisorySeverity.low,
              ))
          .toList(),
      emailNotifications: json['emailNotifications'] as bool,
      pushNotifications: json['pushNotifications'] as bool,
      smsNotifications: json['smsNotifications'] as bool,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      lastNotified: json['lastNotified'] != null
          ? (json['lastNotified'] as Timestamp).toDate()
          : null,
      isActive: json['isActive'] as bool,
      preferences: Map<String, dynamic>.from(json['preferences'] as Map),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'countryCodes': countryCodes,
      'advisoryTypes':
          advisoryTypes.map((e) => e.toString().split('.').last).toList(),
      'severityLevels':
          severityLevels.map((e) => e.toString().split('.').last).toList(),
      'emailNotifications': emailNotifications,
      'pushNotifications': pushNotifications,
      'smsNotifications': smsNotifications,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastNotified':
          lastNotified != null ? Timestamp.fromDate(lastNotified!) : null,
      'isActive': isActive,
      'preferences': preferences,
    };
  }

  /// Check if subscription matches advisory
  bool matchesAdvisory(TravelAdvisory advisory) {
    if (!isActive) return false;
    if (!countryCodes.contains(advisory.countryCode)) return false;
    if (advisoryTypes.isNotEmpty && !advisoryTypes.contains(advisory.type)) {
      return false;
    }
    if (severityLevels.isNotEmpty &&
        !severityLevels.contains(advisory.severity)) {
      return false;
    }
    return true;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AdvisorySubscription &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
