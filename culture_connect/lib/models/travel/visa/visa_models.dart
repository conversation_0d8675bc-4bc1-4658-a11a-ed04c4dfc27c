import 'package:flutter/foundation.dart';

/// Status of a visa application
enum VisaApplicationStatus {
  draft,
  submitted,
  underReview,
  approved,
  rejected,
  cancelled,
}

/// Status of a visa service booking
enum VisaServiceBookingStatus {
  pending,
  confirmed,
  inProgress,
  completed,
  cancelled,
  refunded,
}

/// Status of visa payment
enum VisaPaymentStatus {
  pending,
  processing,
  paid,
  completed,
  failed,
  refunded,
}

/// Represents a visa application
@immutable
class VisaApplication {
  /// Unique identifier for the application
  final String id;

  /// User ID who created the application
  final String userId;

  /// Destination country for the visa
  final String destinationCountry;

  /// Type of visa being applied for
  final String visaType;

  /// Embassy or consulate handling the application
  final String embassy;

  /// Base fee for the visa
  final double baseFee;

  /// Service fee charged by the provider
  final double serviceFee;

  /// Processing fee for the application
  final double processingFee;

  /// Total fees for the application
  final double totalFees;

  /// Currency for the fees
  final String currency;

  /// Current status of the application
  final VisaApplicationStatus status;

  /// Payment status
  final VisaPaymentStatus paymentStatus;

  /// Payment reference if paid
  final String? paymentReference;

  /// When payment was made
  final DateTime? paidAt;

  /// When the application was created
  final DateTime createdAt;

  /// When the application was last updated
  final DateTime updatedAt;

  /// Creates a new visa application
  const VisaApplication({
    required this.id,
    required this.userId,
    required this.destinationCountry,
    required this.visaType,
    required this.embassy,
    required this.baseFee,
    required this.serviceFee,
    required this.processingFee,
    required this.totalFees,
    required this.currency,
    required this.status,
    required this.paymentStatus,
    this.paymentReference,
    this.paidAt,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a copy of this application with the given fields replaced
  VisaApplication copyWith({
    String? id,
    String? userId,
    String? destinationCountry,
    String? visaType,
    String? embassy,
    double? baseFee,
    double? serviceFee,
    double? processingFee,
    double? totalFees,
    String? currency,
    VisaApplicationStatus? status,
    VisaPaymentStatus? paymentStatus,
    String? paymentReference,
    DateTime? paidAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VisaApplication(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      destinationCountry: destinationCountry ?? this.destinationCountry,
      visaType: visaType ?? this.visaType,
      embassy: embassy ?? this.embassy,
      baseFee: baseFee ?? this.baseFee,
      serviceFee: serviceFee ?? this.serviceFee,
      processingFee: processingFee ?? this.processingFee,
      totalFees: totalFees ?? this.totalFees,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentReference: paymentReference ?? this.paymentReference,
      paidAt: paidAt ?? this.paidAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Represents a visa service provider
@immutable
class VisaServiceProvider {
  /// Unique identifier for the provider
  final String id;

  /// Name of the service provider
  final String name;

  /// Description of services offered
  final String description;

  /// Countries the provider serves
  final List<String> supportedCountries;

  /// Services offered by the provider
  final List<String> services;

  /// Average rating of the provider
  final double rating;

  /// Number of reviews
  final int reviewCount;

  /// Whether the provider is verified
  final bool isVerified;

  /// Contact information
  final String email;
  final String? phone;
  final String? website;

  /// Creates a new visa service provider
  const VisaServiceProvider({
    required this.id,
    required this.name,
    required this.description,
    required this.supportedCountries,
    required this.services,
    required this.rating,
    required this.reviewCount,
    required this.isVerified,
    required this.email,
    this.phone,
    this.website,
  });

  /// Creates a copy of this provider with the given fields replaced
  VisaServiceProvider copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? supportedCountries,
    List<String>? services,
    double? rating,
    int? reviewCount,
    bool? isVerified,
    String? email,
    String? phone,
    String? website,
  }) {
    return VisaServiceProvider(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      supportedCountries: supportedCountries ?? this.supportedCountries,
      services: services ?? this.services,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isVerified: isVerified ?? this.isVerified,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      website: website ?? this.website,
    );
  }
}

/// Represents a booking with a visa service provider
@immutable
class VisaServiceBooking {
  /// Unique identifier for the booking
  final String id;

  /// User ID who made the booking
  final String userId;

  /// Service provider ID
  final String providerId;

  /// Name of the service being booked
  final String serviceName;

  /// Date of the booking
  final DateTime bookingDate;

  /// Total amount for the service
  final double totalAmount;

  /// Currency for the amount
  final String currency;

  /// Current status of the booking
  final VisaServiceBookingStatus status;

  /// Payment reference if paid
  final String? paymentReference;

  /// When payment was made
  final DateTime? paidAt;

  /// When the booking was created
  final DateTime createdAt;

  /// When the booking was last updated
  final DateTime updatedAt;

  /// Creates a new visa service booking
  const VisaServiceBooking({
    required this.id,
    required this.userId,
    required this.providerId,
    required this.serviceName,
    required this.bookingDate,
    required this.totalAmount,
    required this.currency,
    required this.status,
    this.paymentReference,
    this.paidAt,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a copy of this booking with the given fields replaced
  VisaServiceBooking copyWith({
    String? id,
    String? userId,
    String? providerId,
    String? serviceName,
    DateTime? bookingDate,
    double? totalAmount,
    String? currency,
    VisaServiceBookingStatus? status,
    String? paymentReference,
    DateTime? paidAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VisaServiceBooking(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      providerId: providerId ?? this.providerId,
      serviceName: serviceName ?? this.serviceName,
      bookingDate: bookingDate ?? this.bookingDate,
      totalAmount: totalAmount ?? this.totalAmount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      paymentReference: paymentReference ?? this.paymentReference,
      paidAt: paidAt ?? this.paidAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Represents a document verification service
@immutable
class DocumentVerificationService {
  /// Unique identifier for the service
  final String id;

  /// Name of the verification service
  final String name;

  /// Description of the service
  final String description;

  /// Price for the service
  final double price;

  /// Currency for the price
  final String currency;

  /// Types of documents supported
  final List<String> supportedDocuments;

  /// Processing time in business days
  final int processingDays;

  /// Whether the service is available
  final bool isAvailable;

  /// Creates a new document verification service
  const DocumentVerificationService({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    required this.supportedDocuments,
    required this.processingDays,
    required this.isAvailable,
  });

  /// Creates a copy of this service with the given fields replaced
  DocumentVerificationService copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? currency,
    List<String>? supportedDocuments,
    int? processingDays,
    bool? isAvailable,
  }) {
    return DocumentVerificationService(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      supportedDocuments: supportedDocuments ?? this.supportedDocuments,
      processingDays: processingDays ?? this.processingDays,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }
}
