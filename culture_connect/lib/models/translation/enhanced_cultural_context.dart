import 'package:culture_connect/models/translation/cultural_context_model.dart';

/// Enhanced cultural context with regional variations and cultural adaptation
class EnhancedCulturalContext {
  /// Unique identifier for the cultural context
  final String id;

  /// Source language code
  final String sourceLanguage;

  /// Target language code
  final String targetLanguage;

  /// Source region/country code
  final String? sourceRegion;

  /// Target region/country code
  final String? targetRegion;

  /// Original text being analyzed
  final String originalText;

  /// Translated text
  final String translatedText;

  /// Cultural context notes
  final List<CulturalContextNote> contextNotes;

  /// Regional variations and alternatives
  final List<RegionalVariation> regionalVariations;

  /// Cultural adaptation suggestions
  final List<CulturalAdaptation> adaptations;

  /// Formality level analysis
  final FormalityAnalysis? formalityAnalysis;

  /// Cultural sensitivity warnings
  final List<CulturalSensitivityWarning> sensitivityWarnings;

  /// Local customs and etiquette notes
  final List<LocalCustomNote> customNotes;

  /// Historical and cultural background information
  final CulturalBackground? background;

  /// Confidence score for cultural analysis (0.0 to 1.0)
  final double confidenceScore;

  /// Whether this context has been verified by cultural experts
  final bool isExpertVerified;

  /// Timestamp when context was generated
  final DateTime createdAt;

  /// Last updated timestamp
  final DateTime updatedAt;

  /// User feedback on cultural context accuracy
  final List<CulturalContextFeedback> feedback;

  const EnhancedCulturalContext({
    required this.id,
    required this.sourceLanguage,
    required this.targetLanguage,
    this.sourceRegion,
    this.targetRegion,
    required this.originalText,
    required this.translatedText,
    this.contextNotes = const [],
    this.regionalVariations = const [],
    this.adaptations = const [],
    this.formalityAnalysis,
    this.sensitivityWarnings = const [],
    this.customNotes = const [],
    this.background,
    required this.confidenceScore,
    this.isExpertVerified = false,
    required this.createdAt,
    required this.updatedAt,
    this.feedback = const [],
  });

  /// Create a copy with updated fields
  EnhancedCulturalContext copyWith({
    String? id,
    String? sourceLanguage,
    String? targetLanguage,
    String? sourceRegion,
    String? targetRegion,
    String? originalText,
    String? translatedText,
    List<CulturalContextNote>? contextNotes,
    List<RegionalVariation>? regionalVariations,
    List<CulturalAdaptation>? adaptations,
    FormalityAnalysis? formalityAnalysis,
    List<CulturalSensitivityWarning>? sensitivityWarnings,
    List<LocalCustomNote>? customNotes,
    CulturalBackground? background,
    double? confidenceScore,
    bool? isExpertVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<CulturalContextFeedback>? feedback,
  }) {
    return EnhancedCulturalContext(
      id: id ?? this.id,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      sourceRegion: sourceRegion ?? this.sourceRegion,
      targetRegion: targetRegion ?? this.targetRegion,
      originalText: originalText ?? this.originalText,
      translatedText: translatedText ?? this.translatedText,
      contextNotes: contextNotes ?? this.contextNotes,
      regionalVariations: regionalVariations ?? this.regionalVariations,
      adaptations: adaptations ?? this.adaptations,
      formalityAnalysis: formalityAnalysis ?? this.formalityAnalysis,
      sensitivityWarnings: sensitivityWarnings ?? this.sensitivityWarnings,
      customNotes: customNotes ?? this.customNotes,
      background: background ?? this.background,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      isExpertVerified: isExpertVerified ?? this.isExpertVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      feedback: feedback ?? this.feedback,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'sourceRegion': sourceRegion,
      'targetRegion': targetRegion,
      'originalText': originalText,
      'translatedText': translatedText,
      'contextNotes': contextNotes.map((note) => note.toJson()).toList(),
      'regionalVariations':
          regionalVariations.map((variation) => variation.toJson()).toList(),
      'adaptations':
          adaptations.map((adaptation) => adaptation.toJson()).toList(),
      'formalityAnalysis': formalityAnalysis?.toJson(),
      'sensitivityWarnings':
          sensitivityWarnings.map((warning) => warning.toJson()).toList(),
      'customNotes': customNotes.map((note) => note.toJson()).toList(),
      'background': background?.toJson(),
      'confidenceScore': confidenceScore,
      'isExpertVerified': isExpertVerified,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'feedback': feedback.map((fb) => fb.toJson()).toList(),
    };
  }

  /// Create from JSON
  factory EnhancedCulturalContext.fromJson(Map<String, dynamic> json) {
    return EnhancedCulturalContext(
      id: json['id'] as String,
      sourceLanguage: json['sourceLanguage'] as String,
      targetLanguage: json['targetLanguage'] as String,
      sourceRegion: json['sourceRegion'] as String?,
      targetRegion: json['targetRegion'] as String?,
      originalText: json['originalText'] as String,
      translatedText: json['translatedText'] as String,
      contextNotes: (json['contextNotes'] as List<dynamic>?)
              ?.map((noteJson) => CulturalContextNote.fromJson(
                  noteJson as Map<String, dynamic>))
              .toList() ??
          [],
      regionalVariations: (json['regionalVariations'] as List<dynamic>?)
              ?.map((variationJson) => RegionalVariation.fromJson(
                  variationJson as Map<String, dynamic>))
              .toList() ??
          [],
      adaptations: (json['adaptations'] as List<dynamic>?)
              ?.map((adaptationJson) => CulturalAdaptation.fromJson(
                  adaptationJson as Map<String, dynamic>))
              .toList() ??
          [],
      formalityAnalysis: json['formalityAnalysis'] != null
          ? FormalityAnalysis.fromJson(
              json['formalityAnalysis'] as Map<String, dynamic>)
          : null,
      sensitivityWarnings: (json['sensitivityWarnings'] as List<dynamic>?)
              ?.map((warningJson) => CulturalSensitivityWarning.fromJson(
                  warningJson as Map<String, dynamic>))
              .toList() ??
          [],
      customNotes: (json['customNotes'] as List<dynamic>?)
              ?.map((noteJson) =>
                  LocalCustomNote.fromJson(noteJson as Map<String, dynamic>))
              .toList() ??
          [],
      background: json['background'] != null
          ? CulturalBackground.fromJson(
              json['background'] as Map<String, dynamic>)
          : null,
      confidenceScore: json['confidenceScore'] as double,
      isExpertVerified: json['isExpertVerified'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      feedback: (json['feedback'] as List<dynamic>?)
              ?.map((fbJson) => CulturalContextFeedback.fromJson(
                  fbJson as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  /// Check if context has important cultural considerations
  bool get hasImportantConsiderations {
    return sensitivityWarnings.isNotEmpty ||
        adaptations.any(
            (adaptation) => adaptation.importance == CulturalImportance.high) ||
        contextNotes.any((note) =>
            note.type == CulturalContextType.formality ||
            note.type == CulturalContextType.taboo);
  }

  /// Get the most important cultural notes
  List<CulturalContextNote> get importantNotes {
    return contextNotes
        .where((note) =>
            note.type == CulturalContextType.formality ||
            note.type == CulturalContextType.taboo ||
            note.type == CulturalContextType.religious)
        .toList();
  }

  /// Get regional alternatives for the translation
  List<String> get regionalAlternatives {
    return regionalVariations
        .map((variation) => variation.alternativeText)
        .toList();
  }

  /// Get average feedback rating
  double get averageFeedbackRating {
    if (feedback.isEmpty) return 0.0;
    final totalRating =
        feedback.fold<double>(0.0, (sum, fb) => sum + fb.rating);
    return totalRating / feedback.length;
  }
}

/// Model for regional variations of translations
class RegionalVariation {
  final String region;
  final String regionName;
  final String alternativeText;
  final String explanation;
  final double popularity; // 0.0 to 1.0
  final List<String> contexts; // Where this variation is commonly used

  const RegionalVariation({
    required this.region,
    required this.regionName,
    required this.alternativeText,
    required this.explanation,
    required this.popularity,
    this.contexts = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'region': region,
      'regionName': regionName,
      'alternativeText': alternativeText,
      'explanation': explanation,
      'popularity': popularity,
      'contexts': contexts,
    };
  }

  factory RegionalVariation.fromJson(Map<String, dynamic> json) {
    return RegionalVariation(
      region: json['region'] as String,
      regionName: json['regionName'] as String,
      alternativeText: json['alternativeText'] as String,
      explanation: json['explanation'] as String,
      popularity: json['popularity'] as double,
      contexts: (json['contexts'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }
}

/// Model for cultural adaptation suggestions
class CulturalAdaptation {
  final String type; // 'formality', 'politeness', 'directness', etc.
  final String originalPhrase;
  final String adaptedPhrase;
  final String reason;
  final CulturalImportance importance;
  final List<String> applicableContexts;

  const CulturalAdaptation({
    required this.type,
    required this.originalPhrase,
    required this.adaptedPhrase,
    required this.reason,
    required this.importance,
    this.applicableContexts = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'originalPhrase': originalPhrase,
      'adaptedPhrase': adaptedPhrase,
      'reason': reason,
      'importance': importance.name,
      'applicableContexts': applicableContexts,
    };
  }

  factory CulturalAdaptation.fromJson(Map<String, dynamic> json) {
    return CulturalAdaptation(
      type: json['type'] as String,
      originalPhrase: json['originalPhrase'] as String,
      adaptedPhrase: json['adaptedPhrase'] as String,
      reason: json['reason'] as String,
      importance: CulturalImportance.values.firstWhere(
        (importance) => importance.name == json['importance'],
      ),
      applicableContexts:
          (json['applicableContexts'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }
}

/// Enum for cultural importance levels
enum CulturalImportance {
  low,
  medium,
  high,
  critical,
}

/// Model for formality analysis
class FormalityAnalysis {
  final FormalityLevel originalLevel;
  final FormalityLevel translatedLevel;
  final bool isAppropriate;
  final String? recommendation;
  final List<FormalityAlternative> alternatives;

  const FormalityAnalysis({
    required this.originalLevel,
    required this.translatedLevel,
    required this.isAppropriate,
    this.recommendation,
    this.alternatives = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'originalLevel': originalLevel.name,
      'translatedLevel': translatedLevel.name,
      'isAppropriate': isAppropriate,
      'recommendation': recommendation,
      'alternatives': alternatives.map((alt) => alt.toJson()).toList(),
    };
  }

  factory FormalityAnalysis.fromJson(Map<String, dynamic> json) {
    return FormalityAnalysis(
      originalLevel: FormalityLevel.values.firstWhere(
        (level) => level.name == json['originalLevel'],
      ),
      translatedLevel: FormalityLevel.values.firstWhere(
        (level) => level.name == json['translatedLevel'],
      ),
      isAppropriate: json['isAppropriate'] as bool,
      recommendation: json['recommendation'] as String?,
      alternatives: (json['alternatives'] as List<dynamic>?)
              ?.map((altJson) => FormalityAlternative.fromJson(
                  altJson as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}

/// Enum for formality levels
enum FormalityLevel {
  veryInformal,
  informal,
  neutral,
  formal,
  veryFormal,
}

/// Model for formality alternatives
class FormalityAlternative {
  final FormalityLevel level;
  final String text;
  final String context;

  const FormalityAlternative({
    required this.level,
    required this.text,
    required this.context,
  });

  Map<String, dynamic> toJson() {
    return {
      'level': level.name,
      'text': text,
      'context': context,
    };
  }

  factory FormalityAlternative.fromJson(Map<String, dynamic> json) {
    return FormalityAlternative(
      level: FormalityLevel.values.firstWhere(
        (level) => level.name == json['level'],
      ),
      text: json['text'] as String,
      context: json['context'] as String,
    );
  }
}

/// Model for cultural sensitivity warnings
class CulturalSensitivityWarning {
  final String type; // 'religious', 'political', 'social', 'gender', etc.
  final String warning;
  final String explanation;
  final String? suggestedAlternative;
  final SeverityLevel severity;

  const CulturalSensitivityWarning({
    required this.type,
    required this.warning,
    required this.explanation,
    this.suggestedAlternative,
    required this.severity,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'warning': warning,
      'explanation': explanation,
      'suggestedAlternative': suggestedAlternative,
      'severity': severity.name,
    };
  }

  factory CulturalSensitivityWarning.fromJson(Map<String, dynamic> json) {
    return CulturalSensitivityWarning(
      type: json['type'] as String,
      warning: json['warning'] as String,
      explanation: json['explanation'] as String,
      suggestedAlternative: json['suggestedAlternative'] as String?,
      severity: SeverityLevel.values.firstWhere(
        (severity) => severity.name == json['severity'],
      ),
    );
  }
}

/// Enum for severity levels
enum SeverityLevel {
  info,
  warning,
  critical,
}

/// Model for local customs and etiquette notes
class LocalCustomNote {
  final String custom;
  final String description;
  final String context;
  final bool isEssential;

  const LocalCustomNote({
    required this.custom,
    required this.description,
    required this.context,
    this.isEssential = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'custom': custom,
      'description': description,
      'context': context,
      'isEssential': isEssential,
    };
  }

  factory LocalCustomNote.fromJson(Map<String, dynamic> json) {
    return LocalCustomNote(
      custom: json['custom'] as String,
      description: json['description'] as String,
      context: json['context'] as String,
      isEssential: json['isEssential'] as bool? ?? false,
    );
  }
}

/// Model for cultural background information
class CulturalBackground {
  final String title;
  final String description;
  final List<String> keyPoints;
  final Map<String, String> additionalInfo;

  const CulturalBackground({
    required this.title,
    required this.description,
    this.keyPoints = const [],
    this.additionalInfo = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'keyPoints': keyPoints,
      'additionalInfo': additionalInfo,
    };
  }

  factory CulturalBackground.fromJson(Map<String, dynamic> json) {
    return CulturalBackground(
      title: json['title'] as String,
      description: json['description'] as String,
      keyPoints: (json['keyPoints'] as List<dynamic>?)?.cast<String>() ?? [],
      additionalInfo: (json['additionalInfo'] as Map<String, dynamic>?)
              ?.cast<String, String>() ??
          {},
    );
  }
}

/// Model for cultural context feedback
class CulturalContextFeedback {
  final String userId;
  final double rating; // 1.0 to 5.0
  final String? comment;
  final DateTime createdAt;

  const CulturalContextFeedback({
    required this.userId,
    required this.rating,
    this.comment,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'rating': rating,
      'comment': comment,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory CulturalContextFeedback.fromJson(Map<String, dynamic> json) {
    return CulturalContextFeedback(
      userId: json['userId'] as String,
      rating: json['rating'] as double,
      comment: json['comment'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }
}
