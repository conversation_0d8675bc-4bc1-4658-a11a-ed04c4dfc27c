// Enhanced pronunciation guidance with detailed feedback and practice recommendations

/// Enhanced pronunciation guidance with phonetic transcription and practice modes
class EnhancedPronunciationGuidance {
  /// Unique identifier for the pronunciation guidance
  final String id;

  /// Text being analyzed for pronunciation
  final String text;

  /// Language code
  final String languageCode;

  /// Dialect or accent variant
  final String? dialect;

  /// Phonetic transcription (IPA)
  final String? phoneticTranscription;

  /// Simplified phonetic guide for non-linguists
  final String? simplifiedPhonetics;

  /// Audio file path for native pronunciation
  final String? nativeAudioPath;

  /// Audio file path for slow pronunciation
  final String? slowAudioPath;

  /// Breakdown of pronunciation by syllables
  final List<SyllableBreakdown> syllableBreakdown;

  /// Stress patterns and emphasis
  final List<StressPattern> stressPatterns;

  /// Common pronunciation mistakes for learners
  final List<PronunciationMistake> commonMistakes;

  /// Practice exercises and drills
  final List<PronunciationExercise> exercises;

  /// Difficulty level for pronunciation
  final PronunciationDifficulty difficulty;

  /// Tips and techniques for better pronunciation
  final List<PronunciationTip> tips;

  /// Related words with similar pronunciation patterns
  final List<RelatedWord> relatedWords;

  /// Regional pronunciation variations
  final List<RegionalPronunciation> regionalVariations;

  /// Confidence score for pronunciation analysis (0.0 to 1.0)
  final double confidenceScore;

  /// Whether this guidance has been verified by linguists
  final bool isLinguistVerified;

  /// Timestamp when guidance was generated
  final DateTime createdAt;

  /// Last updated timestamp
  final DateTime updatedAt;

  /// User practice history and progress
  final List<PronunciationProgress> practiceHistory;

  const EnhancedPronunciationGuidance({
    required this.id,
    required this.text,
    required this.languageCode,
    this.dialect,
    this.phoneticTranscription,
    this.simplifiedPhonetics,
    this.nativeAudioPath,
    this.slowAudioPath,
    this.syllableBreakdown = const [],
    this.stressPatterns = const [],
    this.commonMistakes = const [],
    this.exercises = const [],
    required this.difficulty,
    this.tips = const [],
    this.relatedWords = const [],
    this.regionalVariations = const [],
    required this.confidenceScore,
    this.isLinguistVerified = false,
    required this.createdAt,
    required this.updatedAt,
    this.practiceHistory = const [],
  });

  /// Create a copy with updated fields
  EnhancedPronunciationGuidance copyWith({
    String? id,
    String? text,
    String? languageCode,
    String? dialect,
    String? phoneticTranscription,
    String? simplifiedPhonetics,
    String? nativeAudioPath,
    String? slowAudioPath,
    List<SyllableBreakdown>? syllableBreakdown,
    List<StressPattern>? stressPatterns,
    List<PronunciationMistake>? commonMistakes,
    List<PronunciationExercise>? exercises,
    PronunciationDifficulty? difficulty,
    List<PronunciationTip>? tips,
    List<RelatedWord>? relatedWords,
    List<RegionalPronunciation>? regionalVariations,
    double? confidenceScore,
    bool? isLinguistVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<PronunciationProgress>? practiceHistory,
  }) {
    return EnhancedPronunciationGuidance(
      id: id ?? this.id,
      text: text ?? this.text,
      languageCode: languageCode ?? this.languageCode,
      dialect: dialect ?? this.dialect,
      phoneticTranscription:
          phoneticTranscription ?? this.phoneticTranscription,
      simplifiedPhonetics: simplifiedPhonetics ?? this.simplifiedPhonetics,
      nativeAudioPath: nativeAudioPath ?? this.nativeAudioPath,
      slowAudioPath: slowAudioPath ?? this.slowAudioPath,
      syllableBreakdown: syllableBreakdown ?? this.syllableBreakdown,
      stressPatterns: stressPatterns ?? this.stressPatterns,
      commonMistakes: commonMistakes ?? this.commonMistakes,
      exercises: exercises ?? this.exercises,
      difficulty: difficulty ?? this.difficulty,
      tips: tips ?? this.tips,
      relatedWords: relatedWords ?? this.relatedWords,
      regionalVariations: regionalVariations ?? this.regionalVariations,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      isLinguistVerified: isLinguistVerified ?? this.isLinguistVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      practiceHistory: practiceHistory ?? this.practiceHistory,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'languageCode': languageCode,
      'dialect': dialect,
      'phoneticTranscription': phoneticTranscription,
      'simplifiedPhonetics': simplifiedPhonetics,
      'nativeAudioPath': nativeAudioPath,
      'slowAudioPath': slowAudioPath,
      'syllableBreakdown':
          syllableBreakdown.map((breakdown) => breakdown.toJson()).toList(),
      'stressPatterns':
          stressPatterns.map((pattern) => pattern.toJson()).toList(),
      'commonMistakes':
          commonMistakes.map((mistake) => mistake.toJson()).toList(),
      'exercises': exercises.map((exercise) => exercise.toJson()).toList(),
      'difficulty': difficulty.name,
      'tips': tips.map((tip) => tip.toJson()).toList(),
      'relatedWords': relatedWords.map((word) => word.toJson()).toList(),
      'regionalVariations':
          regionalVariations.map((variation) => variation.toJson()).toList(),
      'confidenceScore': confidenceScore,
      'isLinguistVerified': isLinguistVerified,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'practiceHistory':
          practiceHistory.map((progress) => progress.toJson()).toList(),
    };
  }

  /// Create from JSON
  factory EnhancedPronunciationGuidance.fromJson(Map<String, dynamic> json) {
    return EnhancedPronunciationGuidance(
      id: json['id'] as String,
      text: json['text'] as String,
      languageCode: json['languageCode'] as String,
      dialect: json['dialect'] as String?,
      phoneticTranscription: json['phoneticTranscription'] as String?,
      simplifiedPhonetics: json['simplifiedPhonetics'] as String?,
      nativeAudioPath: json['nativeAudioPath'] as String?,
      slowAudioPath: json['slowAudioPath'] as String?,
      syllableBreakdown: (json['syllableBreakdown'] as List<dynamic>?)
              ?.map((breakdownJson) => SyllableBreakdown.fromJson(
                  breakdownJson as Map<String, dynamic>))
              .toList() ??
          [],
      stressPatterns: (json['stressPatterns'] as List<dynamic>?)
              ?.map((patternJson) =>
                  StressPattern.fromJson(patternJson as Map<String, dynamic>))
              .toList() ??
          [],
      commonMistakes: (json['commonMistakes'] as List<dynamic>?)
              ?.map((mistakeJson) => PronunciationMistake.fromJson(
                  mistakeJson as Map<String, dynamic>))
              .toList() ??
          [],
      exercises: (json['exercises'] as List<dynamic>?)
              ?.map((exerciseJson) => PronunciationExercise.fromJson(
                  exerciseJson as Map<String, dynamic>))
              .toList() ??
          [],
      difficulty: PronunciationDifficulty.values.firstWhere(
        (difficulty) => difficulty.name == json['difficulty'],
      ),
      tips: (json['tips'] as List<dynamic>?)
              ?.map((tipJson) =>
                  PronunciationTip.fromJson(tipJson as Map<String, dynamic>))
              .toList() ??
          [],
      relatedWords: (json['relatedWords'] as List<dynamic>?)
              ?.map((wordJson) =>
                  RelatedWord.fromJson(wordJson as Map<String, dynamic>))
              .toList() ??
          [],
      regionalVariations: (json['regionalVariations'] as List<dynamic>?)
              ?.map((variationJson) => RegionalPronunciation.fromJson(
                  variationJson as Map<String, dynamic>))
              .toList() ??
          [],
      confidenceScore: json['confidenceScore'] as double,
      isLinguistVerified: json['isLinguistVerified'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      practiceHistory: (json['practiceHistory'] as List<dynamic>?)
              ?.map((progressJson) => PronunciationProgress.fromJson(
                  progressJson as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  /// Get the primary stress syllable
  SyllableBreakdown? get primaryStressSyllable {
    return syllableBreakdown
        .where((syllable) => syllable.isPrimaryStress)
        .firstOrNull;
  }

  /// Get all stressed syllables
  List<SyllableBreakdown> get stressedSyllables {
    return syllableBreakdown
        .where((syllable) =>
            syllable.isPrimaryStress || syllable.isSecondaryStress)
        .toList();
  }

  /// Get the most recent practice score
  double? get latestPracticeScore {
    if (practiceHistory.isEmpty) return null;
    return practiceHistory.last.accuracyScore;
  }

  /// Get average practice score
  double get averagePracticeScore {
    if (practiceHistory.isEmpty) return 0.0;
    final totalScore = practiceHistory.fold<double>(
        0.0, (sum, progress) => sum + progress.accuracyScore);
    return totalScore / practiceHistory.length;
  }

  /// Check if user needs more practice
  bool get needsMorePractice {
    return averagePracticeScore < 0.7 || practiceHistory.length < 3;
  }
}

/// Model for syllable breakdown
class SyllableBreakdown {
  final String syllable;
  final String phonetic;
  final bool isPrimaryStress;
  final bool isSecondaryStress;
  final int position;
  final String? audioPath;

  const SyllableBreakdown({
    required this.syllable,
    required this.phonetic,
    this.isPrimaryStress = false,
    this.isSecondaryStress = false,
    required this.position,
    this.audioPath,
  });

  Map<String, dynamic> toJson() {
    return {
      'syllable': syllable,
      'phonetic': phonetic,
      'isPrimaryStress': isPrimaryStress,
      'isSecondaryStress': isSecondaryStress,
      'position': position,
      'audioPath': audioPath,
    };
  }

  factory SyllableBreakdown.fromJson(Map<String, dynamic> json) {
    return SyllableBreakdown(
      syllable: json['syllable'] as String,
      phonetic: json['phonetic'] as String,
      isPrimaryStress: json['isPrimaryStress'] as bool? ?? false,
      isSecondaryStress: json['isSecondaryStress'] as bool? ?? false,
      position: json['position'] as int,
      audioPath: json['audioPath'] as String?,
    );
  }
}

/// Model for stress patterns
class StressPattern {
  final String pattern; // e.g., "1-0-1" for primary-unstressed-primary
  final String description;
  final List<int> stressedPositions;

  const StressPattern({
    required this.pattern,
    required this.description,
    required this.stressedPositions,
  });

  Map<String, dynamic> toJson() {
    return {
      'pattern': pattern,
      'description': description,
      'stressedPositions': stressedPositions,
    };
  }

  factory StressPattern.fromJson(Map<String, dynamic> json) {
    return StressPattern(
      pattern: json['pattern'] as String,
      description: json['description'] as String,
      stressedPositions:
          (json['stressedPositions'] as List<dynamic>).cast<int>(),
    );
  }
}

/// Model for common pronunciation mistakes
class PronunciationMistake {
  final String incorrectPronunciation;
  final String correctPronunciation;
  final String explanation;
  final String tip;
  final List<String> commonFor; // Languages where this mistake is common

  const PronunciationMistake({
    required this.incorrectPronunciation,
    required this.correctPronunciation,
    required this.explanation,
    required this.tip,
    this.commonFor = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'incorrectPronunciation': incorrectPronunciation,
      'correctPronunciation': correctPronunciation,
      'explanation': explanation,
      'tip': tip,
      'commonFor': commonFor,
    };
  }

  factory PronunciationMistake.fromJson(Map<String, dynamic> json) {
    return PronunciationMistake(
      incorrectPronunciation: json['incorrectPronunciation'] as String,
      correctPronunciation: json['correctPronunciation'] as String,
      explanation: json['explanation'] as String,
      tip: json['tip'] as String,
      commonFor: (json['commonFor'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }
}

/// Model for pronunciation exercises
class PronunciationExercise {
  final String id;
  final ExerciseType type;
  final String instruction;
  final String targetSound;
  final List<String> practiceWords;
  final String? audioPath;
  final int difficulty; // 1-5 scale

  const PronunciationExercise({
    required this.id,
    required this.type,
    required this.instruction,
    required this.targetSound,
    this.practiceWords = const [],
    this.audioPath,
    required this.difficulty,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'instruction': instruction,
      'targetSound': targetSound,
      'practiceWords': practiceWords,
      'audioPath': audioPath,
      'difficulty': difficulty,
    };
  }

  factory PronunciationExercise.fromJson(Map<String, dynamic> json) {
    return PronunciationExercise(
      id: json['id'] as String,
      type: ExerciseType.values.firstWhere(
        (type) => type.name == json['type'],
      ),
      instruction: json['instruction'] as String,
      targetSound: json['targetSound'] as String,
      practiceWords:
          (json['practiceWords'] as List<dynamic>?)?.cast<String>() ?? [],
      audioPath: json['audioPath'] as String?,
      difficulty: json['difficulty'] as int,
    );
  }
}

/// Enum for exercise types
enum ExerciseType {
  minimalPairs,
  repetition,
  shadowReading,
  stressPattern,
  intonation,
  tonguetwister,
}

/// Enum for pronunciation difficulty
enum PronunciationDifficulty {
  beginner,
  elementary,
  intermediate,
  advanced,
  expert,
}

/// Model for pronunciation tips
class PronunciationTip {
  final String category; // 'mouth_position', 'breathing', 'rhythm', etc.
  final String tip;
  final String explanation;
  final String? visualAid; // Path to image or diagram

  const PronunciationTip({
    required this.category,
    required this.tip,
    required this.explanation,
    this.visualAid,
  });

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'tip': tip,
      'explanation': explanation,
      'visualAid': visualAid,
    };
  }

  factory PronunciationTip.fromJson(Map<String, dynamic> json) {
    return PronunciationTip(
      category: json['category'] as String,
      tip: json['tip'] as String,
      explanation: json['explanation'] as String,
      visualAid: json['visualAid'] as String?,
    );
  }
}

/// Model for related words with similar pronunciation
class RelatedWord {
  final String word;
  final String phonetic;
  final String meaning;
  final double similarity; // 0.0 to 1.0

  const RelatedWord({
    required this.word,
    required this.phonetic,
    required this.meaning,
    required this.similarity,
  });

  Map<String, dynamic> toJson() {
    return {
      'word': word,
      'phonetic': phonetic,
      'meaning': meaning,
      'similarity': similarity,
    };
  }

  factory RelatedWord.fromJson(Map<String, dynamic> json) {
    return RelatedWord(
      word: json['word'] as String,
      phonetic: json['phonetic'] as String,
      meaning: json['meaning'] as String,
      similarity: json['similarity'] as double,
    );
  }
}

/// Model for regional pronunciation variations
class RegionalPronunciation {
  final String region;
  final String accent;
  final String phonetic;
  final String? audioPath;
  final String description;

  const RegionalPronunciation({
    required this.region,
    required this.accent,
    required this.phonetic,
    this.audioPath,
    required this.description,
  });

  Map<String, dynamic> toJson() {
    return {
      'region': region,
      'accent': accent,
      'phonetic': phonetic,
      'audioPath': audioPath,
      'description': description,
    };
  }

  factory RegionalPronunciation.fromJson(Map<String, dynamic> json) {
    return RegionalPronunciation(
      region: json['region'] as String,
      accent: json['accent'] as String,
      phonetic: json['phonetic'] as String,
      audioPath: json['audioPath'] as String?,
      description: json['description'] as String,
    );
  }
}

/// Model for pronunciation practice progress
class PronunciationProgress {
  final String sessionId;
  final DateTime practiceDate;
  final double accuracyScore; // 0.0 to 1.0
  final int attemptCount;
  final Duration practiceTime;
  final List<String> improvedSounds;
  final List<String> needsWorkSounds;

  const PronunciationProgress({
    required this.sessionId,
    required this.practiceDate,
    required this.accuracyScore,
    required this.attemptCount,
    required this.practiceTime,
    this.improvedSounds = const [],
    this.needsWorkSounds = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'practiceDate': practiceDate.toIso8601String(),
      'accuracyScore': accuracyScore,
      'attemptCount': attemptCount,
      'practiceTime': practiceTime.inMilliseconds,
      'improvedSounds': improvedSounds,
      'needsWorkSounds': needsWorkSounds,
    };
  }

  factory PronunciationProgress.fromJson(Map<String, dynamic> json) {
    return PronunciationProgress(
      sessionId: json['sessionId'] as String,
      practiceDate: DateTime.parse(json['practiceDate'] as String),
      accuracyScore: json['accuracyScore'] as double,
      attemptCount: json['attemptCount'] as int,
      practiceTime: Duration(milliseconds: json['practiceTime'] as int),
      improvedSounds:
          (json['improvedSounds'] as List<dynamic>?)?.cast<String>() ?? [],
      needsWorkSounds:
          (json['needsWorkSounds'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }
}

extension SyllableBreakdownListExtension on List<SyllableBreakdown> {
  SyllableBreakdown? get firstOrNull {
    return isEmpty ? null : first;
  }
}
