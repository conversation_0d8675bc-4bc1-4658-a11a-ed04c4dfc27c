// Translation accuracy feedback model for user feedback and improvement tracking

/// Enum for translation accuracy rating
enum TranslationAccuracyRating {
  excellent,
  good,
  fair,
  poor,
  terrible,
}

/// Enum for feedback type
enum TranslationFeedbackType {
  accuracy,
  fluency,
  cultural,
  pronunciation,
  slang,
  technical,
  general,
}

/// Enum for improvement suggestion type
enum ImprovementSuggestionType {
  wordChoice,
  grammar,
  culturalContext,
  pronunciation,
  formality,
  slangHandling,
  technicalTerms,
}

/// Model for translation accuracy feedback
class TranslationAccuracyFeedback {
  /// Unique identifier for the feedback
  final String id;

  /// ID of the translation being rated
  final String translationId;

  /// User ID who provided the feedback
  final String userId;

  /// Original text that was translated
  final String originalText;

  /// Translated text being rated
  final String translatedText;

  /// Source language code
  final String sourceLanguage;

  /// Target language code
  final String targetLanguage;

  /// Overall accuracy rating
  final TranslationAccuracyRating accuracyRating;

  /// Specific feedback type
  final TranslationFeedbackType feedbackType;

  /// Detailed feedback comments
  final String? comments;

  /// Suggested correction
  final String? suggestedCorrection;

  /// Specific issues identified
  final List<TranslationIssue> issues;

  /// Improvement suggestions
  final List<ImprovementSuggestion> improvements;

  /// Confidence score from the original translation (0.0 to 1.0)
  final double? originalConfidence;

  /// User's perceived confidence in their feedback (0.0 to 1.0)
  final double userConfidence;

  /// Whether this feedback is from a verified translator
  final bool isVerifiedTranslator;

  /// Language proficiency level of the user providing feedback
  final LanguageProficiencyLevel? userProficiency;

  /// Context where the translation was used
  final TranslationContext? context;

  /// Timestamp when feedback was provided
  final DateTime createdAt;

  /// Whether this feedback has been reviewed by moderators
  final bool isReviewed;

  /// Moderator notes (if reviewed)
  final String? moderatorNotes;

  /// Helpfulness votes from other users
  final int helpfulnessVotes;

  /// Whether this feedback led to model improvements
  final bool contributedToImprovement;

  const TranslationAccuracyFeedback({
    required this.id,
    required this.translationId,
    required this.userId,
    required this.originalText,
    required this.translatedText,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.accuracyRating,
    required this.feedbackType,
    this.comments,
    this.suggestedCorrection,
    this.issues = const [],
    this.improvements = const [],
    this.originalConfidence,
    required this.userConfidence,
    this.isVerifiedTranslator = false,
    this.userProficiency,
    this.context,
    required this.createdAt,
    this.isReviewed = false,
    this.moderatorNotes,
    this.helpfulnessVotes = 0,
    this.contributedToImprovement = false,
  });

  /// Create a copy with updated fields
  TranslationAccuracyFeedback copyWith({
    String? id,
    String? translationId,
    String? userId,
    String? originalText,
    String? translatedText,
    String? sourceLanguage,
    String? targetLanguage,
    TranslationAccuracyRating? accuracyRating,
    TranslationFeedbackType? feedbackType,
    String? comments,
    String? suggestedCorrection,
    List<TranslationIssue>? issues,
    List<ImprovementSuggestion>? improvements,
    double? originalConfidence,
    double? userConfidence,
    bool? isVerifiedTranslator,
    LanguageProficiencyLevel? userProficiency,
    TranslationContext? context,
    DateTime? createdAt,
    bool? isReviewed,
    String? moderatorNotes,
    int? helpfulnessVotes,
    bool? contributedToImprovement,
  }) {
    return TranslationAccuracyFeedback(
      id: id ?? this.id,
      translationId: translationId ?? this.translationId,
      userId: userId ?? this.userId,
      originalText: originalText ?? this.originalText,
      translatedText: translatedText ?? this.translatedText,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      accuracyRating: accuracyRating ?? this.accuracyRating,
      feedbackType: feedbackType ?? this.feedbackType,
      comments: comments ?? this.comments,
      suggestedCorrection: suggestedCorrection ?? this.suggestedCorrection,
      issues: issues ?? this.issues,
      improvements: improvements ?? this.improvements,
      originalConfidence: originalConfidence ?? this.originalConfidence,
      userConfidence: userConfidence ?? this.userConfidence,
      isVerifiedTranslator: isVerifiedTranslator ?? this.isVerifiedTranslator,
      userProficiency: userProficiency ?? this.userProficiency,
      context: context ?? this.context,
      createdAt: createdAt ?? this.createdAt,
      isReviewed: isReviewed ?? this.isReviewed,
      moderatorNotes: moderatorNotes ?? this.moderatorNotes,
      helpfulnessVotes: helpfulnessVotes ?? this.helpfulnessVotes,
      contributedToImprovement:
          contributedToImprovement ?? this.contributedToImprovement,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'translationId': translationId,
      'userId': userId,
      'originalText': originalText,
      'translatedText': translatedText,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'accuracyRating': accuracyRating.name,
      'feedbackType': feedbackType.name,
      'comments': comments,
      'suggestedCorrection': suggestedCorrection,
      'issues': issues.map((issue) => issue.toJson()).toList(),
      'improvements':
          improvements.map((improvement) => improvement.toJson()).toList(),
      'originalConfidence': originalConfidence,
      'userConfidence': userConfidence,
      'isVerifiedTranslator': isVerifiedTranslator,
      'userProficiency': userProficiency?.name,
      'context': context?.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'isReviewed': isReviewed,
      'moderatorNotes': moderatorNotes,
      'helpfulnessVotes': helpfulnessVotes,
      'contributedToImprovement': contributedToImprovement,
    };
  }

  /// Create from JSON
  factory TranslationAccuracyFeedback.fromJson(Map<String, dynamic> json) {
    return TranslationAccuracyFeedback(
      id: json['id'] as String,
      translationId: json['translationId'] as String,
      userId: json['userId'] as String,
      originalText: json['originalText'] as String,
      translatedText: json['translatedText'] as String,
      sourceLanguage: json['sourceLanguage'] as String,
      targetLanguage: json['targetLanguage'] as String,
      accuracyRating: TranslationAccuracyRating.values.firstWhere(
        (rating) => rating.name == json['accuracyRating'],
      ),
      feedbackType: TranslationFeedbackType.values.firstWhere(
        (type) => type.name == json['feedbackType'],
      ),
      comments: json['comments'] as String?,
      suggestedCorrection: json['suggestedCorrection'] as String?,
      issues: (json['issues'] as List<dynamic>?)
              ?.map((issueJson) =>
                  TranslationIssue.fromJson(issueJson as Map<String, dynamic>))
              .toList() ??
          [],
      improvements: (json['improvements'] as List<dynamic>?)
              ?.map((improvementJson) => ImprovementSuggestion.fromJson(
                  improvementJson as Map<String, dynamic>))
              .toList() ??
          [],
      originalConfidence: json['originalConfidence'] as double?,
      userConfidence: json['userConfidence'] as double,
      isVerifiedTranslator: json['isVerifiedTranslator'] as bool? ?? false,
      userProficiency: json['userProficiency'] != null
          ? LanguageProficiencyLevel.values.firstWhere(
              (level) => level.name == json['userProficiency'],
            )
          : null,
      context: json['context'] != null
          ? TranslationContext.fromJson(json['context'] as Map<String, dynamic>)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      isReviewed: json['isReviewed'] as bool? ?? false,
      moderatorNotes: json['moderatorNotes'] as String?,
      helpfulnessVotes: json['helpfulnessVotes'] as int? ?? 0,
      contributedToImprovement:
          json['contributedToImprovement'] as bool? ?? false,
    );
  }

  /// Get numeric score for accuracy rating
  double get accuracyScore {
    switch (accuracyRating) {
      case TranslationAccuracyRating.excellent:
        return 1.0;
      case TranslationAccuracyRating.good:
        return 0.8;
      case TranslationAccuracyRating.fair:
        return 0.6;
      case TranslationAccuracyRating.poor:
        return 0.4;
      case TranslationAccuracyRating.terrible:
        return 0.2;
    }
  }

  /// Get display name for accuracy rating
  String get accuracyRatingDisplayName {
    switch (accuracyRating) {
      case TranslationAccuracyRating.excellent:
        return 'Excellent';
      case TranslationAccuracyRating.good:
        return 'Good';
      case TranslationAccuracyRating.fair:
        return 'Fair';
      case TranslationAccuracyRating.poor:
        return 'Poor';
      case TranslationAccuracyRating.terrible:
        return 'Terrible';
    }
  }

  /// Check if feedback has actionable suggestions
  bool get hasActionableSuggestions {
    return suggestedCorrection != null || improvements.isNotEmpty;
  }

  /// Get priority score for this feedback (higher = more important)
  double get priorityScore {
    double score = 0.0;

    // Base score from accuracy rating (inverted - worse ratings get higher priority)
    score += (1.0 - accuracyScore) * 10;

    // Boost for verified translators
    if (isVerifiedTranslator) score += 5.0;

    // Boost for high user confidence
    score += userConfidence * 3.0;

    // Boost for actionable suggestions
    if (hasActionableSuggestions) score += 2.0;

    // Boost for helpfulness votes
    score += helpfulnessVotes * 0.5;

    return score;
  }
}

/// Model for specific translation issues
class TranslationIssue {
  final String type;
  final String description;
  final int? startIndex;
  final int? endIndex;
  final String? affectedText;
  final String severity; // 'low', 'medium', 'high', 'critical'

  const TranslationIssue({
    required this.type,
    required this.description,
    this.startIndex,
    this.endIndex,
    this.affectedText,
    required this.severity,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'description': description,
      'startIndex': startIndex,
      'endIndex': endIndex,
      'affectedText': affectedText,
      'severity': severity,
    };
  }

  factory TranslationIssue.fromJson(Map<String, dynamic> json) {
    return TranslationIssue(
      type: json['type'] as String,
      description: json['description'] as String,
      startIndex: json['startIndex'] as int?,
      endIndex: json['endIndex'] as int?,
      affectedText: json['affectedText'] as String?,
      severity: json['severity'] as String,
    );
  }
}

/// Model for improvement suggestions
class ImprovementSuggestion {
  final ImprovementSuggestionType type;
  final String description;
  final String? suggestedText;
  final double confidence;

  const ImprovementSuggestion({
    required this.type,
    required this.description,
    this.suggestedText,
    required this.confidence,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'description': description,
      'suggestedText': suggestedText,
      'confidence': confidence,
    };
  }

  factory ImprovementSuggestion.fromJson(Map<String, dynamic> json) {
    return ImprovementSuggestion(
      type: ImprovementSuggestionType.values.firstWhere(
        (type) => type.name == json['type'],
      ),
      description: json['description'] as String,
      suggestedText: json['suggestedText'] as String?,
      confidence: json['confidence'] as double,
    );
  }
}

/// Enum for language proficiency levels
enum LanguageProficiencyLevel {
  beginner,
  elementary,
  intermediate,
  upperIntermediate,
  advanced,
  proficient,
  native,
}

/// Model for translation context
class TranslationContext {
  final String contextType; // 'casual', 'formal', 'business', 'academic', etc.
  final String? domain; // 'medical', 'legal', 'technical', etc.
  final String? situation; // 'restaurant', 'airport', 'meeting', etc.
  final Map<String, dynamic>? metadata;

  const TranslationContext({
    required this.contextType,
    this.domain,
    this.situation,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'contextType': contextType,
      'domain': domain,
      'situation': situation,
      'metadata': metadata,
    };
  }

  factory TranslationContext.fromJson(Map<String, dynamic> json) {
    return TranslationContext(
      contextType: json['contextType'] as String,
      domain: json['domain'] as String?,
      situation: json['situation'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}
