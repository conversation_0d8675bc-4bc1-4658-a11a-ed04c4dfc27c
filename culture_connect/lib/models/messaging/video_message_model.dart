import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for video messages in chat
class VideoMessageModel {
  final String id;
  final String messageId;
  final String videoUrl;
  final String? thumbnailUrl;
  final String? localPath;
  final String? localThumbnailPath;
  final Duration duration;
  final VideoResolution resolution;
  final int fileSize;
  final VideoMessageStatus status;
  final DateTime createdAt;
  final DateTime? playedAt;
  final int playCount;
  final Map<String, dynamic>? metadata;

  const VideoMessageModel({
    required this.id,
    required this.messageId,
    required this.videoUrl,
    this.thumbnailUrl,
    this.localPath,
    this.localThumbnailPath,
    required this.duration,
    required this.resolution,
    required this.fileSize,
    required this.status,
    required this.createdAt,
    this.playedAt,
    this.playCount = 0,
    this.metadata,
  });

  /// Create a copy with updated fields
  VideoMessageModel copyWith({
    String? id,
    String? messageId,
    String? videoUrl,
    String? thumbnailUrl,
    String? localPath,
    String? localThumbnailPath,
    Duration? duration,
    VideoResolution? resolution,
    int? fileSize,
    VideoMessageStatus? status,
    DateTime? createdAt,
    DateTime? playedAt,
    int? playCount,
    Map<String, dynamic>? metadata,
  }) {
    return VideoMessageModel(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      videoUrl: videoUrl ?? this.videoUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      localPath: localPath ?? this.localPath,
      localThumbnailPath: localThumbnailPath ?? this.localThumbnailPath,
      duration: duration ?? this.duration,
      resolution: resolution ?? this.resolution,
      fileSize: fileSize ?? this.fileSize,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      playedAt: playedAt ?? this.playedAt,
      playCount: playCount ?? this.playCount,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'videoUrl': videoUrl,
      'thumbnailUrl': thumbnailUrl,
      'localPath': localPath,
      'localThumbnailPath': localThumbnailPath,
      'duration': duration.inMilliseconds,
      'resolution': {
        'width': resolution.width,
        'height': resolution.height,
      },
      'fileSize': fileSize,
      'status': status.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
      'playedAt': playedAt != null ? Timestamp.fromDate(playedAt!) : null,
      'playCount': playCount,
      'metadata': metadata,
    };
  }

  /// Create from JSON (Firestore)
  factory VideoMessageModel.fromJson(Map<String, dynamic> json) {
    final resolutionData = json['resolution'] as Map<String, dynamic>? ?? {};
    
    return VideoMessageModel(
      id: json['id'] ?? '',
      messageId: json['messageId'] ?? '',
      videoUrl: json['videoUrl'] ?? '',
      thumbnailUrl: json['thumbnailUrl'],
      localPath: json['localPath'],
      localThumbnailPath: json['localThumbnailPath'],
      duration: Duration(milliseconds: json['duration'] ?? 0),
      resolution: VideoResolution(
        width: resolutionData['width'] ?? 720,
        height: resolutionData['height'] ?? 1280,
      ),
      fileSize: json['fileSize'] ?? 0,
      status: VideoMessageStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => VideoMessageStatus.pending,
      ),
      createdAt: json['createdAt'] is Timestamp
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      playedAt: json['playedAt'] is Timestamp
          ? (json['playedAt'] as Timestamp).toDate()
          : null,
      playCount: json['playCount'] ?? 0,
      metadata: json['metadata'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoMessageModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VideoMessageModel(id: $id, duration: $duration, status: $status)';
  }
}

/// Video resolution
class VideoResolution {
  final int width;
  final int height;

  const VideoResolution({
    required this.width,
    required this.height,
  });

  double get aspectRatio => width / height;

  String get displayName => '${width}x$height';

  bool get isPortrait => height > width;
  bool get isLandscape => width > height;
  bool get isSquare => width == height;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoResolution && 
           other.width == width && 
           other.height == height;
  }

  @override
  int get hashCode => width.hashCode ^ height.hashCode;

  @override
  String toString() => displayName;
}

/// Status of video message
enum VideoMessageStatus {
  pending,
  uploading,
  uploaded,
  downloading,
  downloaded,
  playing,
  played,
  error,
}

/// Extension for video message status
extension VideoMessageStatusExtension on VideoMessageStatus {
  bool get isLoading => this == VideoMessageStatus.uploading || 
                       this == VideoMessageStatus.downloading;
  
  bool get isReady => this == VideoMessageStatus.uploaded || 
                     this == VideoMessageStatus.downloaded;
  
  bool get hasError => this == VideoMessageStatus.error;
  
  String get displayName {
    switch (this) {
      case VideoMessageStatus.pending:
        return 'Pending';
      case VideoMessageStatus.uploading:
        return 'Uploading';
      case VideoMessageStatus.uploaded:
        return 'Uploaded';
      case VideoMessageStatus.downloading:
        return 'Downloading';
      case VideoMessageStatus.downloaded:
        return 'Downloaded';
      case VideoMessageStatus.playing:
        return 'Playing';
      case VideoMessageStatus.played:
        return 'Played';
      case VideoMessageStatus.error:
        return 'Error';
    }
  }
}

/// Video recording configuration
class VideoRecordingConfig {
  final VideoResolution resolution;
  final int bitRate;
  final int frameRate;
  final Duration maxDuration;
  final VideoQuality quality;
  final bool enableAudio;

  const VideoRecordingConfig({
    this.resolution = const VideoResolution(width: 720, height: 1280),
    this.bitRate = 2000000, // 2 Mbps
    this.frameRate = 30,
    this.maxDuration = const Duration(minutes: 2),
    this.quality = VideoQuality.medium,
    this.enableAudio = true,
  });
}

/// Video quality settings
enum VideoQuality {
  low,
  medium,
  high,
  ultraHigh,
}

extension VideoQualityExtension on VideoQuality {
  String get displayName {
    switch (this) {
      case VideoQuality.low:
        return 'Low (480p)';
      case VideoQuality.medium:
        return 'Medium (720p)';
      case VideoQuality.high:
        return 'High (1080p)';
      case VideoQuality.ultraHigh:
        return 'Ultra High (4K)';
    }
  }

  VideoResolution get resolution {
    switch (this) {
      case VideoQuality.low:
        return const VideoResolution(width: 480, height: 854);
      case VideoQuality.medium:
        return const VideoResolution(width: 720, height: 1280);
      case VideoQuality.high:
        return const VideoResolution(width: 1080, height: 1920);
      case VideoQuality.ultraHigh:
        return const VideoResolution(width: 2160, height: 3840);
    }
  }

  int get bitRate {
    switch (this) {
      case VideoQuality.low:
        return 1000000; // 1 Mbps
      case VideoQuality.medium:
        return 2000000; // 2 Mbps
      case VideoQuality.high:
        return 5000000; // 5 Mbps
      case VideoQuality.ultraHigh:
        return 10000000; // 10 Mbps
    }
  }
}

/// Video playback state
class VideoPlaybackState {
  final String? currentMessageId;
  final Duration position;
  final Duration duration;
  final bool isPlaying;
  final bool isLoading;
  final bool isMuted;
  final double volume;
  final double playbackSpeed;

  const VideoPlaybackState({
    this.currentMessageId,
    this.position = Duration.zero,
    this.duration = Duration.zero,
    this.isPlaying = false,
    this.isLoading = false,
    this.isMuted = false,
    this.volume = 1.0,
    this.playbackSpeed = 1.0,
  });

  VideoPlaybackState copyWith({
    String? currentMessageId,
    Duration? position,
    Duration? duration,
    bool? isPlaying,
    bool? isLoading,
    bool? isMuted,
    double? volume,
    double? playbackSpeed,
  }) {
    return VideoPlaybackState(
      currentMessageId: currentMessageId ?? this.currentMessageId,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      isPlaying: isPlaying ?? this.isPlaying,
      isLoading: isLoading ?? this.isLoading,
      isMuted: isMuted ?? this.isMuted,
      volume: volume ?? this.volume,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
    );
  }
}
