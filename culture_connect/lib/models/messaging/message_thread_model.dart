import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/message_model.dart';

/// Model for message threading and replies
class MessageThreadModel {
  final String id;
  final String parentMessageId;
  final String chatId;
  final List<String> replyMessageIds;
  final int replyCount;
  final DateTime createdAt;
  final DateTime lastReplyAt;
  final String lastReplyUserId;
  final bool isActive;
  final Map<String, dynamic>? metadata;

  const MessageThreadModel({
    required this.id,
    required this.parentMessageId,
    required this.chatId,
    required this.replyMessageIds,
    required this.replyCount,
    required this.createdAt,
    required this.lastReplyAt,
    required this.lastReplyUserId,
    this.isActive = true,
    this.metadata,
  });

  /// Create from JSON (Firestore)
  factory MessageThreadModel.fromJson(Map<String, dynamic> json) {
    return MessageThreadModel(
      id: json['id'] ?? '',
      parentMessageId: json['parentMessageId'] ?? '',
      chatId: json['chatId'] ?? '',
      replyMessageIds: List<String>.from(json['replyMessageIds'] ?? []),
      replyCount: json['replyCount'] ?? 0,
      createdAt: (json['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastReplyAt: (json['lastReplyAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastReplyUserId: json['lastReplyUserId'] ?? '',
      isActive: json['isActive'] ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parentMessageId': parentMessageId,
      'chatId': chatId,
      'replyMessageIds': replyMessageIds,
      'replyCount': replyCount,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastReplyAt': Timestamp.fromDate(lastReplyAt),
      'lastReplyUserId': lastReplyUserId,
      'isActive': isActive,
      'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  MessageThreadModel copyWith({
    String? id,
    String? parentMessageId,
    String? chatId,
    List<String>? replyMessageIds,
    int? replyCount,
    DateTime? createdAt,
    DateTime? lastReplyAt,
    String? lastReplyUserId,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return MessageThreadModel(
      id: id ?? this.id,
      parentMessageId: parentMessageId ?? this.parentMessageId,
      chatId: chatId ?? this.chatId,
      replyMessageIds: replyMessageIds ?? this.replyMessageIds,
      replyCount: replyCount ?? this.replyCount,
      createdAt: createdAt ?? this.createdAt,
      lastReplyAt: lastReplyAt ?? this.lastReplyAt,
      lastReplyUserId: lastReplyUserId ?? this.lastReplyUserId,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageThreadModel &&
        other.id == id &&
        other.parentMessageId == parentMessageId &&
        other.chatId == chatId;
  }

  @override
  int get hashCode => Object.hash(id, parentMessageId, chatId);

  @override
  String toString() {
    return 'MessageThreadModel(id: $id, parentMessageId: $parentMessageId, replyCount: $replyCount)';
  }
}

/// Model for message replies
class MessageReplyModel {
  final String id;
  final String threadId;
  final String parentMessageId;
  final String replyToMessageId; // Direct reply target (for nested replies)
  final String chatId;
  final String senderId;
  final String text;
  final DateTime timestamp;
  final MessageStatus status;
  final MessageType type;
  final String? mediaUrl;
  final Map<String, dynamic>? metadata;
  final bool isEdited;
  final DateTime? editedAt;

  const MessageReplyModel({
    required this.id,
    required this.threadId,
    required this.parentMessageId,
    required this.replyToMessageId,
    required this.chatId,
    required this.senderId,
    required this.text,
    required this.timestamp,
    required this.status,
    required this.type,
    this.mediaUrl,
    this.metadata,
    this.isEdited = false,
    this.editedAt,
  });

  /// Create from JSON (Firestore)
  factory MessageReplyModel.fromJson(Map<String, dynamic> json) {
    return MessageReplyModel(
      id: json['id'] ?? '',
      threadId: json['threadId'] ?? '',
      parentMessageId: json['parentMessageId'] ?? '',
      replyToMessageId: json['replyToMessageId'] ?? '',
      chatId: json['chatId'] ?? '',
      senderId: json['senderId'] ?? '',
      text: json['text'] ?? '',
      timestamp: (json['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      status: MessageStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      type: MessageType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => MessageType.text,
      ),
      mediaUrl: json['mediaUrl'],
      metadata: json['metadata'] as Map<String, dynamic>?,
      isEdited: json['isEdited'] ?? false,
      editedAt: (json['editedAt'] as Timestamp?)?.toDate(),
    );
  }

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'threadId': threadId,
      'parentMessageId': parentMessageId,
      'replyToMessageId': replyToMessageId,
      'chatId': chatId,
      'senderId': senderId,
      'text': text,
      'timestamp': Timestamp.fromDate(timestamp),
      'status': status.toString().split('.').last,
      'type': type.toString().split('.').last,
      'mediaUrl': mediaUrl,
      'metadata': metadata,
      'isEdited': isEdited,
      'editedAt': editedAt != null ? Timestamp.fromDate(editedAt!) : null,
    };
  }

  /// Create a copy with updated fields
  MessageReplyModel copyWith({
    String? id,
    String? threadId,
    String? parentMessageId,
    String? replyToMessageId,
    String? chatId,
    String? senderId,
    String? text,
    DateTime? timestamp,
    MessageStatus? status,
    MessageType? type,
    String? mediaUrl,
    Map<String, dynamic>? metadata,
    bool? isEdited,
    DateTime? editedAt,
  }) {
    return MessageReplyModel(
      id: id ?? this.id,
      threadId: threadId ?? this.threadId,
      parentMessageId: parentMessageId ?? this.parentMessageId,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      text: text ?? this.text,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      type: type ?? this.type,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      metadata: metadata ?? this.metadata,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageReplyModel &&
        other.id == id &&
        other.threadId == threadId &&
        other.parentMessageId == parentMessageId;
  }

  @override
  int get hashCode => Object.hash(id, threadId, parentMessageId);

  @override
  String toString() {
    return 'MessageReplyModel(id: $id, threadId: $threadId, text: ${text.length > 50 ? '${text.substring(0, 50)}...' : text})';
  }
}
