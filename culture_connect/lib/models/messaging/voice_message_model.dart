import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for voice messages in chat
class VoiceMessageModel {
  final String id;
  final String messageId;
  final String audioUrl;
  final String? localPath;
  final Duration duration;
  final double? amplitude;
  final List<double>? waveform;
  final VoiceMessageStatus status;
  final DateTime createdAt;
  final DateTime? playedAt;
  final int playCount;
  final Map<String, dynamic>? metadata;

  const VoiceMessageModel({
    required this.id,
    required this.messageId,
    required this.audioUrl,
    this.localPath,
    required this.duration,
    this.amplitude,
    this.waveform,
    required this.status,
    required this.createdAt,
    this.playedAt,
    this.playCount = 0,
    this.metadata,
  });

  /// Create a copy with updated fields
  VoiceMessageModel copyWith({
    String? id,
    String? messageId,
    String? audioUrl,
    String? localPath,
    Duration? duration,
    double? amplitude,
    List<double>? waveform,
    VoiceMessageStatus? status,
    DateTime? createdAt,
    DateTime? playedAt,
    int? playCount,
    Map<String, dynamic>? metadata,
  }) {
    return VoiceMessageModel(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      audioUrl: audioUrl ?? this.audioUrl,
      localPath: localPath ?? this.localPath,
      duration: duration ?? this.duration,
      amplitude: amplitude ?? this.amplitude,
      waveform: waveform ?? this.waveform,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      playedAt: playedAt ?? this.playedAt,
      playCount: playCount ?? this.playCount,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'audioUrl': audioUrl,
      'localPath': localPath,
      'duration': duration.inMilliseconds,
      'amplitude': amplitude,
      'waveform': waveform,
      'status': status.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
      'playedAt': playedAt != null ? Timestamp.fromDate(playedAt!) : null,
      'playCount': playCount,
      'metadata': metadata,
    };
  }

  /// Create from JSON (Firestore)
  factory VoiceMessageModel.fromJson(Map<String, dynamic> json) {
    return VoiceMessageModel(
      id: json['id'] ?? '',
      messageId: json['messageId'] ?? '',
      audioUrl: json['audioUrl'] ?? '',
      localPath: json['localPath'],
      duration: Duration(milliseconds: json['duration'] ?? 0),
      amplitude: json['amplitude']?.toDouble(),
      waveform: json['waveform'] != null 
          ? List<double>.from(json['waveform'].map((x) => x.toDouble()))
          : null,
      status: VoiceMessageStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => VoiceMessageStatus.pending,
      ),
      createdAt: json['createdAt'] is Timestamp
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      playedAt: json['playedAt'] is Timestamp
          ? (json['playedAt'] as Timestamp).toDate()
          : null,
      playCount: json['playCount'] ?? 0,
      metadata: json['metadata'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VoiceMessageModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VoiceMessageModel(id: $id, duration: $duration, status: $status)';
  }
}

/// Status of voice message
enum VoiceMessageStatus {
  pending,
  uploading,
  uploaded,
  downloading,
  downloaded,
  playing,
  played,
  error,
}

/// Extension for voice message status
extension VoiceMessageStatusExtension on VoiceMessageStatus {
  bool get isLoading => this == VoiceMessageStatus.uploading || 
                       this == VoiceMessageStatus.downloading;
  
  bool get isReady => this == VoiceMessageStatus.uploaded || 
                     this == VoiceMessageStatus.downloaded;
  
  bool get hasError => this == VoiceMessageStatus.error;
  
  String get displayName {
    switch (this) {
      case VoiceMessageStatus.pending:
        return 'Pending';
      case VoiceMessageStatus.uploading:
        return 'Uploading';
      case VoiceMessageStatus.uploaded:
        return 'Uploaded';
      case VoiceMessageStatus.downloading:
        return 'Downloading';
      case VoiceMessageStatus.downloaded:
        return 'Downloaded';
      case VoiceMessageStatus.playing:
        return 'Playing';
      case VoiceMessageStatus.played:
        return 'Played';
      case VoiceMessageStatus.error:
        return 'Error';
    }
  }
}

/// Voice message recording configuration
class VoiceRecordingConfig {
  final int bitRate;
  final int sampleRate;
  final String encoder;
  final Duration maxDuration;
  final bool enableWaveform;
  final bool enableAmplitude;

  const VoiceRecordingConfig({
    this.bitRate = 128000,
    this.sampleRate = 44100,
    this.encoder = 'aac',
    this.maxDuration = const Duration(minutes: 5),
    this.enableWaveform = true,
    this.enableAmplitude = true,
  });
}

/// Voice message playback state
class VoicePlaybackState {
  final String? currentMessageId;
  final Duration position;
  final Duration duration;
  final bool isPlaying;
  final bool isLoading;
  final double playbackSpeed;

  const VoicePlaybackState({
    this.currentMessageId,
    this.position = Duration.zero,
    this.duration = Duration.zero,
    this.isPlaying = false,
    this.isLoading = false,
    this.playbackSpeed = 1.0,
  });

  VoicePlaybackState copyWith({
    String? currentMessageId,
    Duration? position,
    Duration? duration,
    bool? isPlaying,
    bool? isLoading,
    double? playbackSpeed,
  }) {
    return VoicePlaybackState(
      currentMessageId: currentMessageId ?? this.currentMessageId,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      isPlaying: isPlaying ?? this.isPlaying,
      isLoading: isLoading ?? this.isLoading,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
    );
  }
}

/// Voice message analytics data
class VoiceMessageAnalytics {
  final String messageId;
  final Duration totalDuration;
  final Duration listenedDuration;
  final int playCount;
  final DateTime firstPlayedAt;
  final DateTime lastPlayedAt;
  final double completionRate;

  const VoiceMessageAnalytics({
    required this.messageId,
    required this.totalDuration,
    required this.listenedDuration,
    required this.playCount,
    required this.firstPlayedAt,
    required this.lastPlayedAt,
    required this.completionRate,
  });

  Map<String, dynamic> toJson() {
    return {
      'messageId': messageId,
      'totalDuration': totalDuration.inMilliseconds,
      'listenedDuration': listenedDuration.inMilliseconds,
      'playCount': playCount,
      'firstPlayedAt': Timestamp.fromDate(firstPlayedAt),
      'lastPlayedAt': Timestamp.fromDate(lastPlayedAt),
      'completionRate': completionRate,
    };
  }

  factory VoiceMessageAnalytics.fromJson(Map<String, dynamic> json) {
    return VoiceMessageAnalytics(
      messageId: json['messageId'] ?? '',
      totalDuration: Duration(milliseconds: json['totalDuration'] ?? 0),
      listenedDuration: Duration(milliseconds: json['listenedDuration'] ?? 0),
      playCount: json['playCount'] ?? 0,
      firstPlayedAt: json['firstPlayedAt'] is Timestamp
          ? (json['firstPlayedAt'] as Timestamp).toDate()
          : DateTime.now(),
      lastPlayedAt: json['lastPlayedAt'] is Timestamp
          ? (json['lastPlayedAt'] as Timestamp).toDate()
          : DateTime.now(),
      completionRate: json['completionRate']?.toDouble() ?? 0.0,
    );
  }
}
