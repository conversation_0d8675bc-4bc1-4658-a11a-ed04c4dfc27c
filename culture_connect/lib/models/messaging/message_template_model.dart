import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for message templates and quick replies
class MessageTemplateModel {
  final String id;
  final String userId;
  final String name;
  final String content;
  final String? description;
  final MessageTemplateCategory category;
  final List<String> tags;
  final int usageCount;
  final DateTime createdAt;
  final DateTime lastUsedAt;
  final bool isActive;
  final bool isPublic;
  final Map<String, String>? variables; // For template variables like {name}, {date}
  final Map<String, dynamic>? metadata;

  const MessageTemplateModel({
    required this.id,
    required this.userId,
    required this.name,
    required this.content,
    this.description,
    required this.category,
    this.tags = const [],
    this.usageCount = 0,
    required this.createdAt,
    required this.lastUsedAt,
    this.isActive = true,
    this.isPublic = false,
    this.variables,
    this.metadata,
  });

  /// Create from JSON (Firestore)
  factory MessageTemplateModel.fromJson(Map<String, dynamic> json) {
    return MessageTemplateModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      name: json['name'] ?? '',
      content: json['content'] ?? '',
      description: json['description'],
      category: MessageTemplateCategory.values.firstWhere(
        (e) => e.toString().split('.').last == json['category'],
        orElse: () => MessageTemplateCategory.general,
      ),
      tags: List<String>.from(json['tags'] ?? []),
      usageCount: json['usageCount'] ?? 0,
      createdAt: (json['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastUsedAt: (json['lastUsedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isActive: json['isActive'] ?? true,
      isPublic: json['isPublic'] ?? false,
      variables: json['variables'] != null 
          ? Map<String, String>.from(json['variables']) 
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'content': content,
      'description': description,
      'category': category.toString().split('.').last,
      'tags': tags,
      'usageCount': usageCount,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastUsedAt': Timestamp.fromDate(lastUsedAt),
      'isActive': isActive,
      'isPublic': isPublic,
      'variables': variables,
      'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  MessageTemplateModel copyWith({
    String? id,
    String? userId,
    String? name,
    String? content,
    String? description,
    MessageTemplateCategory? category,
    List<String>? tags,
    int? usageCount,
    DateTime? createdAt,
    DateTime? lastUsedAt,
    bool? isActive,
    bool? isPublic,
    Map<String, String>? variables,
    Map<String, dynamic>? metadata,
  }) {
    return MessageTemplateModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      content: content ?? this.content,
      description: description ?? this.description,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      usageCount: usageCount ?? this.usageCount,
      createdAt: createdAt ?? this.createdAt,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
      isActive: isActive ?? this.isActive,
      isPublic: isPublic ?? this.isPublic,
      variables: variables ?? this.variables,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Process template content with variables
  String processContent(Map<String, String>? variableValues) {
    if (variables == null || variableValues == null) return content;
    
    String processedContent = content;
    for (final entry in variables!.entries) {
      final placeholder = '{${entry.key}}';
      final value = variableValues[entry.key] ?? entry.value;
      processedContent = processedContent.replaceAll(placeholder, value);
    }
    return processedContent;
  }

  /// Get list of variables in template
  List<String> get templateVariables {
    if (variables == null) return [];
    return variables!.keys.toList();
  }

  /// Check if template has variables
  bool get hasVariables {
    return variables != null && variables!.isNotEmpty;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageTemplateModel &&
        other.id == id &&
        other.userId == userId;
  }

  @override
  int get hashCode => Object.hash(id, userId);

  @override
  String toString() {
    return 'MessageTemplateModel(id: $id, name: $name, category: $category)';
  }
}

/// Categories for message templates
enum MessageTemplateCategory {
  general,
  greeting,
  farewell,
  business,
  travel,
  emergency,
  social,
  custom,
}

/// Extension for MessageTemplateCategory
extension MessageTemplateCategoryExtension on MessageTemplateCategory {
  String get displayName {
    switch (this) {
      case MessageTemplateCategory.general:
        return 'General';
      case MessageTemplateCategory.greeting:
        return 'Greetings';
      case MessageTemplateCategory.farewell:
        return 'Farewells';
      case MessageTemplateCategory.business:
        return 'Business';
      case MessageTemplateCategory.travel:
        return 'Travel';
      case MessageTemplateCategory.emergency:
        return 'Emergency';
      case MessageTemplateCategory.social:
        return 'Social';
      case MessageTemplateCategory.custom:
        return 'Custom';
    }
  }

  String get description {
    switch (this) {
      case MessageTemplateCategory.general:
        return 'General purpose templates';
      case MessageTemplateCategory.greeting:
        return 'Greeting and welcome messages';
      case MessageTemplateCategory.farewell:
        return 'Goodbye and farewell messages';
      case MessageTemplateCategory.business:
        return 'Business and professional messages';
      case MessageTemplateCategory.travel:
        return 'Travel and tourism related messages';
      case MessageTemplateCategory.emergency:
        return 'Emergency and urgent messages';
      case MessageTemplateCategory.social:
        return 'Social and casual messages';
      case MessageTemplateCategory.custom:
        return 'Custom user-created templates';
    }
  }

  String get emoji {
    switch (this) {
      case MessageTemplateCategory.general:
        return '💬';
      case MessageTemplateCategory.greeting:
        return '👋';
      case MessageTemplateCategory.farewell:
        return '👋';
      case MessageTemplateCategory.business:
        return '💼';
      case MessageTemplateCategory.travel:
        return '✈️';
      case MessageTemplateCategory.emergency:
        return '🚨';
      case MessageTemplateCategory.social:
        return '😊';
      case MessageTemplateCategory.custom:
        return '⚙️';
    }
  }
}

/// Model for quick reply suggestions
class QuickReplyModel {
  final String id;
  final String text;
  final String? emoji;
  final MessageTemplateCategory category;
  final int priority;
  final bool isContextual;
  final List<String> contextKeywords;
  final Map<String, dynamic>? metadata;

  const QuickReplyModel({
    required this.id,
    required this.text,
    this.emoji,
    required this.category,
    this.priority = 0,
    this.isContextual = false,
    this.contextKeywords = const [],
    this.metadata,
  });

  /// Create from JSON
  factory QuickReplyModel.fromJson(Map<String, dynamic> json) {
    return QuickReplyModel(
      id: json['id'] ?? '',
      text: json['text'] ?? '',
      emoji: json['emoji'],
      category: MessageTemplateCategory.values.firstWhere(
        (e) => e.toString().split('.').last == json['category'],
        orElse: () => MessageTemplateCategory.general,
      ),
      priority: json['priority'] ?? 0,
      isContextual: json['isContextual'] ?? false,
      contextKeywords: List<String>.from(json['contextKeywords'] ?? []),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'emoji': emoji,
      'category': category.toString().split('.').last,
      'priority': priority,
      'isContextual': isContextual,
      'contextKeywords': contextKeywords,
      'metadata': metadata,
    };
  }

  /// Get display text with emoji
  String get displayText {
    return emoji != null ? '$emoji $text' : text;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuickReplyModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'QuickReplyModel(id: $id, text: $text)';
  }
}
