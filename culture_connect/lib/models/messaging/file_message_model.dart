/// Model for file messages in chat
class FileMessageModel {
  final String id;
  final String messageId;
  final String fileName;
  final String fileUrl;
  final String? localPath;
  final String? thumbnailUrl;
  final String? localThumbnailPath;
  final int fileSize;
  final String mimeType;
  final FileType fileType;
  final FileMessageStatus status;
  final DateTime createdAt;
  final DateTime? downloadedAt;
  final int downloadCount;
  final FilePermissions permissions;
  final Map<String, dynamic>? metadata;

  const FileMessageModel({
    required this.id,
    required this.messageId,
    required this.fileName,
    required this.fileUrl,
    this.localPath,
    this.thumbnailUrl,
    this.localThumbnailPath,
    required this.fileSize,
    required this.mimeType,
    required this.fileType,
    required this.status,
    required this.createdAt,
    this.downloadedAt,
    this.downloadCount = 0,
    required this.permissions,
    this.metadata,
  });

  /// Create a copy with updated properties
  FileMessageModel copyWith({
    String? id,
    String? messageId,
    String? fileName,
    String? fileUrl,
    String? localPath,
    String? thumbnailUrl,
    String? localThumbnailPath,
    int? fileSize,
    String? mimeType,
    FileType? fileType,
    FileMessageStatus? status,
    DateTime? createdAt,
    DateTime? downloadedAt,
    int? downloadCount,
    FilePermissions? permissions,
    Map<String, dynamic>? metadata,
  }) {
    return FileMessageModel(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      fileName: fileName ?? this.fileName,
      fileUrl: fileUrl ?? this.fileUrl,
      localPath: localPath ?? this.localPath,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      localThumbnailPath: localThumbnailPath ?? this.localThumbnailPath,
      fileSize: fileSize ?? this.fileSize,
      mimeType: mimeType ?? this.mimeType,
      fileType: fileType ?? this.fileType,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      downloadedAt: downloadedAt ?? this.downloadedAt,
      downloadCount: downloadCount ?? this.downloadCount,
      permissions: permissions ?? this.permissions,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert to JSON (for Firestore)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'fileName': fileName,
      'fileUrl': fileUrl,
      'localPath': localPath,
      'thumbnailUrl': thumbnailUrl,
      'localThumbnailPath': localThumbnailPath,
      'fileSize': fileSize,
      'mimeType': mimeType,
      'fileType': fileType.toString().split('.').last,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'downloadedAt': downloadedAt?.millisecondsSinceEpoch,
      'downloadCount': downloadCount,
      'permissions': permissions.toJson(),
      'metadata': metadata,
    };
  }

  /// Create from JSON (Firestore)
  factory FileMessageModel.fromJson(Map<String, dynamic> json) {
    return FileMessageModel(
      id: json['id'] ?? '',
      messageId: json['messageId'] ?? '',
      fileName: json['fileName'] ?? '',
      fileUrl: json['fileUrl'] ?? '',
      localPath: json['localPath'],
      thumbnailUrl: json['thumbnailUrl'],
      localThumbnailPath: json['localThumbnailPath'],
      fileSize: json['fileSize'] ?? 0,
      mimeType: json['mimeType'] ?? 'application/octet-stream',
      fileType: FileType.values.firstWhere(
        (e) => e.toString().split('.').last == json['fileType'],
        orElse: () => FileType.other,
      ),
      status: FileMessageStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => FileMessageStatus.pending,
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] ?? 0),
      downloadedAt: json['downloadedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['downloadedAt'])
          : null,
      downloadCount: json['downloadCount'] ?? 0,
      permissions: FilePermissions.fromJson(json['permissions'] ?? {}),
      metadata: json['metadata'],
    );
  }

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024)
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    if (fileSize < 1024 * 1024 * 1024)
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// Get file extension
  String get fileExtension {
    final parts = fileName.split('.');
    return parts.length > 1 ? parts.last.toLowerCase() : '';
  }

  /// Check if file is locally available
  bool get isLocallyAvailable => localPath != null && localPath!.isNotEmpty;

  /// Check if file has thumbnail
  bool get hasThumbnail => thumbnailUrl != null || localThumbnailPath != null;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FileMessageModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// File message status enumeration
enum FileMessageStatus {
  pending,
  uploading,
  uploaded,
  downloading,
  downloaded,
  failed,
  expired,
}

/// File type enumeration
enum FileType {
  image,
  video,
  audio,
  document,
  archive,
  code,
  other,
}

/// File permissions model
class FilePermissions {
  final bool canDownload;
  final bool canShare;
  final bool canPreview;
  final bool canDelete;
  final DateTime? expiresAt;
  final List<String> allowedUsers;
  final int maxDownloads;

  const FilePermissions({
    this.canDownload = true,
    this.canShare = true,
    this.canPreview = true,
    this.canDelete = false,
    this.expiresAt,
    this.allowedUsers = const [],
    this.maxDownloads = -1, // -1 means unlimited
  });

  /// Create a copy with updated properties
  FilePermissions copyWith({
    bool? canDownload,
    bool? canShare,
    bool? canPreview,
    bool? canDelete,
    DateTime? expiresAt,
    List<String>? allowedUsers,
    int? maxDownloads,
  }) {
    return FilePermissions(
      canDownload: canDownload ?? this.canDownload,
      canShare: canShare ?? this.canShare,
      canPreview: canPreview ?? this.canPreview,
      canDelete: canDelete ?? this.canDelete,
      expiresAt: expiresAt ?? this.expiresAt,
      allowedUsers: allowedUsers ?? this.allowedUsers,
      maxDownloads: maxDownloads ?? this.maxDownloads,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'canDownload': canDownload,
      'canShare': canShare,
      'canPreview': canPreview,
      'canDelete': canDelete,
      'expiresAt': expiresAt?.millisecondsSinceEpoch,
      'allowedUsers': allowedUsers,
      'maxDownloads': maxDownloads,
    };
  }

  /// Create from JSON
  factory FilePermissions.fromJson(Map<String, dynamic> json) {
    return FilePermissions(
      canDownload: json['canDownload'] ?? true,
      canShare: json['canShare'] ?? true,
      canPreview: json['canPreview'] ?? true,
      canDelete: json['canDelete'] ?? false,
      expiresAt: json['expiresAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['expiresAt'])
          : null,
      allowedUsers: List<String>.from(json['allowedUsers'] ?? []),
      maxDownloads: json['maxDownloads'] ?? -1,
    );
  }

  /// Check if file is expired
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);

  /// Check if user has access
  bool hasAccess(String userId) {
    if (allowedUsers.isEmpty) return true;
    return allowedUsers.contains(userId);
  }

  /// Check if download limit is reached
  bool isDownloadLimitReached(int currentDownloads) {
    if (maxDownloads == -1) return false;
    return currentDownloads >= maxDownloads;
  }
}

/// File type helper utilities
class FileTypeUtils {
  static const Map<String, FileType> _extensionMap = {
    // Images
    'jpg': FileType.image,
    'jpeg': FileType.image,
    'png': FileType.image,
    'gif': FileType.image,
    'webp': FileType.image,
    'bmp': FileType.image,
    'svg': FileType.image,
    'heic': FileType.image,
    'heif': FileType.image,

    // Videos
    'mp4': FileType.video,
    'avi': FileType.video,
    'mov': FileType.video,
    'wmv': FileType.video,
    'flv': FileType.video,
    'webm': FileType.video,
    'mkv': FileType.video,
    'm4v': FileType.video,

    // Audio
    'mp3': FileType.audio,
    'wav': FileType.audio,
    'flac': FileType.audio,
    'aac': FileType.audio,
    'ogg': FileType.audio,
    'm4a': FileType.audio,
    'wma': FileType.audio,

    // Documents
    'pdf': FileType.document,
    'doc': FileType.document,
    'docx': FileType.document,
    'xls': FileType.document,
    'xlsx': FileType.document,
    'ppt': FileType.document,
    'pptx': FileType.document,
    'txt': FileType.document,
    'rtf': FileType.document,
    'odt': FileType.document,
    'ods': FileType.document,
    'odp': FileType.document,

    // Archives
    'zip': FileType.archive,
    'rar': FileType.archive,
    '7z': FileType.archive,
    'tar': FileType.archive,
    'gz': FileType.archive,
    'bz2': FileType.archive,

    // Code
    'js': FileType.code,
    'html': FileType.code,
    'css': FileType.code,
    'dart': FileType.code,
    'java': FileType.code,
    'py': FileType.code,
    'cpp': FileType.code,
    'c': FileType.code,
    'json': FileType.code,
    'xml': FileType.code,
    'yaml': FileType.code,
    'yml': FileType.code,
  };

  /// Get file type from extension
  static FileType getFileTypeFromExtension(String extension) {
    final ext = extension.toLowerCase().replaceAll('.', '');
    return _extensionMap[ext] ?? FileType.other;
  }

  /// Get file type from filename
  static FileType getFileTypeFromFilename(String filename) {
    final parts = filename.split('.');
    if (parts.length > 1) {
      return getFileTypeFromExtension(parts.last);
    }
    return FileType.other;
  }

  /// Get file type from MIME type
  static FileType getFileTypeFromMimeType(String mimeType) {
    if (mimeType.startsWith('image/')) return FileType.image;
    if (mimeType.startsWith('video/')) return FileType.video;
    if (mimeType.startsWith('audio/')) return FileType.audio;
    if (mimeType.startsWith('text/') ||
        mimeType.contains('document') ||
        mimeType.contains('pdf')) return FileType.document;
    if (mimeType.contains('zip') ||
        mimeType.contains('archive') ||
        mimeType.contains('compressed')) return FileType.archive;
    return FileType.other;
  }
}
