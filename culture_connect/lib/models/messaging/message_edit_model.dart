import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for message editing and edit history
class MessageEditModel {
  final String id;
  final String messageId;
  final String chatId;
  final String editorUserId;
  final String originalText;
  final String editedText;
  final DateTime editedAt;
  final String? editReason;
  final Map<String, dynamic>? metadata;

  const MessageEditModel({
    required this.id,
    required this.messageId,
    required this.chatId,
    required this.editorUserId,
    required this.originalText,
    required this.editedText,
    required this.editedAt,
    this.editReason,
    this.metadata,
  });

  /// Create from JSON (Firestore)
  factory MessageEditModel.fromJson(Map<String, dynamic> json) {
    return MessageEditModel(
      id: json['id'] ?? '',
      messageId: json['messageId'] ?? '',
      chatId: json['chatId'] ?? '',
      editorUserId: json['editorUserId'] ?? '',
      originalText: json['originalText'] ?? '',
      editedText: json['editedText'] ?? '',
      editedAt: (json['editedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      editReason: json['editReason'],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'chatId': chatId,
      'editorUserId': editorUserId,
      'originalText': originalText,
      'editedText': editedText,
      'editedAt': Timestamp.fromDate(editedAt),
      'editReason': editReason,
      'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  MessageEditModel copyWith({
    String? id,
    String? messageId,
    String? chatId,
    String? editorUserId,
    String? originalText,
    String? editedText,
    DateTime? editedAt,
    String? editReason,
    Map<String, dynamic>? metadata,
  }) {
    return MessageEditModel(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      chatId: chatId ?? this.chatId,
      editorUserId: editorUserId ?? this.editorUserId,
      originalText: originalText ?? this.originalText,
      editedText: editedText ?? this.editedText,
      editedAt: editedAt ?? this.editedAt,
      editReason: editReason ?? this.editReason,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageEditModel &&
        other.id == id &&
        other.messageId == messageId;
  }

  @override
  int get hashCode => Object.hash(id, messageId);

  @override
  String toString() {
    return 'MessageEditModel(id: $id, messageId: $messageId, editedAt: $editedAt)';
  }
}

/// Model for message deletion
class MessageDeletionModel {
  final String id;
  final String messageId;
  final String chatId;
  final String deletedByUserId;
  final DateTime deletedAt;
  final MessageDeletionType deletionType;
  final String? deletionReason;
  final bool isRecoverable;
  final DateTime? recoverableUntil;
  final Map<String, dynamic>? metadata;

  const MessageDeletionModel({
    required this.id,
    required this.messageId,
    required this.chatId,
    required this.deletedByUserId,
    required this.deletedAt,
    required this.deletionType,
    this.deletionReason,
    this.isRecoverable = true,
    this.recoverableUntil,
    this.metadata,
  });

  /// Create from JSON (Firestore)
  factory MessageDeletionModel.fromJson(Map<String, dynamic> json) {
    return MessageDeletionModel(
      id: json['id'] ?? '',
      messageId: json['messageId'] ?? '',
      chatId: json['chatId'] ?? '',
      deletedByUserId: json['deletedByUserId'] ?? '',
      deletedAt: (json['deletedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deletionType: MessageDeletionType.values.firstWhere(
        (e) => e.toString().split('.').last == json['deletionType'],
        orElse: () => MessageDeletionType.soft,
      ),
      deletionReason: json['deletionReason'],
      isRecoverable: json['isRecoverable'] ?? true,
      recoverableUntil: (json['recoverableUntil'] as Timestamp?)?.toDate(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'chatId': chatId,
      'deletedByUserId': deletedByUserId,
      'deletedAt': Timestamp.fromDate(deletedAt),
      'deletionType': deletionType.toString().split('.').last,
      'deletionReason': deletionReason,
      'isRecoverable': isRecoverable,
      'recoverableUntil': recoverableUntil != null 
          ? Timestamp.fromDate(recoverableUntil!) 
          : null,
      'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  MessageDeletionModel copyWith({
    String? id,
    String? messageId,
    String? chatId,
    String? deletedByUserId,
    DateTime? deletedAt,
    MessageDeletionType? deletionType,
    String? deletionReason,
    bool? isRecoverable,
    DateTime? recoverableUntil,
    Map<String, dynamic>? metadata,
  }) {
    return MessageDeletionModel(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      chatId: chatId ?? this.chatId,
      deletedByUserId: deletedByUserId ?? this.deletedByUserId,
      deletedAt: deletedAt ?? this.deletedAt,
      deletionType: deletionType ?? this.deletionType,
      deletionReason: deletionReason ?? this.deletionReason,
      isRecoverable: isRecoverable ?? this.isRecoverable,
      recoverableUntil: recoverableUntil ?? this.recoverableUntil,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if message can still be recovered
  bool get canRecover {
    if (!isRecoverable) return false;
    if (recoverableUntil == null) return true;
    return DateTime.now().isBefore(recoverableUntil!);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageDeletionModel &&
        other.id == id &&
        other.messageId == messageId;
  }

  @override
  int get hashCode => Object.hash(id, messageId);

  @override
  String toString() {
    return 'MessageDeletionModel(id: $id, messageId: $messageId, deletionType: $deletionType)';
  }
}

/// Types of message deletion
enum MessageDeletionType {
  soft,     // Message hidden but recoverable
  hard,     // Message permanently deleted
  admin,    // Deleted by admin/moderator
  system,   // Deleted by system (e.g., policy violation)
}

/// Extension for MessageDeletionType
extension MessageDeletionTypeExtension on MessageDeletionType {
  String get displayName {
    switch (this) {
      case MessageDeletionType.soft:
        return 'Deleted';
      case MessageDeletionType.hard:
        return 'Permanently Deleted';
      case MessageDeletionType.admin:
        return 'Removed by Admin';
      case MessageDeletionType.system:
        return 'Removed by System';
    }
  }

  String get description {
    switch (this) {
      case MessageDeletionType.soft:
        return 'Message deleted but can be recovered';
      case MessageDeletionType.hard:
        return 'Message permanently deleted and cannot be recovered';
      case MessageDeletionType.admin:
        return 'Message removed by administrator';
      case MessageDeletionType.system:
        return 'Message removed by system for policy violation';
    }
  }

  bool get isRecoverable {
    switch (this) {
      case MessageDeletionType.soft:
        return true;
      case MessageDeletionType.hard:
      case MessageDeletionType.admin:
      case MessageDeletionType.system:
        return false;
    }
  }
}
