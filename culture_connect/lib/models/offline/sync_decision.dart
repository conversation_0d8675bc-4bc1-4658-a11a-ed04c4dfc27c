import 'package:flutter/material.dart';

/// Enum for sync priority
enum SyncPriority {
  /// Low priority sync
  low,
  
  /// Normal priority sync
  normal,
  
  /// High priority sync
  high,
  
  /// Critical priority sync
  critical,
}

/// Extension for sync priority
extension SyncPriorityExtension on SyncPriority {
  /// Get the display name for the sync priority
  String get displayName {
    switch (this) {
      case SyncPriority.low:
        return 'Low';
      case SyncPriority.normal:
        return 'Normal';
      case SyncPriority.high:
        return 'High';
      case SyncPriority.critical:
        return 'Critical';
    }
  }
  
  /// Get the color for the sync priority
  Color get color {
    switch (this) {
      case SyncPriority.low:
        return Colors.grey;
      case SyncPriority.normal:
        return Colors.blue;
      case SyncPriority.high:
        return Colors.orange;
      case SyncPriority.critical:
        return Colors.red;
    }
  }
  
  /// Get the numeric value for priority comparison
  int get value {
    switch (this) {
      case SyncPriority.low:
        return 1;
      case SyncPriority.normal:
        return 2;
      case SyncPriority.high:
        return 3;
      case SyncPriority.critical:
        return 4;
    }
  }
}

/// A class representing a sync decision
class SyncDecision {
  /// Whether sync should proceed
  final bool shouldSync;
  
  /// The priority of the sync
  final SyncPriority priority;
  
  /// The reason for the decision
  final String reason;
  
  /// Creates a new sync decision
  const SyncDecision({
    required this.shouldSync,
    this.priority = SyncPriority.normal,
    required this.reason,
  });
  
  /// Create a sync decision from JSON
  factory SyncDecision.fromJson(Map<String, dynamic> json) {
    return SyncDecision(
      shouldSync: json['shouldSync'] as bool,
      priority: SyncPriority.values.firstWhere(
        (priority) => priority.name == json['priority'],
        orElse: () => SyncPriority.normal,
      ),
      reason: json['reason'] as String,
    );
  }
  
  /// Convert this sync decision to JSON
  Map<String, dynamic> toJson() {
    return {
      'shouldSync': shouldSync,
      'priority': priority.name,
      'reason': reason,
    };
  }
  
  @override
  String toString() {
    return 'SyncDecision(shouldSync: $shouldSync, priority: ${priority.displayName}, reason: $reason)';
  }
}

/// A class representing a sync result
class SyncResult {
  /// Whether the sync was successful
  final bool success;
  
  /// Number of items processed
  final int itemsProcessed;
  
  /// Number of items failed
  final int itemsFailed;
  
  /// Error message if sync failed
  final String? errorMessage;
  
  /// Duration of the sync operation
  final Duration duration;
  
  /// Timestamp when sync completed
  final DateTime completedAt;
  
  /// Creates a new sync result
  const SyncResult({
    required this.success,
    required this.itemsProcessed,
    required this.itemsFailed,
    this.errorMessage,
    required this.duration,
    required this.completedAt,
  });
  
  /// Create a successful sync result
  factory SyncResult.success({
    required int itemsProcessed,
    required Duration duration,
  }) {
    return SyncResult(
      success: true,
      itemsProcessed: itemsProcessed,
      itemsFailed: 0,
      duration: duration,
      completedAt: DateTime.now(),
    );
  }
  
  /// Create a failed sync result
  factory SyncResult.failure({
    required String errorMessage,
    int itemsProcessed = 0,
    int itemsFailed = 0,
    required Duration duration,
  }) {
    return SyncResult(
      success: false,
      itemsProcessed: itemsProcessed,
      itemsFailed: itemsFailed,
      errorMessage: errorMessage,
      duration: duration,
      completedAt: DateTime.now(),
    );
  }
  
  /// Create a sync result from JSON
  factory SyncResult.fromJson(Map<String, dynamic> json) {
    return SyncResult(
      success: json['success'] as bool,
      itemsProcessed: json['itemsProcessed'] as int,
      itemsFailed: json['itemsFailed'] as int,
      errorMessage: json['errorMessage'] as String?,
      duration: Duration(milliseconds: json['durationMs'] as int),
      completedAt: DateTime.parse(json['completedAt'] as String),
    );
  }
  
  /// Convert this sync result to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'itemsProcessed': itemsProcessed,
      'itemsFailed': itemsFailed,
      'errorMessage': errorMessage,
      'durationMs': duration.inMilliseconds,
      'completedAt': completedAt.toIso8601String(),
    };
  }
  
  /// Get the total number of items
  int get totalItems => itemsProcessed + itemsFailed;
  
  /// Get the success rate as a percentage
  double get successRate {
    if (totalItems == 0) return 0.0;
    return (itemsProcessed / totalItems) * 100;
  }
  
  @override
  String toString() {
    return 'SyncResult(success: $success, processed: $itemsProcessed, failed: $itemsFailed, duration: ${duration.inSeconds}s)';
  }
}
