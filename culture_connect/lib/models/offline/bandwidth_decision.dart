import 'package:flutter/material.dart';

/// Enum for bandwidth actions
enum BandwidthAction {
  /// Proceed with the operation
  proceed,
  
  /// Wait for WiFi connection
  waitForWifi,
  
  /// Wait for better connection
  waitForConnection,
  
  /// Defer the operation
  defer,
  
  /// Cancel the operation
  cancel,
}

/// Extension for bandwidth action
extension BandwidthActionExtension on BandwidthAction {
  /// Get the display name for the bandwidth action
  String get displayName {
    switch (this) {
      case BandwidthAction.proceed:
        return 'Proceed';
      case BandwidthAction.waitForWifi:
        return 'Wait for WiFi';
      case BandwidthAction.waitForConnection:
        return 'Wait for Connection';
      case BandwidthAction.defer:
        return 'Defer';
      case BandwidthAction.cancel:
        return 'Cancel';
    }
  }
}

/// Enum for bandwidth priority
enum BandwidthPriority {
  /// Low priority
  low,
  
  /// Normal priority
  normal,
  
  /// High priority
  high,
  
  /// Critical priority
  critical,
}

/// Extension for bandwidth priority
extension BandwidthPriorityExtension on BandwidthPriority {
  /// Get the display name for the bandwidth priority
  String get displayName {
    switch (this) {
      case BandwidthPriority.low:
        return 'Low';
      case BandwidthPriority.normal:
        return 'Normal';
      case BandwidthPriority.high:
        return 'High';
      case BandwidthPriority.critical:
        return 'Critical';
    }
  }
  
  /// Get the color for the bandwidth priority
  Color get color {
    switch (this) {
      case BandwidthPriority.low:
        return Colors.grey;
      case BandwidthPriority.normal:
        return Colors.blue;
      case BandwidthPriority.high:
        return Colors.orange;
      case BandwidthPriority.critical:
        return Colors.red;
    }
  }
}

/// A class representing a bandwidth decision
class BandwidthDecision {
  /// Whether the operation is allowed
  final bool allowed;
  
  /// The reason for the decision
  final String reason;
  
  /// The recommended action
  final BandwidthAction recommendedAction;
  
  /// The priority of the operation
  final BandwidthPriority? priority;
  
  /// Creates a new bandwidth decision
  const BandwidthDecision({
    required this.allowed,
    required this.reason,
    required this.recommendedAction,
    this.priority,
  });
  
  /// Create a bandwidth decision from JSON
  factory BandwidthDecision.fromJson(Map<String, dynamic> json) {
    return BandwidthDecision(
      allowed: json['allowed'] as bool,
      reason: json['reason'] as String,
      recommendedAction: BandwidthAction.values.firstWhere(
        (action) => action.name == json['recommendedAction'],
        orElse: () => BandwidthAction.defer,
      ),
      priority: json['priority'] != null
          ? BandwidthPriority.values.firstWhere(
              (priority) => priority.name == json['priority'],
              orElse: () => BandwidthPriority.normal,
            )
          : null,
    );
  }
  
  /// Convert this bandwidth decision to JSON
  Map<String, dynamic> toJson() {
    return {
      'allowed': allowed,
      'reason': reason,
      'recommendedAction': recommendedAction.name,
      'priority': priority?.name,
    };
  }
  
  @override
  String toString() {
    return 'BandwidthDecision(allowed: $allowed, reason: $reason, action: ${recommendedAction.displayName}, priority: ${priority?.displayName})';
  }
}

/// A class representing bandwidth limits
class BandwidthLimits {
  /// Daily WiFi limit in bytes
  final int wifiDailyLimit;
  
  /// Daily mobile data limit in bytes
  final int mobileDailyLimit;
  
  /// Daily roaming limit in bytes
  final int roamingDailyLimit;
  
  /// Creates new bandwidth limits
  const BandwidthLimits({
    required this.wifiDailyLimit,
    required this.mobileDailyLimit,
    required this.roamingDailyLimit,
  });
  
  /// Create bandwidth limits from JSON
  factory BandwidthLimits.fromJson(Map<String, dynamic> json) {
    return BandwidthLimits(
      wifiDailyLimit: json['wifiDailyLimit'] as int,
      mobileDailyLimit: json['mobileDailyLimit'] as int,
      roamingDailyLimit: json['roamingDailyLimit'] as int,
    );
  }
  
  /// Convert this bandwidth limits to JSON
  Map<String, dynamic> toJson() {
    return {
      'wifiDailyLimit': wifiDailyLimit,
      'mobileDailyLimit': mobileDailyLimit,
      'roamingDailyLimit': roamingDailyLimit,
    };
  }
  
  /// Create a copy of the bandwidth limits with updated values
  BandwidthLimits copyWith({
    int? wifiDailyLimit,
    int? mobileDailyLimit,
    int? roamingDailyLimit,
  }) {
    return BandwidthLimits(
      wifiDailyLimit: wifiDailyLimit ?? this.wifiDailyLimit,
      mobileDailyLimit: mobileDailyLimit ?? this.mobileDailyLimit,
      roamingDailyLimit: roamingDailyLimit ?? this.roamingDailyLimit,
    );
  }
}
