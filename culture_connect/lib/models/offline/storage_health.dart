import 'package:flutter/material.dart';

/// Enum for storage health levels
enum StorageHealthLevel {
  /// Excellent storage health
  excellent,
  
  /// Good storage health
  good,
  
  /// Fair storage health
  fair,
  
  /// Poor storage health
  poor,
  
  /// Critical storage health
  critical,
}

/// Extension for storage health level
extension StorageHealthLevelExtension on StorageHealthLevel {
  /// Get the display name for the storage health level
  String get displayName {
    switch (this) {
      case StorageHealthLevel.excellent:
        return 'Excellent';
      case StorageHealthLevel.good:
        return 'Good';
      case StorageHealthLevel.fair:
        return 'Fair';
      case StorageHealthLevel.poor:
        return 'Poor';
      case StorageHealthLevel.critical:
        return 'Critical';
    }
  }
  
  /// Get the color for the storage health level
  Color get color {
    switch (this) {
      case StorageHealthLevel.excellent:
        return Colors.green;
      case StorageHealthLevel.good:
        return Colors.lightGreen;
      case StorageHealthLevel.fair:
        return Colors.amber;
      case StorageHealthLevel.poor:
        return Colors.orange;
      case StorageHealthLevel.critical:
        return Colors.red;
    }
  }
  
  /// Get the icon for the storage health level
  IconData get icon {
    switch (this) {
      case StorageHealthLevel.excellent:
        return Icons.check_circle;
      case StorageHealthLevel.good:
        return Icons.check_circle_outline;
      case StorageHealthLevel.fair:
        return Icons.warning_amber;
      case StorageHealthLevel.poor:
        return Icons.warning;
      case StorageHealthLevel.critical:
        return Icons.error;
    }
  }
}

/// A class representing storage health status
class StorageHealthStatus {
  /// The overall health level
  final StorageHealthLevel level;
  
  /// The percentage of storage used
  final double usagePercentage;
  
  /// The available storage in bytes
  final int availableStorage;
  
  /// The total storage in bytes
  final int totalStorage;
  
  /// Health recommendations
  final List<String> recommendations;
  
  /// Whether cleanup is needed
  final bool needsCleanup;
  
  /// Creates a new storage health status
  const StorageHealthStatus({
    required this.level,
    required this.usagePercentage,
    required this.availableStorage,
    required this.totalStorage,
    this.recommendations = const [],
    this.needsCleanup = false,
  });
  
  /// Create storage health status from storage usage
  factory StorageHealthStatus.fromUsage({
    required int totalStorage,
    required int usedStorage,
    List<String>? recommendations,
  }) {
    final usagePercentage = (usedStorage / totalStorage) * 100;
    final availableStorage = totalStorage - usedStorage;
    
    StorageHealthLevel level;
    bool needsCleanup = false;
    List<String> defaultRecommendations = [];
    
    if (usagePercentage < 50) {
      level = StorageHealthLevel.excellent;
    } else if (usagePercentage < 70) {
      level = StorageHealthLevel.good;
    } else if (usagePercentage < 85) {
      level = StorageHealthLevel.fair;
      defaultRecommendations.add('Consider cleaning up old content');
    } else if (usagePercentage < 95) {
      level = StorageHealthLevel.poor;
      needsCleanup = true;
      defaultRecommendations.addAll([
        'Storage is running low',
        'Clean up old content to free space',
        'Consider reducing offline content',
      ]);
    } else {
      level = StorageHealthLevel.critical;
      needsCleanup = true;
      defaultRecommendations.addAll([
        'Storage is critically low',
        'Immediate cleanup required',
        'Some features may not work properly',
      ]);
    }
    
    return StorageHealthStatus(
      level: level,
      usagePercentage: usagePercentage,
      availableStorage: availableStorage,
      totalStorage: totalStorage,
      recommendations: recommendations ?? defaultRecommendations,
      needsCleanup: needsCleanup,
    );
  }
  
  /// Create storage health status from JSON
  factory StorageHealthStatus.fromJson(Map<String, dynamic> json) {
    return StorageHealthStatus(
      level: StorageHealthLevel.values.firstWhere(
        (level) => level.name == json['level'],
        orElse: () => StorageHealthLevel.good,
      ),
      usagePercentage: (json['usagePercentage'] as num).toDouble(),
      availableStorage: json['availableStorage'] as int,
      totalStorage: json['totalStorage'] as int,
      recommendations: List<String>.from(json['recommendations'] ?? []),
      needsCleanup: json['needsCleanup'] as bool? ?? false,
    );
  }
  
  /// Convert this storage health status to JSON
  Map<String, dynamic> toJson() {
    return {
      'level': level.name,
      'usagePercentage': usagePercentage,
      'availableStorage': availableStorage,
      'totalStorage': totalStorage,
      'recommendations': recommendations,
      'needsCleanup': needsCleanup,
    };
  }
  
  /// Get the used storage in bytes
  int get usedStorage => totalStorage - availableStorage;
  
  /// Get a formatted string for available storage
  String get availableStorageFormatted {
    if (availableStorage >= 1024 * 1024 * 1024) {
      return '${(availableStorage / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    } else if (availableStorage >= 1024 * 1024) {
      return '${(availableStorage / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else if (availableStorage >= 1024) {
      return '${(availableStorage / 1024).toStringAsFixed(1)} KB';
    } else {
      return '$availableStorage bytes';
    }
  }
  
  @override
  String toString() {
    return 'StorageHealthStatus(level: ${level.displayName}, usage: ${usagePercentage.toStringAsFixed(1)}%, available: $availableStorageFormatted)';
  }
}

/// A class representing storage cleanup result
class StorageCleanupResult {
  /// Whether the cleanup was successful
  final bool success;
  
  /// Amount of storage freed in bytes
  final int storageFreed;
  
  /// Number of items cleaned up
  final int itemsCleaned;
  
  /// Error message if cleanup failed
  final String? errorMessage;
  
  /// Duration of the cleanup operation
  final Duration duration;
  
  /// Creates a new storage cleanup result
  const StorageCleanupResult({
    required this.success,
    required this.storageFreed,
    required this.itemsCleaned,
    this.errorMessage,
    required this.duration,
  });
  
  /// Create a successful cleanup result
  factory StorageCleanupResult.success({
    required int storageFreed,
    required int itemsCleaned,
    required Duration duration,
  }) {
    return StorageCleanupResult(
      success: true,
      storageFreed: storageFreed,
      itemsCleaned: itemsCleaned,
      duration: duration,
    );
  }
  
  /// Create a failed cleanup result
  factory StorageCleanupResult.failure({
    required String errorMessage,
    required Duration duration,
  }) {
    return StorageCleanupResult(
      success: false,
      storageFreed: 0,
      itemsCleaned: 0,
      errorMessage: errorMessage,
      duration: duration,
    );
  }
  
  /// Get a formatted string for storage freed
  String get storageFreedFormatted {
    if (storageFreed >= 1024 * 1024 * 1024) {
      return '${(storageFreed / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    } else if (storageFreed >= 1024 * 1024) {
      return '${(storageFreed / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else if (storageFreed >= 1024) {
      return '${(storageFreed / 1024).toStringAsFixed(1)} KB';
    } else {
      return '$storageFreed bytes';
    }
  }
  
  @override
  String toString() {
    return 'StorageCleanupResult(success: $success, freed: $storageFreedFormatted, items: $itemsCleaned)';
  }
}

/// A class representing category cleanup result
class CategoryCleanupResult {
  /// The content type category
  final String category;
  
  /// Whether the cleanup was successful
  final bool success;
  
  /// Amount of storage freed in bytes
  final int storageFreed;
  
  /// Number of items cleaned up
  final int itemsCleaned;
  
  /// Error message if cleanup failed
  final String? errorMessage;
  
  /// Creates a new category cleanup result
  const CategoryCleanupResult({
    required this.category,
    required this.success,
    required this.storageFreed,
    required this.itemsCleaned,
    this.errorMessage,
  });
  
  /// Get a formatted string for storage freed
  String get storageFreedFormatted {
    if (storageFreed >= 1024 * 1024 * 1024) {
      return '${(storageFreed / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    } else if (storageFreed >= 1024 * 1024) {
      return '${(storageFreed / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else if (storageFreed >= 1024) {
      return '${(storageFreed / 1024).toStringAsFixed(1)} KB';
    } else {
      return '$storageFreed bytes';
    }
  }
  
  @override
  String toString() {
    return 'CategoryCleanupResult(category: $category, success: $success, freed: $storageFreedFormatted, items: $itemsCleaned)';
  }
}
