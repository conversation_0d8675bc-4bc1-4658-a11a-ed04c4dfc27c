/// Security settings model for auto-lock and authentication preferences
class SecuritySettings {
  final bool autoLockEnabled;
  final int autoLockTimeoutMinutes; // 0 = 30s, 1 = 1m, 5 = 5m, 15 = 15m, -1 = never
  final bool biometricEnabled;
  final bool requirePasswordFallback;
  final bool lockOnAppBackground;
  final bool showLockScreenPreview;

  const SecuritySettings({
    this.autoLockEnabled = true,
    this.autoLockTimeoutMinutes = 5, // Default 5 minutes
    this.biometricEnabled = true,
    this.requirePasswordFallback = true,
    this.lockOnAppBackground = true,
    this.showLockScreenPreview = false,
  });

  /// Get timeout duration from minutes setting
  Duration get timeoutDuration {
    switch (autoLockTimeoutMinutes) {
      case 0:
        return const Duration(seconds: 30);
      case 1:
        return const Duration(minutes: 1);
      case 5:
        return const Duration(minutes: 5);
      case 15:
        return const Duration(minutes: 15);
      case -1:
        return Duration.zero; // Never lock
      default:
        return const Duration(minutes: 5);
    }
  }

  /// Get display text for timeout setting
  String get timeoutDisplayText {
    switch (autoLockTimeoutMinutes) {
      case 0:
        return '30 seconds';
      case 1:
        return '1 minute';
      case 5:
        return '5 minutes';
      case 15:
        return '15 minutes';
      case -1:
        return 'Never';
      default:
        return '5 minutes';
    }
  }

  /// Available timeout options
  static const List<int> timeoutOptions = [0, 1, 5, 15, -1];

  /// Get display texts for all timeout options
  static List<String> get timeoutDisplayTexts {
    return timeoutOptions.map((minutes) {
      switch (minutes) {
        case 0:
          return '30 seconds';
        case 1:
          return '1 minute';
        case 5:
          return '5 minutes';
        case 15:
          return '15 minutes';
        case -1:
          return 'Never';
        default:
          return '5 minutes';
      }
    }).toList();
  }

  factory SecuritySettings.fromJson(Map<String, dynamic> json) {
    return SecuritySettings(
      autoLockEnabled: json['autoLockEnabled'] as bool? ?? true,
      autoLockTimeoutMinutes: json['autoLockTimeoutMinutes'] as int? ?? 5,
      biometricEnabled: json['biometricEnabled'] as bool? ?? true,
      requirePasswordFallback: json['requirePasswordFallback'] as bool? ?? true,
      lockOnAppBackground: json['lockOnAppBackground'] as bool? ?? true,
      showLockScreenPreview: json['showLockScreenPreview'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'autoLockEnabled': autoLockEnabled,
      'autoLockTimeoutMinutes': autoLockTimeoutMinutes,
      'biometricEnabled': biometricEnabled,
      'requirePasswordFallback': requirePasswordFallback,
      'lockOnAppBackground': lockOnAppBackground,
      'showLockScreenPreview': showLockScreenPreview,
    };
  }

  SecuritySettings copyWith({
    bool? autoLockEnabled,
    int? autoLockTimeoutMinutes,
    bool? biometricEnabled,
    bool? requirePasswordFallback,
    bool? lockOnAppBackground,
    bool? showLockScreenPreview,
  }) {
    return SecuritySettings(
      autoLockEnabled: autoLockEnabled ?? this.autoLockEnabled,
      autoLockTimeoutMinutes: autoLockTimeoutMinutes ?? this.autoLockTimeoutMinutes,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      requirePasswordFallback: requirePasswordFallback ?? this.requirePasswordFallback,
      lockOnAppBackground: lockOnAppBackground ?? this.lockOnAppBackground,
      showLockScreenPreview: showLockScreenPreview ?? this.showLockScreenPreview,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SecuritySettings &&
        other.autoLockEnabled == autoLockEnabled &&
        other.autoLockTimeoutMinutes == autoLockTimeoutMinutes &&
        other.biometricEnabled == biometricEnabled &&
        other.requirePasswordFallback == requirePasswordFallback &&
        other.lockOnAppBackground == lockOnAppBackground &&
        other.showLockScreenPreview == showLockScreenPreview;
  }

  @override
  int get hashCode {
    return Object.hash(
      autoLockEnabled,
      autoLockTimeoutMinutes,
      biometricEnabled,
      requirePasswordFallback,
      lockOnAppBackground,
      showLockScreenPreview,
    );
  }

  @override
  String toString() {
    return 'SecuritySettings('
        'autoLockEnabled: $autoLockEnabled, '
        'autoLockTimeoutMinutes: $autoLockTimeoutMinutes, '
        'biometricEnabled: $biometricEnabled, '
        'requirePasswordFallback: $requirePasswordFallback, '
        'lockOnAppBackground: $lockOnAppBackground, '
        'showLockScreenPreview: $showLockScreenPreview'
        ')';
  }
}
