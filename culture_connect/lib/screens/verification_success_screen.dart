import 'dart:async';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:confetti/confetti.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/auth_gradient_background.dart';
import 'package:culture_connect/widgets/curved_content_container.dart';
import 'package:culture_connect/widgets/custom_button.dart';

class VerificationSuccessScreen extends StatefulWidget {
  const VerificationSuccessScreen({super.key});

  @override
  State<VerificationSuccessScreen> createState() =>
      _VerificationSuccessScreenState();
}

class _VerificationSuccessScreenState extends State<VerificationSuccessScreen>
    with TickerProviderStateMixin {
  late ConfettiController _confettiController;
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  Timer? _redirectTimer;
  int _countdown = 5;
  bool _isRedirecting = false;

  @override
  void initState() {
    super.initState();

    // Initialize confetti controller
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 3));

    // Initialize animation controllers
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Create animations
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Start animations and confetti
    _startCelebration();

    // Start countdown timer
    _startCountdown();
  }

  void _startCelebration() {
    // Start confetti
    _confettiController.play();

    // Start scale animation
    _scaleController.forward();

    // Start fade animation with delay
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _fadeController.forward();
      }
    });
  }

  void _startCountdown() {
    _redirectTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        _countdown--;
      });

      if (_countdown <= 0) {
        timer.cancel();
        _navigateToLogin();
      }
    });
  }

  void _navigateToLogin() {
    if (!mounted || _isRedirecting) return;

    setState(() {
      _isRedirecting = true;
    });

    Navigator.pushReplacementNamed(context, '/login');
  }

  Widget _buildSuccessAnimation() {
    try {
      return Lottie.asset(
        'assets/animations/success_celebration.json',
        repeat: false,
        animate: true,
        errorBuilder: (context, error, stackTrace) {
          // Fallback to a simple animated checkmark icon
          return _buildFallbackAnimation();
        },
      );
    } catch (e) {
      return _buildFallbackAnimation();
    }
  }

  Widget _buildFallbackAnimation() {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppTheme.successColor.withAlpha(26),
      ),
      child: const Icon(
        Icons.check_circle,
        size: 120,
        color: AppTheme.successColor,
      ),
    );
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _scaleController.dispose();
    _fadeController.dispose();
    _redirectTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AuthGradientBackground(
        child: Stack(
          children: [
            // Confetti overlay
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirection: 1.5708, // radians for downward
                particleDrag: 0.05,
                emissionFrequency: 0.05,
                numberOfParticles: 50,
                gravity: 0.05,
                shouldLoop: false,
                colors: const [
                  AppTheme.primaryColor,
                  AppTheme.secondaryColor,
                  AppTheme.accentColor,
                  Colors.yellow,
                  Colors.pink,
                  Colors.orange,
                ],
              ),
            ),

            // Main content
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Spacer(),

                    // Success animation and content
                    SimpleCurvedContainer(
                      padding: const EdgeInsets.all(32.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Success animation (Lottie with fallback)
                          ScaleTransition(
                            scale: _scaleAnimation,
                            child: SizedBox(
                              height: 200,
                              width: 200,
                              child: _buildSuccessAnimation(),
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Success title
                          FadeTransition(
                            opacity: _fadeAnimation,
                            child: Text(
                              'Congratulations!',
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineMedium
                                  ?.copyWith(
                                    color: AppTheme.primaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Success message
                          FadeTransition(
                            opacity: _fadeAnimation,
                            child: Text(
                              'Your email has been successfully verified!\nYou can now access all features of CultureConnect.',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    color: AppTheme.textSecondaryColor,
                                    height: 1.5,
                                  ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          const SizedBox(height: 32),

                          // Countdown display
                          if (!_isRedirecting) ...[
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: AppTheme.primaryColor.withAlpha(26),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  'Redirecting to login in $_countdown seconds...',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: AppTheme.primaryColor,
                                        fontWeight: FontWeight.w500,
                                      ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 24),
                          ],

                          // Manual navigation button
                          FadeTransition(
                            opacity: _fadeAnimation,
                            child: CustomButton(
                              text: _isRedirecting
                                  ? 'Redirecting...'
                                  : 'Go to Login',
                              onPressed:
                                  _isRedirecting ? null : _navigateToLogin,
                              type: ButtonType.primary,
                              isLoading: _isRedirecting,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Spacer(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
