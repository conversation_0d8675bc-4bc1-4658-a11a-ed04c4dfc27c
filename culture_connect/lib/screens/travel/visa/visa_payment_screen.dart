import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/visa_models.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/travel/visa/visa_payment_service.dart';
import 'package:culture_connect/widgets/common/error_display.dart';
import 'package:culture_connect/widgets/payment/payment_method_selector.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/payment/provider_config_manager.dart';
import 'package:culture_connect/services/payment/payment_config_service.dart';
import 'package:culture_connect/services/payment/real_payment_api_service.dart';
import 'package:culture_connect/services/payment/payment_auth_service.dart';
import 'package:culture_connect/services/payment_providers/real_stripe_provider.dart';
import 'package:culture_connect/services/payment_providers/modern_paystack_provider.dart';
import 'package:culture_connect/services/payment_providers/busha_payment_provider.dart';

/// Screen for processing visa-related payments
class VisaPaymentScreen extends ConsumerStatefulWidget {
  final VisaApplication? application;
  final VisaServiceBooking? serviceBooking;
  final DocumentVerificationService? documentService;
  final String title;

  const VisaPaymentScreen({
    super.key,
    this.application,
    this.serviceBooking,
    this.documentService,
    required this.title,
  });

  @override
  ConsumerState<VisaPaymentScreen> createState() => _VisaPaymentScreenState();
}

class _VisaPaymentScreenState extends ConsumerState<VisaPaymentScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  PaymentProvider? _selectedPaymentMethod;
  bool _isProcessing = false;
  String? _errorMessage;
  bool _agreedToTerms = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentUserAsync = ref.watch(currentUserProvider);
    final currentUser = currentUserAsync.value;

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header section
                _buildHeaderSection(theme),

                const SizedBox(height: 24),

                // Payment summary
                _buildPaymentSummary(theme),

                const SizedBox(height: 24),

                // Payment method selection
                _buildPaymentMethodSection(theme),

                const SizedBox(height: 24),

                // Terms and conditions
                _buildTermsSection(theme),

                const SizedBox(height: 32),

                // Payment button
                _buildPaymentButton(theme, currentUser),

                const SizedBox(height: 16),

                // Error display
                if (_errorMessage != null) ...[
                  ErrorDisplay(
                    message: _errorMessage!,
                    showRetry: true,
                    onRetry: () => setState(() => _errorMessage = null),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primaryContainer,
            theme.colorScheme.primaryContainer.withAlpha(179),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Icon(
            Icons.payment,
            size: 32,
            color: theme.colorScheme.onPrimaryContainer,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Secure Payment',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: theme.colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Complete your payment securely',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onPrimaryContainer.withAlpha(204),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSummary(ThemeData theme) {
    final amount = _getPaymentAmount();
    final currency = _getPaymentCurrency();
    final description = _getPaymentDescription();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Payment Summary',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Service description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest.withAlpha(77),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    _getServiceIcon(),
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      description,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Amount breakdown
            _buildAmountBreakdown(theme, amount, currency),

            const Divider(height: 24),

            // Total amount
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Amount',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '$currency ${amount.toStringAsFixed(2)}',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountBreakdown(
      ThemeData theme, double amount, String currency) {
    if (widget.application != null) {
      final app = widget.application!;
      return Column(
        children: [
          _buildAmountRow(theme, 'Base Fee', app.baseFee, currency),
          if (app.serviceFee > 0) ...[
            const SizedBox(height: 8),
            _buildAmountRow(theme, 'Service Fee', app.serviceFee, currency),
          ],
          if (app.processingFee > 0) ...[
            const SizedBox(height: 8),
            _buildAmountRow(
                theme, 'Processing Fee', app.processingFee, currency),
          ],
        ],
      );
    } else {
      return _buildAmountRow(theme, 'Service Fee', amount, currency);
    }
  }

  Widget _buildAmountRow(
      ThemeData theme, String label, double amount, String currency) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          '$currency ${amount.toStringAsFixed(2)}',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentMethodSection(ThemeData theme) {
    // Payment method selection using PaymentMethodSelector widget

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.credit_card,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Payment Method',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Payment method selector
            PaymentMethodSelector(
              amount: widget.application?.totalFees ??
                  widget.serviceBooking?.totalAmount ??
                  widget.documentService?.price ??
                  0.0,
              currency: widget.application?.currency ??
                  widget.serviceBooking?.currency ??
                  widget.documentService?.currency ??
                  'USD',
              userCountryCode: 'NG', // TODO: Get from user location
              onProviderSelected: (provider) {
                setState(() {
                  _selectedPaymentMethod = provider;
                });
              },
              configManager: ProviderConfigManager(
                loggingService: LoggingService(),
                configService:
                    PaymentConfigService(loggingService: LoggingService()),
                apiService: RealPaymentApiService(
                  loggingService: LoggingService(),
                  configService:
                      PaymentConfigService(loggingService: LoggingService()),
                  authService: PaymentAuthService(
                    authService: ref.read(authServiceProvider),
                    loggingService: LoggingService(),
                  ),
                ),
                stripeProvider:
                    RealStripeProvider(loggingService: LoggingService()),
                paystackProvider:
                    ModernPaystackProvider(loggingService: LoggingService()),
                bushaProvider:
                    BushaPaymentProvider(loggingService: LoggingService()),
              ),
              loggingService: LoggingService(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTermsSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.gavel,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Terms & Conditions',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            CheckboxListTile(
              value: _agreedToTerms,
              onChanged: (value) {
                setState(() {
                  _agreedToTerms = value ?? false;
                });
              },
              title: Text(
                'I agree to the Terms of Service and Privacy Policy',
                style: theme.textTheme.bodyMedium,
              ),
              subtitle: Text(
                'By proceeding, you acknowledge that you have read and agree to our terms.',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentButton(ThemeData theme, dynamic user) {
    final isEnabled =
        _selectedPaymentMethod != null && _agreedToTerms && !_isProcessing;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: isEnabled ? _processPayment : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: _isProcessing
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.lock, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Pay Securely',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Future<void> _processPayment() async {
    if (!mounted) return;

    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    try {
      final visaPaymentService = ref.read(visaPaymentServiceProvider);
      final currentUserAsync = ref.read(currentUserProvider);
      final user = currentUserAsync.value;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      VisaPaymentResult result;

      // Convert PaymentProvider to PaymentMethodType
      PaymentMethodType? paymentMethodType;
      if (_selectedPaymentMethod != null) {
        switch (_selectedPaymentMethod!) {
          case PaymentProvider.stripe:
            paymentMethodType = PaymentMethodType.card;
            break;
          case PaymentProvider.paystack:
            paymentMethodType = PaymentMethodType.card;
            break;
          case PaymentProvider.busha:
            paymentMethodType = PaymentMethodType.crypto;
            break;
        }
      }

      if (widget.application != null) {
        result = await visaPaymentService.processVisaApplicationPayment(
          context: context,
          application: widget.application!,
          userEmail: user.email ?? '',
          userName: user.displayName ?? user.email ?? '',
          userPhone: user.phoneNumber ?? '',
          preferredMethod: paymentMethodType,
        );
      } else if (widget.serviceBooking != null) {
        // This would need the provider as well
        throw UnimplementedError('Service booking payment not implemented');
      } else if (widget.documentService != null) {
        result = await visaPaymentService.processDocumentVerificationPayment(
          context: context,
          service: widget.documentService!,
          userEmail: user.email ?? '',
          userName: user.displayName ?? user.email ?? '',
          userPhone: user.phoneNumber ?? '',
          preferredMethod: paymentMethodType,
        );
      } else {
        throw Exception('No payment item specified');
      }

      if (mounted) {
        if (result.success) {
          // Navigate to success screen
          Navigator.of(context).pushReplacementNamed(
            '/visa/payment/success',
            arguments: {
              'transactionReference': result.transactionReference,
              'receiptUrl': result.receiptUrl,
              'application': result.application,
              'serviceBooking': result.serviceBooking,
            },
          );
        } else {
          setState(() {
            _errorMessage = result.errorMessage ?? 'Payment failed';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Payment processing failed: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  double _getPaymentAmount() {
    if (widget.application != null) {
      return widget.application!.totalFees;
    } else if (widget.serviceBooking != null) {
      return widget.serviceBooking!.totalAmount;
    } else if (widget.documentService != null) {
      return widget.documentService!.price;
    }
    return 0.0;
  }

  String _getPaymentCurrency() {
    if (widget.application != null) {
      return widget.application!.currency;
    } else if (widget.serviceBooking != null) {
      return widget.serviceBooking!.currency;
    } else if (widget.documentService != null) {
      return widget.documentService!.currency;
    }
    return 'USD';
  }

  String _getPaymentDescription() {
    if (widget.application != null) {
      return 'Visa Application - ${widget.application!.destinationCountry}';
    } else if (widget.serviceBooking != null) {
      return widget.serviceBooking!.serviceName;
    } else if (widget.documentService != null) {
      return 'Document Verification - ${widget.documentService!.name}';
    }
    return 'Visa Service Payment';
  }

  IconData _getServiceIcon() {
    if (widget.application != null) {
      return Icons.flight_takeoff;
    } else if (widget.serviceBooking != null) {
      return Icons.business_center;
    } else if (widget.documentService != null) {
      return Icons.verified_user;
    }
    return Icons.payment;
  }
}
