import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/verification_model.dart';
import 'package:culture_connect/models/verification_document.dart';
import 'package:culture_connect/models/travel/document/visa_assistance_models.dart';
import 'package:culture_connect/services/verification_service.dart';
import 'package:culture_connect/widgets/travel/visa/document_upload_widget.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_display.dart';

/// Screen for managing document verification status and uploads
class DocumentVerificationScreen extends ConsumerStatefulWidget {
  final String? applicationId;
  final List<DocumentRequirement>? requirements;

  const DocumentVerificationScreen({
    super.key,
    this.applicationId,
    this.requirements,
  });

  @override
  ConsumerState<DocumentVerificationScreen> createState() =>
      _DocumentVerificationScreenState();
}

class _DocumentVerificationScreenState
    extends ConsumerState<DocumentVerificationScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  String? _errorMessage;
  List<VerificationDocument> _documents = [];
  List<VerificationRequest> _requests = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final verificationService = ref.read(verificationServiceProvider);

      // Load documents and requests
      final documents = await verificationService.getUserDocuments();
      final requests = await verificationService.getVerificationRequests();

      setState(() {
        _documents = documents;
        _requests = requests;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Document Verification'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Upload', icon: Icon(Icons.cloud_upload)),
            Tab(text: 'Status', icon: Icon(Icons.track_changes)),
            Tab(text: 'History', icon: Icon(Icons.history)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _errorMessage != null
              ? Center(
                  child: ErrorDisplay(
                    message: _errorMessage!,
                    showRetry: true,
                    onRetry: _loadData,
                  ),
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildUploadTab(),
                    _buildStatusTab(),
                    _buildHistoryTab(),
                  ],
                ),
    );
  }

  Widget _buildUploadTab() {
    final theme = Theme.of(context);
    final requirements = widget.requirements ?? _getDefaultRequirements();

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    theme.colorScheme.primaryContainer,
                    theme.colorScheme.primaryContainer.withAlpha(179),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.upload_file,
                    size: 32,
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Document Upload',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            color: theme.colorScheme.onPrimaryContainer,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Upload required documents for verification',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onPrimaryContainer
                                .withAlpha(204),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Requirements list
            Text(
              'Required Documents',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: requirements.length,
              itemBuilder: (context, index) {
                final requirement = requirements[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: DocumentUploadWidget(
                    requirement: requirement,
                    applicationId: widget.applicationId,
                    allowMultiple: false,
                    showPreview: true,
                    onUploadComplete: (request) {
                      _showUploadSuccess(request);
                      _loadData(); // Refresh data
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusTab() {
    final theme = Theme.of(context);
    final pendingRequests =
        _requests.where((r) => r.status == VerificationStatus.pending).toList();
    final approvedDocs = _documents
        .where((d) => d.status == VerificationStatus.approved)
        .toList();
    final rejectedDocs = _documents
        .where((d) => d.status == VerificationStatus.rejected)
        .toList();

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status overview
            _buildStatusOverview(theme, pendingRequests.length,
                approvedDocs.length, rejectedDocs.length),

            const SizedBox(height: 24),

            // Pending requests
            if (pendingRequests.isNotEmpty) ...[
              _buildStatusSection(
                theme,
                'Pending Verification',
                pendingRequests
                    .map((r) => _buildRequestCard(theme, r))
                    .toList(),
                Icons.pending_actions,
                theme.colorScheme.primary,
              ),
              const SizedBox(height: 24),
            ],

            // Approved documents
            if (approvedDocs.isNotEmpty) ...[
              _buildStatusSection(
                theme,
                'Approved Documents',
                approvedDocs.map((d) => _buildDocumentCard(theme, d)).toList(),
                Icons.check_circle,
                Colors.green,
              ),
              const SizedBox(height: 24),
            ],

            // Rejected documents
            if (rejectedDocs.isNotEmpty) ...[
              _buildStatusSection(
                theme,
                'Rejected Documents',
                rejectedDocs.map((d) => _buildDocumentCard(theme, d)).toList(),
                Icons.cancel,
                Colors.red,
              ),
            ],

            // Empty state
            if (pendingRequests.isEmpty &&
                approvedDocs.isEmpty &&
                rejectedDocs.isEmpty) ...[
              _buildEmptyState(
                theme,
                'No Documents',
                'Upload documents to see verification status',
                Icons.description,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryTab() {
    final theme = Theme.of(context);
    final allItems = <dynamic>[];
    allItems.addAll(_requests);
    allItems.addAll(_documents);

    // Sort by date (newest first)
    allItems.sort((a, b) {
      final dateA = a is VerificationRequest
          ? a.submittedAt
          : (a as VerificationDocument).uploadDate;
      final dateB = b is VerificationRequest
          ? b.submittedAt
          : (b as VerificationDocument).uploadDate;
      return dateB.compareTo(dateA);
    });

    return RefreshIndicator(
      onRefresh: _loadData,
      child: allItems.isEmpty
          ? _buildEmptyState(
              theme,
              'No History',
              'Document verification history will appear here',
              Icons.history,
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: allItems.length,
              itemBuilder: (context, index) {
                final item = allItems[index];
                if (item is VerificationRequest) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: _buildRequestCard(theme, item),
                  );
                } else {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child:
                        _buildDocumentCard(theme, item as VerificationDocument),
                  );
                }
              },
            ),
    );
  }

  Widget _buildStatusOverview(
      ThemeData theme, int pending, int approved, int rejected) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withAlpha(77),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Verification Overview',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildStatusCard(theme, 'Pending', pending.toString(),
                  Icons.pending_actions, theme.colorScheme.primary),
              const SizedBox(width: 12),
              _buildStatusCard(theme, 'Approved', approved.toString(),
                  Icons.check_circle, Colors.green),
              const SizedBox(width: 12),
              _buildStatusCard(theme, 'Rejected', rejected.toString(),
                  Icons.cancel, Colors.red),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCard(
      ThemeData theme, String label, String count, IconData icon, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withAlpha(26),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              count,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w700,
              ),
            ),
            Text(
              label,
              style: theme.textTheme.labelMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection(ThemeData theme, String title,
      List<Widget> children, IconData icon, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildRequestCard(ThemeData theme, VerificationRequest request) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getStatusIcon(request.status),
                  color: _getStatusColor(request.status),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Verification Request',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(request.status).withAlpha(26),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(request.status),
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: _getStatusColor(request.status),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Submitted: ${_formatDate(request.submittedAt)}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            if (request.notes != null) ...[
              const SizedBox(height: 8),
              Text(
                request.notes!,
                style: theme.textTheme.bodyMedium,
              ),
            ],
            if (request.rejectionReason != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error_outline,
                        color: Colors.red, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        request.rejectionReason!,
                        style: theme.textTheme.bodySmall
                            ?.copyWith(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentCard(ThemeData theme, VerificationDocument document) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getDocumentTypeIcon(document.type),
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    document.fileName,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(document.status).withAlpha(26),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(document.status),
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: _getStatusColor(document.status),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Uploaded: ${_formatDate(document.uploadDate)}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            if (document.rejectionReason != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error_outline,
                        color: Colors.red, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        document.rejectionReason!,
                        style: theme.textTheme.bodySmall
                            ?.copyWith(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(
      ThemeData theme, String title, String message, IconData icon) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: theme.colorScheme.onSurfaceVariant.withAlpha(128),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant.withAlpha(179),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showUploadSuccess(VerificationRequest request) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Documents uploaded successfully!'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'View Status',
          textColor: Colors.white,
          onPressed: () {
            _tabController.animateTo(1); // Switch to status tab
          },
        ),
      ),
    );
  }

  List<DocumentRequirement> _getDefaultRequirements() {
    return [
      const DocumentRequirement(
        documentType: 'passport',
        displayName: 'Passport',
        description: 'Valid passport with at least 6 months validity',
        isMandatory: true,
        acceptedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
        maxFileSizeMB: 5.0,
        specificRequirements: [
          'Color copy of passport bio page',
          'Must be valid for at least 6 months',
          'Clear and readable image',
        ],
      ),
      const DocumentRequirement(
        documentType: 'photo',
        displayName: 'Passport Photo',
        description: 'Recent passport-sized photograph',
        isMandatory: true,
        acceptedFormats: ['jpg', 'jpeg', 'png'],
        maxFileSizeMB: 2.0,
        specificRequirements: [
          'White background',
          '2x2 inches (51x51mm)',
          'Taken within last 6 months',
        ],
      ),
    ];
  }

  IconData _getStatusIcon(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
        return Icons.pending_actions;
      case VerificationStatus.approved:
        return Icons.check_circle;
      case VerificationStatus.rejected:
        return Icons.cancel;
      case VerificationStatus.expired:
        return Icons.access_time;
    }
  }

  Color _getStatusColor(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
        return Colors.orange;
      case VerificationStatus.approved:
        return Colors.green;
      case VerificationStatus.rejected:
        return Colors.red;
      case VerificationStatus.expired:
        return Colors.grey;
    }
  }

  String _getStatusText(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
        return 'Pending';
      case VerificationStatus.approved:
        return 'Approved';
      case VerificationStatus.rejected:
        return 'Rejected';
      case VerificationStatus.expired:
        return 'Expired';
    }
  }

  IconData _getDocumentTypeIcon(DocumentType type) {
    switch (type) {
      case DocumentType.passport:
        return Icons.book;
      case DocumentType.visa:
        return Icons.description;
      case DocumentType.driversLicense:
        return Icons.credit_card;
      case DocumentType.nationalId:
        return Icons.badge;
      case DocumentType.certification:
        return Icons.verified;
      case DocumentType.utilityBill:
        return Icons.receipt;
      case DocumentType.bankStatement:
        return Icons.account_balance;
      case DocumentType.businessLicense:
        return Icons.business;
      case DocumentType.insurance:
        return Icons.security;
      case DocumentType.other:
        return Icons.description;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
