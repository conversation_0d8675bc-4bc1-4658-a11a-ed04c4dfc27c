import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart'
    as models;
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/widgets/common/app_button.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';

import 'dart:async';
import 'dart:math' as math;

// Type aliases to make the code more readable
typedef TransferBooking = models.TransferBooking;
typedef TransferDriver = models.TransferDriver;

/// Real-time transfer tracking screen with live GPS tracking and ETA updates
class TransferTrackingScreen extends ConsumerStatefulWidget {
  /// The transfer booking to track
  final TransferBooking booking;

  /// Creates a new transfer tracking screen
  const TransferTrackingScreen({
    super.key,
    required this.booking,
  });

  @override
  ConsumerState<TransferTrackingScreen> createState() =>
      _TransferTrackingScreenState();
}

class _TransferTrackingScreenState extends ConsumerState<TransferTrackingScreen>
    with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  final LocationService _locationService = LocationService();
  final LoggingService _loggingService = LoggingService();

  // Animation controllers
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;

  // Tracking state
  LatLng? _driverLocation;
  LatLng? _destinationLocation;
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  String _estimatedArrival = 'Calculating...';
  double _distanceRemaining = 0.0;
  bool _isLoading = true;
  String? _errorMessage;

  // Mock driver data for demonstration
  final LatLng _mockDriverLocation = const LatLng(37.7749, -122.4194);
  Timer? _trackingTimer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeTracking();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    _trackingTimer?.cancel();
    _mapController?.dispose();
    super.dispose();
  }

  /// Initialize animations
  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _slideController.forward();
  }

  /// Initialize tracking functionality
  Future<void> _initializeTracking() async {
    try {
      // Initialize location service
      await _locationService.startLocationTracking();

      // Set up mock tracking data
      _setupMockTracking();

      // Start real-time tracking updates
      _startTrackingUpdates();

      setState(() {
        _isLoading = false;
      });

      _loggingService.info(
        'TransferTrackingScreen',
        'Tracking initialized successfully',
        {'booking_id': widget.booking.id},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'TransferTrackingScreen',
        'Failed to initialize tracking',
        {'error': e.toString()},
        stackTrace,
      );

      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to initialize tracking: $e';
      });
    }
  }

  /// Set up mock tracking data for demonstration
  void _setupMockTracking() {
    // Mock driver location (San Francisco)
    _driverLocation = _mockDriverLocation;

    // Mock destination (pickup location from booking)
    _destinationLocation = LatLng(
      widget.booking.pickupLocation.coordinates.latitude,
      widget.booking.pickupLocation.coordinates.longitude,
    );

    // Calculate initial distance and ETA
    _updateDistanceAndETA();

    // Create markers
    _updateMarkers();

    // Create route polyline
    _updateRoute();
  }

  /// Start real-time tracking updates
  void _startTrackingUpdates() {
    _trackingTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _simulateDriverMovement();
    });
  }

  /// Simulate driver movement for demonstration
  void _simulateDriverMovement() {
    if (_driverLocation == null || _destinationLocation == null) return;

    // Simulate driver moving towards destination
    final currentLat = _driverLocation!.latitude;
    final currentLng = _driverLocation!.longitude;
    final destLat = _destinationLocation!.latitude;
    final destLng = _destinationLocation!.longitude;

    // Move 10% closer to destination each update
    final newLat = currentLat + (destLat - currentLat) * 0.1;
    final newLng = currentLng + (destLng - currentLng) * 0.1;

    setState(() {
      _driverLocation = LatLng(newLat, newLng);
      _updateDistanceAndETA();
      _updateMarkers();
      _updateRoute();
    });

    // Update map camera to follow driver
    _updateMapCamera();
  }

  /// Update distance and ETA calculations
  void _updateDistanceAndETA() {
    if (_driverLocation == null || _destinationLocation == null) return;

    // Calculate distance using Haversine formula
    _distanceRemaining = _calculateDistance(
      _driverLocation!.latitude,
      _driverLocation!.longitude,
      _destinationLocation!.latitude,
      _destinationLocation!.longitude,
    );

    // Calculate ETA (assuming 30 km/h average speed in city)
    final etaMinutes = (_distanceRemaining / 30 * 60).round();
    final now = DateTime.now();
    final eta = now.add(Duration(minutes: etaMinutes));

    _estimatedArrival = '${eta.hour.toString().padLeft(2, '0')}:'
        '${eta.minute.toString().padLeft(2, '0')}';
  }

  /// Calculate distance between two points using Haversine formula
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);

    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) *
            math.cos(_degreesToRadians(lat2)) *
            math.sin(dLon / 2) *
            math.sin(dLon / 2);

    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  /// Convert degrees to radians
  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  /// Update map markers
  void _updateMarkers() {
    final Set<Marker> markers = {};

    // Driver marker with pulsing animation
    if (_driverLocation != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('driver'),
          position: _driverLocation!,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: InfoWindow(
            title: 'Your Driver',
            snippet: widget.booking.driverName ?? 'Driver',
          ),
        ),
      );
    }

    // Destination marker
    if (_destinationLocation != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('destination'),
          position: _destinationLocation!,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
          infoWindow: InfoWindow(
            title: 'Pickup Location',
            snippet: widget.booking.pickupLocation.name,
          ),
        ),
      );
    }

    setState(() {
      _markers = markers;
    });
  }

  /// Update route polyline
  void _updateRoute() {
    if (_driverLocation == null || _destinationLocation == null) return;

    final Set<Polyline> polylines = {};

    polylines.add(
      Polyline(
        polylineId: const PolylineId('route'),
        points: [_driverLocation!, _destinationLocation!],
        color: Colors.blue,
        width: 4,
        patterns: [PatternItem.dash(20), PatternItem.gap(10)],
      ),
    );

    setState(() {
      _polylines = polylines;
    });
  }

  /// Update map camera to show both driver and destination
  void _updateMapCamera() {
    if (_mapController == null ||
        _driverLocation == null ||
        _destinationLocation == null) {
      return;
    }

    final bounds = LatLngBounds(
      southwest: LatLng(
        math.min(_driverLocation!.latitude, _destinationLocation!.latitude),
        math.min(_driverLocation!.longitude, _destinationLocation!.longitude),
      ),
      northeast: LatLng(
        math.max(_driverLocation!.latitude, _destinationLocation!.latitude),
        math.max(_driverLocation!.longitude, _destinationLocation!.longitude),
      ),
    );

    _mapController!.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, 100),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Track Your Transfer'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshTracking,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: LoadingIndicator(
                size: 48,
                strokeWidth: 4,
              ),
            )
          : _errorMessage != null
              ? _buildErrorState()
              : _buildTrackingInterface(),
    );
  }

  /// Build error state
  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Tracking Error',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 24),
            AppButton(
              text: 'Retry',
              onPressed: _initializeTracking,
            ),
          ],
        ),
      ),
    );
  }

  /// Build main tracking interface
  Widget _buildTrackingInterface() {
    return Stack(
      children: [
        // Map view
        GoogleMap(
          onMapCreated: (GoogleMapController controller) {
            _mapController = controller;
            _updateMapCamera();
          },
          initialCameraPosition: CameraPosition(
            target: _driverLocation ?? const LatLng(37.7749, -122.4194),
            zoom: 14,
          ),
          markers: _markers,
          polylines: _polylines,
          myLocationEnabled: true,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
        ),

        // Tracking info overlay
        Positioned(
          top: 16,
          left: 16,
          right: 16,
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildTrackingInfoCard(),
          ),
        ),

        // Driver info overlay
        Positioned(
          bottom: 16,
          left: 16,
          right: 16,
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildDriverInfoCard(),
          ),
        ),

        // Floating action buttons
        Positioned(
          right: 16,
          bottom: 200,
          child: Column(
            children: [
              FloatingActionButton(
                heroTag: 'center_map',
                mini: true,
                onPressed: _centerMapOnDriver,
                backgroundColor: Colors.white,
                foregroundColor: Colors.blue,
                child: const Icon(Icons.my_location),
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                heroTag: 'call_driver',
                mini: true,
                onPressed: _callDriver,
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                child: const Icon(Icons.phone),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build tracking info card
  Widget _buildTrackingInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(width: 8),
              const Text(
                'Driver En Route',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildInfoItem(
                icon: Icons.access_time,
                label: 'ETA',
                value: _estimatedArrival,
              ),
              _buildInfoItem(
                icon: Icons.straighten,
                label: 'Distance',
                value: '${_distanceRemaining.toStringAsFixed(1)} km',
              ),
              _buildInfoItem(
                icon: Icons.location_on,
                label: 'Status',
                value: 'Approaching',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build driver info card
  Widget _buildDriverInfoCard() {
    final driver = widget.booking.transferService?.driver;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // Driver photo
          CircleAvatar(
            radius: 24,
            backgroundColor: Colors.grey[300],
            backgroundImage: driver?.photoUrl != null
                ? NetworkImage(driver!.photoUrl)
                : null,
            child: driver?.photoUrl == null
                ? const Icon(Icons.person, color: Colors.grey)
                : null,
          ),
          const SizedBox(width: 12),

          // Driver info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.booking.driverName ?? 'Your Driver',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (driver != null) ...[
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        size: 16,
                        color: Colors.amber[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        driver.formattedRating,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (driver.isVerified)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.green.withAlpha(51),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Text(
                            'Verified',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
                if (widget.booking.vehicleLicensePlate != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Vehicle: ${widget.booking.vehicleLicensePlate}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Action buttons
          Row(
            children: [
              IconButton(
                onPressed: _sendMessage,
                icon: const Icon(Icons.message),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.blue.withAlpha(26),
                  foregroundColor: Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _callDriver,
                icon: const Icon(Icons.phone),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.green.withAlpha(26),
                  foregroundColor: Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build info item widget
  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: Colors.grey[600],
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Center map on driver location
  void _centerMapOnDriver() {
    if (_mapController != null && _driverLocation != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(_driverLocation!, 16),
      );
    }
  }

  /// Call driver
  void _callDriver() {
    // TODO: Implement phone call functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Calling driver...'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Send message to driver
  void _sendMessage() {
    // TODO: Implement messaging functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening chat with driver...'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Refresh tracking data
  void _refreshTracking() {
    _initializeTracking();
  }
}
