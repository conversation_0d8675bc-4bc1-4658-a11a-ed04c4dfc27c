import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart';
import 'package:culture_connect/models/location/geo_location.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/travel/transfer/enhanced_driver_profile_card.dart';
import 'package:culture_connect/widgets/travel/transfer/location_picker.dart';
import 'package:culture_connect/widgets/common/app_button.dart';
import 'package:culture_connect/widgets/loading_indicator.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/screens/payment/production_payment_screen.dart';

/// Enhanced transfer booking screen with improved UI and functionality
class EnhancedTransferBookingScreen extends ConsumerStatefulWidget {
  /// The transfer service to book
  final TransferService transfer;

  /// Creates a new enhanced transfer booking screen
  const EnhancedTransferBookingScreen({
    super.key,
    required this.transfer,
  });

  @override
  ConsumerState<EnhancedTransferBookingScreen> createState() =>
      _EnhancedTransferBookingScreenState();
}

class _EnhancedTransferBookingScreenState
    extends ConsumerState<EnhancedTransferBookingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passengerNameController = TextEditingController();
  final _passengerEmailController = TextEditingController();
  final _passengerPhoneController = TextEditingController();
  final _flightNumberController = TextEditingController();
  final _specialRequestsController = TextEditingController();

  TransferLocation? _pickupLocation;
  TransferLocation? _dropoffLocation;
  DateTime? _pickupDateTime;
  int _passengerCount = 1;
  bool _includeFlightTracking = false;
  bool _includeMeetAndGreet = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _passengerNameController.dispose();
    _passengerEmailController.dispose();
    _passengerPhoneController.dispose();
    _flightNumberController.dispose();
    _specialRequestsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Book Transfer',
        backgroundColor: Colors.white,
        foregroundColor: AppTheme.textPrimaryColor,
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTransferSummaryCard(),
                    const SizedBox(height: 24),
                    _buildLocationSection(),
                    const SizedBox(height: 24),
                    _buildDateTimeSection(),
                    const SizedBox(height: 24),
                    _buildPassengerSection(),
                    const SizedBox(height: 24),
                    _buildPassengerDetailsSection(),
                    const SizedBox(height: 24),
                    _buildAdditionalServicesSection(),
                    if (widget.transfer.driver != null) ...[
                      const SizedBox(height: 24),
                      _buildDriverSection(),
                    ],
                    const SizedBox(height: 24),
                    _buildSpecialRequestsSection(),
                    const SizedBox(height: 32),
                    _buildBookingButton(),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
    );
  }

  /// Build transfer summary card
  Widget _buildTransferSummaryCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withAlpha(51)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  widget.transfer.imageUrl,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 60,
                      height: 60,
                      color: AppTheme.surfaceColor,
                      child: const Icon(Icons.airport_shuttle),
                    );
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.transfer.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${widget.transfer.vehicle.make} ${widget.transfer.vehicle.model}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.star,
                          size: 16,
                          color: Colors.amber,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          widget.transfer.rating.toStringAsFixed(1),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          '\$${widget.transfer.price.toStringAsFixed(0)}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build location selection section
  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Pickup & Dropoff',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        _buildLocationField(
          label: 'Pickup Location',
          location: _pickupLocation,
          onTap: () => _selectLocation(isPickup: true),
          icon: Icons.my_location,
        ),
        const SizedBox(height: 12),
        _buildLocationField(
          label: 'Dropoff Location',
          location: _dropoffLocation,
          onTap: () => _selectLocation(isPickup: false),
          icon: Icons.location_on,
        ),
      ],
    );
  }

  /// Build location field
  Widget _buildLocationField({
    required String label,
    required TransferLocation? location,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.withAlpha(51)),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    location?.name ?? 'Select location',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: location != null
                          ? AppTheme.textPrimaryColor
                          : AppTheme.textSecondaryColor,
                    ),
                  ),
                  if (location?.address != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      location!.address,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            const Icon(
              Icons.chevron_right,
              color: AppTheme.textSecondaryColor,
            ),
          ],
        ),
      ),
    );
  }

  /// Build date and time selection section
  Widget _buildDateTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Pickup Date & Time',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        GestureDetector(
          onTap: _selectDateTime,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withAlpha(51)),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.schedule,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _pickupDateTime != null
                        ? '${_pickupDateTime!.day}/${_pickupDateTime!.month}/${_pickupDateTime!.year} at ${_pickupDateTime!.hour}:${_pickupDateTime!.minute.toString().padLeft(2, '0')}'
                        : 'Select pickup date and time',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: _pickupDateTime != null
                          ? AppTheme.textPrimaryColor
                          : AppTheme.textSecondaryColor,
                    ),
                  ),
                ),
                const Icon(
                  Icons.chevron_right,
                  color: AppTheme.textSecondaryColor,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build passenger count section
  Widget _buildPassengerSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Passengers',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.withAlpha(51)),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.people,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Number of passengers',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: _passengerCount > 1
                        ? () => setState(() => _passengerCount--)
                        : null,
                    icon: const Icon(Icons.remove_circle_outline),
                    color: AppTheme.primaryColor,
                  ),
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withAlpha(26),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        _passengerCount.toString(),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed:
                        _passengerCount < widget.transfer.passengerCapacity
                            ? () => setState(() => _passengerCount++)
                            : null,
                    icon: const Icon(Icons.add_circle_outline),
                    color: AppTheme.primaryColor,
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Maximum ${widget.transfer.passengerCapacity} passengers',
          style: const TextStyle(
            fontSize: 12,
            color: AppTheme.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  /// Build passenger details section
  Widget _buildPassengerDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Contact Information',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _passengerNameController,
          decoration: InputDecoration(
            labelText: 'Full Name',
            prefixIcon: const Icon(Icons.person),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your full name';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _passengerEmailController,
          decoration: InputDecoration(
            labelText: 'Email Address',
            prefixIcon: const Icon(Icons.email),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your email address';
            }
            if (!value.contains('@')) {
              return 'Please enter a valid email address';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _passengerPhoneController,
          decoration: InputDecoration(
            labelText: 'Phone Number',
            prefixIcon: const Icon(Icons.phone),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your phone number';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// Build additional services section
  Widget _buildAdditionalServicesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Additional Services',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        if (widget.transfer.includesFlightTracking)
          _buildServiceOption(
            title: 'Flight Tracking',
            subtitle: 'Monitor your flight for delays and updates',
            value: _includeFlightTracking,
            onChanged: (value) =>
                setState(() => _includeFlightTracking = value),
            icon: Icons.flight,
          ),
        if (widget.transfer.includesMeetAndGreet)
          _buildServiceOption(
            title: 'Meet & Greet',
            subtitle: 'Driver will meet you at arrivals with a name sign',
            value: _includeMeetAndGreet,
            onChanged: (value) => setState(() => _includeMeetAndGreet = value),
            icon: Icons.person_pin_circle,
          ),
        if (_includeFlightTracking) ...[
          const SizedBox(height: 16),
          TextFormField(
            controller: _flightNumberController,
            decoration: InputDecoration(
              labelText: 'Flight Number',
              prefixIcon: const Icon(Icons.flight),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              hintText: 'e.g., AA123, BA456',
            ),
          ),
        ],
      ],
    );
  }

  /// Build service option toggle
  Widget _buildServiceOption({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withAlpha(51)),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppTheme.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }

  /// Build driver section
  Widget _buildDriverSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Your Driver',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        EnhancedDriverProfileCard(
          driver: widget.transfer.driver!,
          isCompact: true,
        ),
      ],
    );
  }

  /// Build special requests section
  Widget _buildSpecialRequestsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Special Requests',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _specialRequestsController,
          decoration: InputDecoration(
            labelText: 'Any special requests or notes?',
            prefixIcon: const Icon(Icons.note),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            hintText: 'Child seat, wheelchair access, etc.',
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  /// Build booking button
  Widget _buildBookingButton() {
    final totalPrice = _calculateTotalPrice();
    final isValid = _isFormValid();

    return AppButton(
      onPressed: () => isValid ? _handleBooking() : null,
      text: 'Book Transfer - \$${totalPrice.toStringAsFixed(0)}',
      isLoading: _isLoading,
      isDisabled: !isValid,
    );
  }

  /// Calculate total price including additional services
  double _calculateTotalPrice() {
    double total = widget.transfer.price;

    // Add additional service costs (placeholder logic)
    if (_includeFlightTracking) total += 5;
    if (_includeMeetAndGreet) total += 10;

    return total;
  }

  /// Check if form is valid
  bool _isFormValid() {
    return _pickupLocation != null &&
        _dropoffLocation != null &&
        _pickupDateTime != null &&
        _passengerNameController.text.isNotEmpty &&
        _passengerEmailController.text.isNotEmpty &&
        _passengerPhoneController.text.isNotEmpty;
  }

  /// Handle location selection
  Future<void> _selectLocation({required bool isPickup}) async {
    // Mock location selection - in real app, this would show location picker
    final mockLocations = [
      const TransferLocation(
        id: '1',
        name: 'Lagos Airport (LOS)',
        address: 'Murtala Muhammed International Airport',
        city: 'Lagos',
        country: 'Nigeria',
        type: TransferLocationType.airport,
        coordinates: GeoLocation(latitude: 6.5774, longitude: 3.3211),
      ),
      const TransferLocation(
        id: '2',
        name: 'Victoria Island',
        address: 'Victoria Island, Lagos',
        city: 'Lagos',
        country: 'Nigeria',
        type: TransferLocationType.hotel,
        coordinates: GeoLocation(latitude: 6.4281, longitude: 3.4219),
      ),
    ];

    final result = await showModalBottomSheet<TransferLocation>(
      context: context,
      builder: (context) => LocationPicker(
        availableLocations: mockLocations,
        onLocationSelected: (location) => Navigator.pop(context, location),
      ),
    );

    if (result != null) {
      setState(() {
        if (isPickup) {
          _pickupLocation = result;
        } else {
          _dropoffLocation = result;
        }
      });
    }
  }

  /// Handle date and time selection
  Future<void> _selectDateTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(hours: 2)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (time != null && mounted) {
        setState(() {
          _pickupDateTime = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  /// Handle booking submission
  Future<void> _handleBooking() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Create booking object
      final booking = TransferBooking(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: 'user1', // TODO: Get from auth service
        transferId: widget.transfer.id,
        pickupLocation: _pickupLocation!,
        dropoffLocation: _dropoffLocation!,
        pickupDateTime: _pickupDateTime!,
        passengerCount: _passengerCount,
        luggageCount: 1, // Default luggage count
        contactName: _passengerNameController.text,
        contactEmail: _passengerEmailController.text,
        contactPhone: _passengerPhoneController.text,
        flightInfo: _flightNumberController.text.isNotEmpty
            ? _flightNumberController.text
            : null,
        specialRequests: _specialRequestsController.text.isNotEmpty
            ? _specialRequestsController.text
            : null,
        status: TransferBookingStatus.pending,
        totalPrice: _calculateTotalPrice(),
        currency: 'USD',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Convert TransferBooking to Booking for payment screen
      final paymentBooking = Booking(
        id: booking.id,
        experienceId: booking.transferId,
        date: booking.pickupDateTime,
        timeSlot: TimeSlot(
          startTime: booking.pickupDateTime,
          endTime: booking.pickupDateTime.add(const Duration(hours: 2)),
        ),
        participantCount: booking.passengerCount,
        totalAmount: booking.totalPrice,
        status: BookingStatus.pending,
        specialRequirements: booking.specialRequests ?? '',
        createdAt: booking.createdAt,
        updatedAt: booking.updatedAt,
      );

      // Navigate to payment screen
      if (mounted) {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProductionPaymentScreen(
              booking: paymentBooking,
              userEmail: _passengerEmailController.text,
              userName: _passengerNameController.text,
              userPhone: _passengerPhoneController.text,
            ),
          ),
        );

        if (result == true && mounted) {
          Navigator.pop(context, true);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating booking: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
