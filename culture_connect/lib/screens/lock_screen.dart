import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/security_settings_provider.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/services/auto_lock_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/auth_gradient_background.dart';
import 'package:culture_connect/widgets/curved_content_container.dart';
import 'package:culture_connect/widgets/custom_text_field.dart';

/// Lock screen for auto-lock security feature
class LockScreen extends ConsumerStatefulWidget {
  const LockScreen({super.key});

  @override
  ConsumerState<LockScreen> createState() => _LockScreenState();
}

class _LockScreenState extends ConsumerState<LockScreen> {
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  bool _obscurePassword = true;
  String? _errorMessage;
  bool _biometricAttempted = false;

  @override
  void initState() {
    super.initState();
    _attemptBiometricAuth();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  /// Attempt biometric authentication on screen load
  Future<void> _attemptBiometricAuth() async {
    if (_biometricAttempted) return;

    final securitySettings = ref.read(securitySettingsProvider);
    if (!securitySettings.biometricEnabled) return;

    _biometricAttempted = true;

    try {
      final authService = AuthService();
      final isAvailable = await authService.isBiometricAvailable();

      if (isAvailable) {
        final success = await authService.authenticateWithBiometrics();
        if (success && mounted) {
          _unlockApp();
        }
      }
    } catch (e) {
      // Biometric auth failed, show password input
      debugPrint('Biometric authentication failed: $e');
    }
  }

  /// Authenticate with password
  Future<void> _authenticateWithPassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = ref.read(authServiceProvider);
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      // Re-authenticate with current credentials
      // Note: In a real implementation, you might want to store encrypted credentials
      // or use a different authentication method for the lock screen
      await authService.loginWithEmailAndPassword(
        email: currentUser.email!,
        password: _passwordController.text,
      );

      if (mounted) {
        _unlockApp();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid password. Please try again.';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Unlock the app and return to previous screen
  void _unlockApp() {
    final autoLockService = ref.read(autoLockServiceProvider);
    autoLockService.unlockApp();

    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  /// Retry biometric authentication
  Future<void> _retryBiometric() async {
    _biometricAttempted = false;
    await _attemptBiometricAuth();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final topSectionHeight = screenHeight * 0.4;
    final currentUser = ref.watch(currentUserModelProvider);

    return Scaffold(
      backgroundColor: AppTheme.authBackgroundStart,
      body: AuthGradientBackground(
        child: Column(
          children: [
            // Top section with user greeting
            SizedBox(
              height: topSectionHeight,
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // App logo or user avatar
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(51), // 0.2 opacity
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.lock_outline,
                          size: 40,
                          color: Colors.white,
                        ),
                      ),

                      const SizedBox(height: 24),

                      // User greeting
                      currentUser.when(
                        data: (user) => Text(
                          'Welcome back, ${user?.firstName ?? 'User'}!',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            letterSpacing: 0.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        loading: () => const Text(
                          'Welcome back!',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            letterSpacing: 0.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        error: (_, __) => const Text(
                          'Welcome back!',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            letterSpacing: 0.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Subtitle
                      Text(
                        'Please authenticate to continue',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white.withAlpha(179), // 0.7 opacity
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Bottom curved content section
            Expanded(
              child: SimpleCurvedContainer(
                curveHeight: 30.0,
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 20),

                      // Enter password text
                      const Text(
                        'Enter your password',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textSecondaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Password field
                      CustomTextField(
                        controller: _passwordController,
                        label: '',
                        hint: 'enter your password',
                        obscureText: _obscurePassword,
                        keyboardType: TextInputType.visiblePassword,
                        textInputAction: TextInputAction.done,
                        suffixIcon: _obscurePassword
                            ? Icons.visibility_off
                            : Icons.visibility,
                        onSuffixIconPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                        onSubmitted: (_) => _authenticateWithPassword(),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Password is required';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 12),

                      // Error message
                      if (_errorMessage != null)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: Text(
                            _errorMessage!,
                            style: const TextStyle(
                              color: AppTheme.errorColor,
                              fontSize: 14,
                            ),
                          ),
                        ),

                      // Forgot password link
                      Align(
                        alignment: Alignment.centerLeft,
                        child: TextButton(
                          onPressed: () {
                            // Navigate to forgot password or show biometric option
                            _retryBiometric();
                          },
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.zero,
                            minimumSize: Size.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ),
                          child: const Text(
                            'Use Biometric Authentication',
                            style: TextStyle(
                              color: AppTheme.errorColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 32),

                      // Login button
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed:
                              _isLoading ? null : _authenticateWithPassword,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.authButtonColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 0,
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  height: 24,
                                  width: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : const Text(
                                  'Login',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
