import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:video_compress/video_compress.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';

/// Utility class for compressing audio and video files
class MediaCompressionUtils {
  static const int _maxVideoSizeMB = 50;
  static const int _maxAudioSizeMB = 10;
  static const int _maxImageSizeMB = 5;

  /// Compress video file
  static Future<String?> compressVideo({
    required String inputPath,
    VideoQuality quality = VideoQuality.MediumQuality,
    bool deleteOrigin = false,
    int? frameRate,
    int? bitrate,
  }) async {
    try {
      final file = File(inputPath);
      if (!file.existsSync()) {
        throw Exception('Input video file does not exist');
      }

      // Check if compression is needed
      final fileSizeBytes = await file.length();
      final fileSizeMB = fileSizeBytes / (1024 * 1024);

      if (fileSizeMB <= _maxVideoSizeMB) {
        debugPrint(
            'Video file size is acceptable: ${fileSizeMB.toStringAsFixed(2)}MB');
        return inputPath;
      }

      debugPrint(
          'Compressing video: ${fileSizeMB.toStringAsFixed(2)}MB -> target: ${_maxVideoSizeMB}MB');

      // Set up compression parameters
      // Note: Progress monitoring removed due to API changes

      try {
        final info = await VideoCompress.compressVideo(
          inputPath,
          quality: quality,
          deleteOrigin: deleteOrigin,
          includeAudio: true,
        );

        if (info == null || info.file == null) {
          throw Exception('Video compression failed');
        }

        final compressedSizeBytes = await info.file!.length();
        final compressedSizeMB = compressedSizeBytes / (1024 * 1024);

        debugPrint(
            'Video compressed successfully: ${compressedSizeMB.toStringAsFixed(2)}MB');

        return info.file!.path;
      } finally {
        // Progress subscription cleanup removed
      }
    } catch (e) {
      debugPrint('Video compression error: $e');
      return null;
    }
  }

  /// Compress audio file (using video compression for audio-only files)
  static Future<String?> compressAudio({
    required String inputPath,
    bool deleteOrigin = false,
    int? bitrate,
  }) async {
    try {
      final file = File(inputPath);
      if (!file.existsSync()) {
        throw Exception('Input audio file does not exist');
      }

      // Check if compression is needed
      final fileSizeBytes = await file.length();
      final fileSizeMB = fileSizeBytes / (1024 * 1024);

      if (fileSizeMB <= _maxAudioSizeMB) {
        debugPrint(
            'Audio file size is acceptable: ${fileSizeMB.toStringAsFixed(2)}MB');
        return inputPath;
      }

      debugPrint(
          'Compressing audio: ${fileSizeMB.toStringAsFixed(2)}MB -> target: ${_maxAudioSizeMB}MB');

      // Use video compression for audio files
      // Note: Progress monitoring removed due to API changes

      try {
        final info = await VideoCompress.compressVideo(
          inputPath,
          quality: VideoQuality.LowQuality,
          deleteOrigin: deleteOrigin,
          includeAudio: true,
        );

        if (info == null || info.file == null) {
          throw Exception('Audio compression failed');
        }

        final compressedSizeBytes = await info.file!.length();
        final compressedSizeMB = compressedSizeBytes / (1024 * 1024);

        debugPrint(
            'Audio compressed successfully: ${compressedSizeMB.toStringAsFixed(2)}MB');

        return info.file!.path;
      } finally {
        // Progress subscription cleanup removed
      }
    } catch (e) {
      debugPrint('Audio compression error: $e');
      return null;
    }
  }

  /// Compress image file
  static Future<String?> compressImage({
    required String inputPath,
    int quality = 85,
    int? minWidth,
    int? minHeight,
    bool deleteOrigin = false,
  }) async {
    try {
      final file = File(inputPath);
      if (!file.existsSync()) {
        throw Exception('Input image file does not exist');
      }

      // Check if compression is needed
      final fileSizeBytes = await file.length();
      final fileSizeMB = fileSizeBytes / (1024 * 1024);

      if (fileSizeMB <= _maxImageSizeMB) {
        debugPrint(
            'Image file size is acceptable: ${fileSizeMB.toStringAsFixed(2)}MB');
        return inputPath;
      }

      debugPrint(
          'Compressing image: ${fileSizeMB.toStringAsFixed(2)}MB -> target: ${_maxImageSizeMB}MB');

      // Generate output path
      final dir = await getTemporaryDirectory();
      final fileName = path.basenameWithoutExtension(inputPath);
      final extension = path.extension(inputPath).toLowerCase();
      final outputPath =
          path.join(dir.path, '${fileName}_compressed$extension');

      // Compress image
      final result = await FlutterImageCompress.compressAndGetFile(
        inputPath,
        outputPath,
        quality: quality,
        minWidth: minWidth ?? 1024,
        minHeight: minHeight ?? 1024,
        format: _getCompressFormat(extension),
      );

      if (result == null) {
        throw Exception('Image compression failed');
      }

      final compressedSizeBytes = await result.length();
      final compressedSizeMB = compressedSizeBytes / (1024 * 1024);

      debugPrint(
          'Image compressed successfully: ${compressedSizeMB.toStringAsFixed(2)}MB');

      // Delete original if requested
      if (deleteOrigin) {
        await file.delete();
      }

      return result.path;
    } catch (e) {
      debugPrint('Image compression error: $e');
      return null;
    }
  }

  /// Generate video thumbnail
  static Future<String?> generateVideoThumbnail({
    required String videoPath,
    int timeMs = 1000,
    int quality = 50,
  }) async {
    try {
      final thumbnail = await VideoCompress.getFileThumbnail(
        videoPath,
        quality: quality,
      );

      // Save thumbnail to temporary directory
      final dir = await getTemporaryDirectory();
      final fileName = path.basenameWithoutExtension(videoPath);
      final thumbnailPath = path.join(dir.path, '${fileName}_thumbnail.jpg');

      final thumbnailFile = File(thumbnailPath);
      await thumbnailFile.writeAsBytes(await thumbnail.readAsBytes());

      debugPrint('Video thumbnail generated: $thumbnailPath');

      return thumbnailPath;
    } catch (e) {
      debugPrint('Thumbnail generation error: $e');
      return null;
    }
  }

  /// Get video information
  static Future<MediaInfo?> getVideoInfo(String videoPath) async {
    try {
      final info = await VideoCompress.getMediaInfo(videoPath);
      return info;
    } catch (e) {
      debugPrint('Get video info error: $e');
      return null;
    }
  }

  /// Cancel ongoing compression
  static Future<void> cancelCompression() async {
    try {
      await VideoCompress.cancelCompression();
      debugPrint('Compression cancelled');
    } catch (e) {
      debugPrint('Cancel compression error: $e');
    }
  }

  /// Delete temporary files
  static Future<void> deleteTemporaryFiles() async {
    try {
      await VideoCompress.deleteAllCache();
      debugPrint('Temporary files deleted');
    } catch (e) {
      debugPrint('Delete temporary files error: $e');
    }
  }

  /// Get appropriate compression format for image
  static CompressFormat _getCompressFormat(String extension) {
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return CompressFormat.jpeg;
      case '.png':
        return CompressFormat.png;
      case '.webp':
        return CompressFormat.webp;
      case '.heic':
        return CompressFormat.heic;
      default:
        return CompressFormat.jpeg;
    }
  }

  /// Check if file size is within limits
  static Future<bool> isFileSizeAcceptable(
      String filePath, FileType fileType) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) return false;

      final fileSizeBytes = await file.length();
      final fileSizeMB = fileSizeBytes / (1024 * 1024);

      switch (fileType) {
        case FileType.video:
          return fileSizeMB <= _maxVideoSizeMB;
        case FileType.audio:
          return fileSizeMB <= _maxAudioSizeMB;
        case FileType.image:
          return fileSizeMB <= _maxImageSizeMB;
      }
    } catch (e) {
      debugPrint('File size check error: $e');
      return false;
    }
  }

  /// Get file size in MB
  static Future<double> getFileSizeMB(String filePath) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) return 0.0;

      final fileSizeBytes = await file.length();
      return fileSizeBytes / (1024 * 1024);
    } catch (e) {
      debugPrint('Get file size error: $e');
      return 0.0;
    }
  }
}

/// File type enumeration
enum FileType {
  video,
  audio,
  image,
}

/// Compression quality presets
class CompressionPresets {
  static const VideoQuality highQuality = VideoQuality.HighestQuality;
  static const VideoQuality mediumQuality = VideoQuality.MediumQuality;
  static const VideoQuality lowQuality = VideoQuality.LowQuality;

  static const int highImageQuality = 95;
  static const int mediumImageQuality = 85;
  static const int lowImageQuality = 70;

  static const int highAudioBitrate = 256000; // 256kbps
  static const int mediumAudioBitrate = 128000; // 128kbps
  static const int lowAudioBitrate = 64000; // 64kbps
}
