import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/payment/provider_config_manager.dart';
import 'package:culture_connect/widgets/payment/provider_ui_components.dart';
import 'package:culture_connect/core/theme/app_theme.dart';

/// Payment method selector with geolocation-aware provider selection
class PaymentMethodSelector extends StatefulWidget {
  final double amount;
  final String currency;
  final String? userCountryCode;
  final Map<String, dynamic>? userPreferences;
  final Function(PaymentProvider) onProviderSelected;
  final ProviderConfigManager configManager;
  final LoggingService loggingService;

  const PaymentMethodSelector({
    super.key,
    required this.amount,
    required this.currency,
    this.userCountryCode,
    this.userPreferences,
    required this.onProviderSelected,
    required this.configManager,
    required this.loggingService,
  });

  @override
  State<PaymentMethodSelector> createState() => _PaymentMethodSelectorState();
}

class _PaymentMethodSelectorState extends State<PaymentMethodSelector>
    with TickerProviderStateMixin {
  PaymentProvider? _selectedProvider;
  List<PaymentProviderOption> _availableProviders = [];
  bool _isLoading = true;
  String? _errorMessage;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppTheme.animationDuration,
      vsync: this,
    );
    _loadAvailableProviders();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Load available payment providers based on geolocation and configuration
  Future<void> _loadAvailableProviders() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Get available providers from configuration manager
      final availableProviders = widget.configManager.availableProviders;

      // Create provider options with geolocation-aware recommendations
      final providerOptions = <PaymentProviderOption>[];

      for (final provider in PaymentProvider.values) {
        final isAvailable = availableProviders.contains(provider);
        final recommendation = _getProviderRecommendation(provider);

        providerOptions.add(PaymentProviderOption(
          provider: provider,
          isAvailable: isAvailable,
          isRecommended: recommendation.isRecommended,
          subtitle: recommendation.subtitle,
          estimatedFee: _calculateEstimatedFee(provider),
          processingTime: _getProcessingTime(provider),
        ));
      }

      // Sort providers by recommendation and availability
      providerOptions.sort((a, b) {
        if (a.isRecommended && !b.isRecommended) return -1;
        if (!a.isRecommended && b.isRecommended) return 1;
        if (a.isAvailable && !b.isAvailable) return -1;
        if (!a.isAvailable && b.isAvailable) return 1;
        return 0;
      });

      setState(() {
        _availableProviders = providerOptions;
        _isLoading = false;

        // Auto-select the first recommended and available provider
        final autoSelect = providerOptions.firstWhere(
          (option) => option.isRecommended && option.isAvailable,
          orElse: () => providerOptions.firstWhere(
            (option) => option.isAvailable,
            orElse: () => providerOptions.first,
          ),
        );

        if (autoSelect.isAvailable) {
          _selectedProvider = autoSelect.provider;
        }
      });

      _animationController.forward();

      widget.loggingService.info(
        'PaymentMethodSelector',
        'Payment providers loaded successfully',
        {
          'available_count': availableProviders.length,
          'user_country': widget.userCountryCode,
          'auto_selected': _selectedProvider?.name,
        },
      );
    } catch (e, stackTrace) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load payment methods: $e';
      });

      widget.loggingService.error(
        'PaymentMethodSelector',
        'Failed to load payment providers',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Get provider recommendation based on geolocation and preferences
  ProviderRecommendation _getProviderRecommendation(PaymentProvider provider) {
    final countryCode = widget.userCountryCode?.toUpperCase();

    // African countries - recommend Paystack
    const africanCountries = {
      'NG',
      'GH',
      'ZA',
      'KE',
      'UG',
      'TZ',
      'RW',
      'CI',
      'SN',
      'BF',
      'ML',
      'NE',
      'TD',
      'CM',
      'CF',
      'GQ',
      'GA',
      'CG',
      'CD',
      'AO',
      'ZM',
      'ZW',
      'BW',
      'NA',
      'SZ',
      'LS',
      'MW',
      'MZ',
      'MG',
      'MU',
      'SC',
      'KM',
      'DJ',
      'SO',
      'ET',
      'ER',
      'SS',
      'SD',
      'EG',
      'LY',
      'TN',
      'DZ',
      'MA',
      'EH',
      'MR',
      'GM',
      'GW',
      'SL',
      'LR',
      'CV',
    };

    // Diaspora countries - recommend Stripe
    const diasporaCountries = {
      'US',
      'CA',
      'GB',
      'DE',
      'FR',
      'IT',
      'ES',
      'NL',
      'BE',
      'CH',
      'AT',
      'SE',
      'NO',
      'DK',
      'FI',
      'IE',
      'PT',
      'LU',
      'IS',
      'MT',
      'CY',
      'EE',
      'LV',
      'LT',
      'PL',
      'CZ',
      'SK',
      'HU',
      'SI',
      'HR',
      'BG',
      'RO',
      'GR',
      'AU',
      'NZ',
      'JP',
      'SG',
      'HK',
      'AE',
      'QA',
    };

    switch (provider) {
      case PaymentProvider.stripe:
        if (countryCode != null && diasporaCountries.contains(countryCode)) {
          return const ProviderRecommendation(
            isRecommended: true,
            subtitle: 'Recommended for your location • Fast & secure',
          );
        }
        return const ProviderRecommendation(
          isRecommended: false,
          subtitle: 'International payments • Credit/debit cards',
        );

      case PaymentProvider.paystack:
        if (countryCode != null && africanCountries.contains(countryCode)) {
          return const ProviderRecommendation(
            isRecommended: true,
            subtitle: 'Recommended for Africa • Multiple payment options',
          );
        }
        return const ProviderRecommendation(
          isRecommended: false,
          subtitle: 'African markets • Bank transfer, USSD, cards',
        );

      case PaymentProvider.busha:
        // Recommend crypto for tech-savvy users or specific preferences
        final prefersCrypto = widget.userPreferences?['prefers_crypto'] == true;
        return ProviderRecommendation(
          isRecommended: prefersCrypto,
          subtitle: prefersCrypto
              ? 'Recommended • Cryptocurrency payments'
              : 'Cryptocurrency • BTC, ETH, USDT, USDC',
        );
    }
  }

  /// Calculate estimated fee for provider
  String _calculateEstimatedFee(PaymentProvider provider) {
    // TODO: Get actual fee calculation from backend
    // This is a simplified implementation
    final amount = widget.amount;

    switch (provider) {
      case PaymentProvider.stripe:
        final fee = amount * 0.029 + 0.30; // 2.9% + $0.30
        return '+\$${fee.toStringAsFixed(2)}';
      case PaymentProvider.paystack:
        final fee = amount * 0.015; // 1.5%
        return '+₦${fee.toStringAsFixed(2)}';
      case PaymentProvider.busha:
        return 'Network fees apply';
    }
  }

  /// Get estimated processing time for provider
  String _getProcessingTime(PaymentProvider provider) {
    switch (provider) {
      case PaymentProvider.stripe:
        return 'Instant';
      case PaymentProvider.paystack:
        return '1-2 minutes';
      case PaymentProvider.busha:
        return '10-30 minutes';
    }
  }

  /// Handle provider selection
  void _selectProvider(PaymentProvider provider) {
    if (!_availableProviders
        .any((p) => p.provider == provider && p.isAvailable)) {
      return;
    }

    setState(() {
      _selectedProvider = provider;
    });

    // Provide haptic feedback
    HapticFeedback.selectionClick();

    // Notify parent widget
    widget.onProviderSelected(provider);

    widget.loggingService.info(
      'PaymentMethodSelector',
      'Payment provider selected',
      {
        'provider': provider.name,
        'user_country': widget.userCountryCode,
        'amount': widget.amount,
        'currency': widget.currency,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (_isLoading) {
      return _buildLoadingState(theme);
    }

    if (_errorMessage != null) {
      return _buildErrorState(theme);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          child: Text(
            'Choose Payment Method',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
        ),

        // Location indicator
        if (widget.userCountryCode != null)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            child: Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Detected location: ${widget.userCountryCode}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),

        const SizedBox(height: 16),

        // Provider options
        AnimatedList(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          initialItemCount: _availableProviders.length,
          itemBuilder: (context, index, animation) {
            final option = _availableProviders[index];

            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(1, 0), end: Offset.zero)
                    .chain(CurveTween(curve: Curves.easeOutCubic)),
              ),
              child: Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: ProviderUIComponents.buildProviderCard(
                  context: context,
                  provider: option.provider,
                  isSelected: _selectedProvider == option.provider,
                  isRecommended: option.isRecommended,
                  isEnabled: option.isAvailable,
                  onTap: () => _selectProvider(option.provider),
                  subtitle:
                      '${option.subtitle} • ${option.estimatedFee} • ${option.processingTime}',
                ),
              ),
            );
          },
        ),
      ],
    )
        .animate(controller: _animationController)
        .fadeIn(duration: AppTheme.animationDuration)
        .slideY(begin: 0.1, duration: AppTheme.animationDuration);
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          CircularProgressIndicator(
            valueColor:
                AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading payment methods...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withAlpha(77),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.error.withAlpha(77),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            color: theme.colorScheme.error,
            size: 32,
          ),
          const SizedBox(height: 12),
          Text(
            _errorMessage!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onErrorContainer,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadAvailableProviders,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}

/// Payment provider option model
class PaymentProviderOption {
  final PaymentProvider provider;
  final bool isAvailable;
  final bool isRecommended;
  final String subtitle;
  final String estimatedFee;
  final String processingTime;

  const PaymentProviderOption({
    required this.provider,
    required this.isAvailable,
    required this.isRecommended,
    required this.subtitle,
    required this.estimatedFee,
    required this.processingTime,
  });
}

/// Provider recommendation model
class ProviderRecommendation {
  final bool isRecommended;
  final String subtitle;

  const ProviderRecommendation({
    required this.isRecommended,
    required this.subtitle,
  });
}
