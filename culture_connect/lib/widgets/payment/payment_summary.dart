import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying a payment summary with flexible parameters
class PaymentSummary extends StatelessWidget {
  /// The base amount for the payment
  final double baseAmount;

  /// The currency to display (e.g., USD, EUR)
  final String currency;

  /// The title of the service or item
  final String title;

  /// The image URL for the service or item
  final String? imageUrl;

  /// The tax rate to apply (percentage)
  final double taxRate;

  /// The discount amount to apply
  final double? discountAmount;

  /// The discount code
  final String? discountCode;

  /// Additional details to display (key-value pairs)
  final Map<String, String>? additionalDetails;

  /// Creates a new payment summary
  const PaymentSummary({
    super.key,
    required this.baseAmount,
    required this.currency,
    required this.title,
    this.imageUrl,
    this.taxRate = 0.0,
    this.discountAmount,
    this.discountCode,
    this.additionalDetails,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Calculate amounts
    final subtotal = baseAmount;
    final taxAmount = subtotal * (taxRate / 100);
    final discount = discountAmount ?? 0.0;
    final total = subtotal + taxAmount - discount;

    // Format currency symbol
    final currencySymbol = _getCurrencySymbol(currency);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title section
            Row(
              children: [
                if (imageUrl != null) ...[
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      imageUrl!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        width: 60,
                        height: 60,
                        color: Colors.grey[300],
                        child: Icon(
                          Icons.image_not_supported,
                          size: 30,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Payment Summary',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Additional details
            if (additionalDetails != null && additionalDetails!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              ...additionalDetails!.entries.map((entry) => _buildDetailRow(
                    entry.key,
                    entry.value,
                    theme,
                  )),
              const SizedBox(height: 8),
            ],

            // Price breakdown
            const Divider(),
            const SizedBox(height: 12),
            _buildPriceRow(
              'Subtotal',
              '$currencySymbol${subtotal.toStringAsFixed(2)}',
              theme,
              isBold: false,
            ),

            if (taxRate > 0) ...[
              const SizedBox(height: 8),
              _buildPriceRow(
                'Tax ($taxRate%)',
                '$currencySymbol${taxAmount.toStringAsFixed(2)}',
                theme,
                isBold: false,
              ),
            ],

            if (discountAmount != null && discountAmount! > 0) ...[
              const SizedBox(height: 8),
              _buildPriceRow(
                'Discount${discountCode != null ? ' ($discountCode)' : ''}',
                '-$currencySymbol${discountAmount!.toStringAsFixed(2)}',
                theme,
                isBold: false,
                valueColor: Colors.green,
              ),
            ],

            const SizedBox(height: 12),
            const Divider(),
            const SizedBox(height: 12),

            _buildPriceRow(
              'Total',
              '$currencySymbol${total.toStringAsFixed(2)}',
              theme,
              isBold: true,
              fontSize: 18,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a detail row with a label and value
  Widget _buildDetailRow(String label, String value, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a price row with a label and value
  Widget _buildPriceRow(
    String label,
    String value,
    ThemeData theme, {
    bool isBold = false,
    double? fontSize,
    Color? valueColor,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: fontSize ?? 14,
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: fontSize ?? 14,
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            color: valueColor ?? AppTheme.textPrimaryColor,
          ),
        ),
      ],
    );
  }

  /// Gets the currency symbol for a currency code
  String _getCurrencySymbol(String currencyCode) {
    switch (currencyCode.toUpperCase()) {
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'CNY':
        return '¥';
      case 'INR':
        return '₹';
      case 'RUB':
        return '₽';
      case 'BRL':
        return 'R\$';
      case 'KRW':
        return '₩';
      case 'AUD':
        return 'A\$';
      case 'CAD':
        return 'C\$';
      case 'CHF':
        return 'CHF';
      case 'HKD':
        return 'HK\$';
      case 'SGD':
        return 'S\$';
      case 'SEK':
        return 'kr';
      case 'NZD':
        return 'NZ\$';
      case 'MXN':
        return 'Mex\$';
      case 'ZAR':
        return 'R';
      default:
        return currencyCode;
    }
  }
}
