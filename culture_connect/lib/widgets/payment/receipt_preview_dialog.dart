import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';

/// Dialog for previewing payment receipt details
class ReceiptPreviewDialog extends StatelessWidget {
  final String receiptId;
  final String transactionReference;
  final double amount;
  final PaymentMethodType paymentMethod;
  final PaymentProvider provider;
  final Booking booking;

  const ReceiptPreviewDialog({
    super.key,
    required this.receiptId,
    required this.transactionReference,
    required this.amount,
    required this.paymentMethod,
    required this.provider,
    required this.booking,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.maxFinite,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(theme),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildReceiptHeader(theme),
                    const SizedBox(height: 24),
                    _buildPaymentDetails(theme),
                    const SizedBox(height: 24),
                    _buildBookingDetails(theme),
                  ],
                ),
              ),
            ),

            // Actions
            _buildActions(theme, context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withAlpha(26),
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.receipt_long,
            color: theme.colorScheme.primary,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Payment Receipt',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                Text(
                  'Receipt #$receiptId',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(179),
                  ),
                ),
              ],
            ),
          ),
          Builder(
            builder: (context) => IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
              style: IconButton.styleFrom(
                backgroundColor: Colors.white.withAlpha(51),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 48,
          ),
          const SizedBox(height: 12),
          Text(
            'Payment Successful',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Transaction: $transactionReference',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentDetails(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Details',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildDetailRow(
            theme,
            'Payment Method',
            _getPaymentMethodName(paymentMethod),
          ),
          _buildDetailRow(
            theme,
            'Provider',
            _getProviderName(provider),
          ),
          _buildDetailRow(
            theme,
            'Status',
            'Completed',
            valueColor: Colors.green,
          ),
          _buildDetailRow(
            theme,
            'Date',
            _formatDate(DateTime.now()),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingDetails(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Booking Details',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildDetailRow(
            theme,
            'Experience',
            'Experience #${booking.experienceId}',
          ),
          _buildDetailRow(
            theme,
            'Date',
            _formatDate(booking.date),
          ),
          _buildDetailRow(
            theme,
            'Participants',
            '${booking.participantCount}',
          ),
          _buildDetailRow(
            theme,
            'Booking ID',
            booking.id,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    ThemeData theme,
    String label,
    String value, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: valueColor ?? theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions(ThemeData theme, BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(
          bottom: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _downloadReceipt(context),
              icon: const Icon(Icons.download, size: 18),
              label: const Text('Download'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: FilledButton.icon(
              onPressed: () => _shareReceipt(context),
              icon: const Icon(Icons.share, size: 18),
              label: const Text('Share'),
            ),
          ),
        ],
      ),
    );
  }

  void _downloadReceipt(BuildContext context) {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Receipt download started...'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareReceipt(BuildContext context) {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Receipt shared successfully!'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  String _getPaymentMethodName(PaymentMethodType method) {
    switch (method) {
      case PaymentMethodType.card:
        return 'Credit/Debit Card';
      case PaymentMethodType.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethodType.ussd:
        return 'USSD';
      case PaymentMethodType.mobileMoney:
        return 'Mobile Money';
      case PaymentMethodType.crypto:
        return 'Cryptocurrency';
    }
  }

  String _getProviderName(PaymentProvider provider) {
    switch (provider) {
      case PaymentProvider.stripe:
        return 'Stripe';
      case PaymentProvider.paystack:
        return 'Paystack';
      case PaymentProvider.busha:
        return 'Busha';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}
