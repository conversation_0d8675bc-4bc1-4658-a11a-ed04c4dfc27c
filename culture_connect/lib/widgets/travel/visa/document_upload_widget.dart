import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';

// Project imports
import 'package:culture_connect/models/travel/document/visa_assistance_models.dart';
import 'package:culture_connect/models/verification_model.dart';
import 'package:culture_connect/services/verification_service.dart';
import 'package:culture_connect/providers/user_verification_provider.dart';

/// Widget for uploading and managing visa documents
class DocumentUploadWidget extends ConsumerStatefulWidget {
  final DocumentRequirement requirement;
  final String? applicationId;
  final Function(List<File>)? onDocumentsSelected;
  final Function(VerificationRequest)? onUploadComplete;
  final bool allowMultiple;
  final bool showPreview;

  const DocumentUploadWidget({
    super.key,
    required this.requirement,
    this.applicationId,
    this.onDocumentsSelected,
    this.onUploadComplete,
    this.allowMultiple = false,
    this.showPreview = true,
  });

  @override
  ConsumerState<DocumentUploadWidget> createState() =>
      _DocumentUploadWidgetState();
}

class _DocumentUploadWidgetState extends ConsumerState<DocumentUploadWidget> {
  final List<File> _selectedFiles = [];
  bool _isUploading = false;
  String? _uploadError;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  _getDocumentIcon(),
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.requirement.displayName,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (widget.requirement.description.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          widget.requirement.description,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (widget.requirement.isMandatory)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6.0,
                      vertical: 2.0,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.error.withAlpha(26),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Required',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.error,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 16),

            // Requirements info
            if (widget.requirement.specificRequirements.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color:
                      theme.colorScheme.surfaceContainerHighest.withAlpha(77),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Requirements',
                          style: theme.textTheme.labelMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ...widget.requirement.specificRequirements.take(3).map(
                          (spec) => Padding(
                            padding: const EdgeInsets.only(bottom: 4.0),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.fiber_manual_record,
                                  size: 6,
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    spec,
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Upload area
            _buildUploadArea(theme),

            // Selected files preview
            if (_selectedFiles.isNotEmpty && widget.showPreview) ...[
              const SizedBox(height: 16),
              _buildFilePreview(theme),
            ],

            // Error message
            if (_uploadError != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: theme.colorScheme.error.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: theme.colorScheme.error,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _uploadError!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.error,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Upload button
            if (_selectedFiles.isNotEmpty) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: FilledButton.icon(
                  onPressed: _isUploading ? null : _uploadDocuments,
                  icon: _isUploading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.cloud_upload),
                  label:
                      Text(_isUploading ? 'Uploading...' : 'Upload Documents'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUploadArea(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(128),
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(12),
        color: theme.colorScheme.surfaceContainerHighest.withAlpha(51),
      ),
      child: Column(
        children: [
          Icon(
            Icons.cloud_upload_outlined,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 12),
          Text(
            'Upload ${widget.requirement.displayName}',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose from camera, gallery, or files',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildUploadOption(
                theme,
                'Camera',
                Icons.camera_alt,
                () => _pickFromCamera(),
              ),
              _buildUploadOption(
                theme,
                'Gallery',
                Icons.photo_library,
                () => _pickFromGallery(),
              ),
              _buildUploadOption(
                theme,
                'Files',
                Icons.folder,
                () => _pickFromFiles(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUploadOption(
    ThemeData theme,
    String label,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withAlpha(128),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 24,
              color: theme.colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: theme.textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilePreview(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Selected Files',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _selectedFiles.length,
          itemBuilder: (context, index) {
            final file = _selectedFiles[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: Icon(
                  _getFileIcon(file.path),
                  color: theme.colorScheme.primary,
                ),
                title: Text(
                  file.path.split('/').last,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                subtitle: Text(
                  _formatFileSize(file.lengthSync()),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                trailing: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => _removeFile(index),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Future<void> _pickFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (image != null) {
        _addFile(File(image.path));
      }
    } catch (e) {
      _setError('Failed to capture image: $e');
    }
  }

  Future<void> _pickFromGallery() async {
    try {
      if (widget.allowMultiple) {
        final List<XFile> images = await _imagePicker.pickMultiImage(
          imageQuality: 85,
        );

        for (final image in images) {
          _addFile(File(image.path));
        }
      } else {
        final XFile? image = await _imagePicker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 85,
        );

        if (image != null) {
          _addFile(File(image.path));
        }
      }
    } catch (e) {
      _setError('Failed to pick image: $e');
    }
  }

  Future<void> _pickFromFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: widget.allowMultiple,
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'],
      );

      if (result != null) {
        for (final file in result.files) {
          if (file.path != null) {
            _addFile(File(file.path!));
          }
        }
      }
    } catch (e) {
      _setError('Failed to pick file: $e');
    }
  }

  void _addFile(File file) {
    setState(() {
      if (!widget.allowMultiple) {
        _selectedFiles.clear();
      }
      _selectedFiles.add(file);
      _uploadError = null;
    });

    widget.onDocumentsSelected?.call(_selectedFiles);
  }

  void _removeFile(int index) {
    setState(() {
      _selectedFiles.removeAt(index);
    });

    widget.onDocumentsSelected?.call(_selectedFiles);
  }

  void _setError(String error) {
    setState(() {
      _uploadError = error;
    });
  }

  Future<void> _uploadDocuments() async {
    if (_selectedFiles.isEmpty) return;

    setState(() {
      _isUploading = true;
      _uploadError = null;
    });

    try {
      final verificationService = ref.read(verificationServiceProvider);

      VerificationRequest request;
      if (widget.applicationId != null) {
        // Upload as visa-specific documents
        request = await verificationService.submitVisaDocumentVerification(
          applicationId: widget.applicationId!,
          requirement: widget.requirement,
          documents: _selectedFiles,
        );
      } else {
        // Upload as general verification documents
        request = await verificationService.submitVerificationRequest(
          type: VerificationType.identity,
          documents: _selectedFiles,
        );
      }

      widget.onUploadComplete?.call(request);

      // Refresh verification status
      ref.invalidate(userVerificationProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Documents uploaded successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        setState(() {
          _selectedFiles.clear();
        });
      }
    } catch (e) {
      _setError('Upload failed: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  IconData _getDocumentIcon() {
    final type = widget.requirement.documentType.toLowerCase();
    switch (type) {
      case 'passport':
        return Icons.book;
      case 'visa':
        return Icons.description;
      case 'photo':
      case 'photograph':
        return Icons.photo_camera;
      case 'bank_statement':
      case 'financial':
        return Icons.account_balance;
      case 'invitation':
      case 'letter':
        return Icons.mail;
      case 'insurance':
        return Icons.security;
      case 'ticket':
      case 'flight':
        return Icons.flight;
      case 'hotel':
      case 'accommodation':
        return Icons.hotel;
      default:
        return Icons.description;
    }
  }

  IconData _getFileIcon(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'jpg':
      case 'jpeg':
      case 'png':
        return Icons.image;
      case 'doc':
      case 'docx':
        return Icons.description;
      default:
        return Icons.insert_drive_file;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
