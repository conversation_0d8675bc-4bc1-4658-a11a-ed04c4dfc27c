import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Simple appointment slot data class
class AppointmentSlot {
  final String id;
  final DateTime startTime;
  final DateTime endTime;
  final bool isAvailable;

  const AppointmentSlot({
    required this.id,
    required this.startTime,
    required this.endTime,
    this.isAvailable = true,
  });
}

/// Simple visa appointment data class
class VisaAppointment {
  final String id;
  final String embassyId;
  final String serviceType;
  final DateTime appointmentDate;
  final DateTime startTime;
  final DateTime endTime;
  final String status;
  final DateTime bookedAt;

  const VisaAppointment({
    required this.id,
    required this.embassyId,
    required this.serviceType,
    required this.appointmentDate,
    required this.startTime,
    required this.endTime,
    required this.status,
    required this.bookedAt,
  });
}

/// Widget for scheduling visa appointments
class AppointmentScheduler extends ConsumerStatefulWidget {
  final String embassyId;
  final String serviceType;
  final Function(AppointmentSlot)? onSlotSelected;
  final Function(VisaAppointment)? onAppointmentBooked;

  const AppointmentScheduler({
    super.key,
    required this.embassyId,
    required this.serviceType,
    this.onSlotSelected,
    this.onAppointmentBooked,
  });

  @override
  ConsumerState<AppointmentScheduler> createState() =>
      _AppointmentSchedulerState();
}

class _AppointmentSchedulerState extends ConsumerState<AppointmentScheduler> {
  DateTime _selectedDate = DateTime.now();
  AppointmentSlot? _selectedSlot;
  bool _isBooking = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Schedule Appointment',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Date selector
            _buildDateSelector(theme),

            const SizedBox(height: 16),

            // Available time slots
            _buildTimeSlots(theme),

            const SizedBox(height: 16),

            // Selected slot info
            if (_selectedSlot != null) ...[
              _buildSelectedSlotInfo(theme),
              const SizedBox(height: 16),
            ],

            // Book appointment button
            SizedBox(
              width: double.infinity,
              child: FilledButton.icon(
                onPressed: _selectedSlot != null && !_isBooking
                    ? _bookAppointment
                    : null,
                icon: _isBooking
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.event_available),
                label: Text(_isBooking ? 'Booking...' : 'Book Appointment'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Date',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),

        // Date picker
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 14, // Next 14 days
            itemBuilder: (context, index) {
              final date = DateTime.now().add(Duration(days: index + 1));
              final isSelected = _isSameDay(date, _selectedDate);
              final isWeekend = date.weekday == DateTime.saturday ||
                  date.weekday == DateTime.sunday;

              return GestureDetector(
                onTap: isWeekend ? null : () => _selectDate(date),
                child: Container(
                  width: 60,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : isWeekend
                            ? theme.colorScheme.surfaceContainerHighest
                                .withAlpha(77)
                            : theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                    border: isSelected
                        ? null
                        : Border.all(
                            color: theme.colorScheme.outline.withAlpha(77),
                          ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _getDayName(date),
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: isSelected
                              ? theme.colorScheme.onPrimary
                              : isWeekend
                                  ? theme.colorScheme.onSurfaceVariant
                                      .withAlpha(128)
                                  : theme.colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        date.day.toString(),
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: isSelected
                              ? theme.colorScheme.onPrimary
                              : isWeekend
                                  ? theme.colorScheme.onSurfaceVariant
                                      .withAlpha(128)
                                  : theme.colorScheme.onSurface,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSlots(ThemeData theme) {
    // Mock available slots - in real app, this would come from the service
    final availableSlots = _generateMockSlots();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Times',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        if (availableSlots.isEmpty) ...[
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withAlpha(77),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Text(
                  'No available slots for this date',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ] else ...[
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: availableSlots.map((slot) {
              final isSelected = _selectedSlot?.id == slot.id;

              return GestureDetector(
                onTap: () => _selectSlot(slot),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16.0,
                    vertical: 8.0,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.outline.withAlpha(77),
                    ),
                  ),
                  child: Text(
                    _formatTime(slot.startTime),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: isSelected
                          ? theme.colorScheme.onPrimary
                          : theme.colorScheme.onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildSelectedSlotInfo(ThemeData theme) {
    if (_selectedSlot == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withAlpha(77),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.primary.withAlpha(77),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.event_available,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selected Appointment',
                  style: theme.textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_formatDate(_selectedDate)} at ${_formatTime(_selectedSlot!.startTime)}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<AppointmentSlot> _generateMockSlots() {
    // Generate mock available slots for the selected date
    if (_isSameDay(_selectedDate, DateTime.now()) ||
        _selectedDate.weekday == DateTime.saturday ||
        _selectedDate.weekday == DateTime.sunday) {
      return [];
    }

    final slots = <AppointmentSlot>[];
    final baseTime =
        DateTime(_selectedDate.year, _selectedDate.month, _selectedDate.day, 9);

    for (int i = 0; i < 8; i++) {
      final startTime = baseTime.add(Duration(hours: i));
      if (startTime.hour >= 17) break; // No slots after 5 PM

      slots.add(AppointmentSlot(
        id: 'slot_${_selectedDate.millisecondsSinceEpoch}_$i',
        startTime: startTime,
        endTime: startTime.add(const Duration(minutes: 30)),
        isAvailable: true,
      ));
    }

    return slots;
  }

  void _selectDate(DateTime date) {
    setState(() {
      _selectedDate = date;
      _selectedSlot = null; // Reset selected slot when date changes
    });
  }

  void _selectSlot(AppointmentSlot slot) {
    setState(() {
      _selectedSlot = slot;
    });
    widget.onSlotSelected?.call(slot);
  }

  Future<void> _bookAppointment() async {
    if (_selectedSlot == null) return;

    setState(() {
      _isBooking = true;
    });

    try {
      // Mock booking process - in real app, this would call the service
      await Future.delayed(const Duration(seconds: 2));

      final appointment = VisaAppointment(
        id: 'apt_${DateTime.now().millisecondsSinceEpoch}',
        embassyId: widget.embassyId,
        serviceType: widget.serviceType,
        appointmentDate: _selectedDate,
        startTime: _selectedSlot!.startTime,
        endTime: _selectedSlot!.endTime,
        status: 'confirmed',
        bookedAt: DateTime.now(),
      );

      widget.onAppointmentBooked?.call(appointment);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment booked successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to book appointment: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isBooking = false;
        });
      }
    }
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  String _getDayName(DateTime date) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[date.weekday - 1];
  }

  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  String _formatTime(DateTime time) {
    final hour = time.hour;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }
}
