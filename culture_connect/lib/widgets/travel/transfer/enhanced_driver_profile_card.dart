import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/transfer/transfer_driver.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Enhanced driver profile card with AirBnB-inspired design
class EnhancedDriverProfileCard extends StatelessWidget {
  /// The driver to display
  final TransferDriver driver;

  /// Whether to show detailed information
  final bool showDetails;

  /// Whether the card is compact
  final bool isCompact;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Creates a new enhanced driver profile card
  const EnhancedDriverProfileCard({
    super.key,
    required this.driver,
    this.showDetails = true,
    this.isCompact = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(isCompact ? 12 : 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.grey.withAlpha(51),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: isCompact ? _buildCompactLayout() : _buildFullLayout(),
      ),
    );
  }

  /// Build compact layout for smaller spaces
  Widget _buildCompactLayout() {
    return Row(
      children: [
        // Driver Photo
        _buildDriverPhoto(size: 40),
        const SizedBox(width: 12),

        // Driver Info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      driver.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimaryColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (driver.isVerified) _buildVerificationBadge(size: 16),
                ],
              ),
              const SizedBox(height: 4),
              _buildRatingRow(compact: true),
            ],
          ),
        ),
      ],
    );
  }

  /// Build full layout with detailed information
  Widget _buildFullLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header Section
        Row(
          children: [
            _buildDriverPhoto(size: 60),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          driver.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ),
                      if (driver.isVerified) _buildVerificationBadge(),
                    ],
                  ),
                  const SizedBox(height: 8),
                  _buildRatingRow(),
                  const SizedBox(height: 8),
                  _buildExperienceRow(),
                ],
              ),
            ),
          ],
        ),

        if (showDetails) ...[
          const SizedBox(height: 16),
          _buildLanguagesSection(),
          const SizedBox(height: 12),
          _buildContactSection(),
        ],
      ],
    );
  }

  /// Build driver photo with placeholder
  Widget _buildDriverPhoto({double size = 60}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(size / 2),
        border: Border.all(
          color: AppTheme.primaryColor.withAlpha(51),
          width: 2,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(size / 2),
        child: Image.network(
          driver.photoUrl,
          width: size,
          height: size,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              width: size,
              height: size,
              color: AppTheme.surfaceColor,
              child: Icon(
                Icons.person,
                size: size * 0.6,
                color: AppTheme.textSecondaryColor,
              ),
            );
          },
        ),
      ),
    );
  }

  /// Build verification badge
  Widget _buildVerificationBadge({double size = 20}) {
    return Container(
      padding: EdgeInsets.all(size * 0.1),
      decoration: BoxDecoration(
        color: AppTheme.successColor,
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: Icon(
        Icons.verified,
        size: size * 0.8,
        color: Colors.white,
      ),
    );
  }

  /// Build rating row
  Widget _buildRatingRow({bool compact = false}) {
    return Row(
      children: [
        Icon(
          Icons.star,
          size: compact ? 14 : 16,
          color: Colors.amber,
        ),
        const SizedBox(width: 4),
        Text(
          driver.rating.toStringAsFixed(1),
          style: TextStyle(
            fontSize: compact ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '(${driver.reviewCount} reviews)',
          style: TextStyle(
            fontSize: compact ? 12 : 14,
            color: AppTheme.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  /// Build experience row
  Widget _buildExperienceRow() {
    return Row(
      children: [
        const Icon(
          Icons.drive_eta,
          size: 16,
          color: AppTheme.textSecondaryColor,
        ),
        const SizedBox(width: 4),
        Text(
          '${driver.yearsOfExperience} years experience',
          style: const TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  /// Build languages section
  Widget _buildLanguagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Languages',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: driver.languages.map((language) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withAlpha(26),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                language,
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Build contact section
  Widget _buildContactSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Contact Information',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 8),
        if (driver.maskedPhoneNumber != null)
          _buildContactItem(
            Icons.phone,
            'Phone',
            driver.maskedPhoneNumber!,
          ),
        if (driver.maskedLicenseNumber != null) ...[
          const SizedBox(height: 4),
          _buildContactItem(
            Icons.badge,
            'License',
            driver.maskedLicenseNumber!,
          ),
        ],
      ],
    );
  }

  /// Build contact item
  Widget _buildContactItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppTheme.textSecondaryColor,
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(
            fontSize: 12,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppTheme.textPrimaryColor,
          ),
        ),
      ],
    );
  }
}
