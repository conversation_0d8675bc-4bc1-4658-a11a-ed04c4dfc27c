// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';

// Project imports
import 'package:culture_connect/models/travel/appointment/embassy_appointment_models.dart';
import 'package:culture_connect/services/travel/appointment/embassy_appointment_service.dart';

/// Provider for the selected appointment date
final selectedAppointmentDateProvider = StateProvider<DateTime?>((ref) => null);

/// Provider for the selected appointment time slot
final selectedAppointmentTimeSlotProvider =
    StateProvider<AppointmentTimeSlot?>((ref) => null);

/// Provider for the selected appointment type
final selectedAppointmentTypeProvider =
    StateProvider<AppointmentType?>((ref) => null);

/// A widget for selecting embassy appointment date and time
class EmbassyAppointmentCalendar extends ConsumerStatefulWidget {
  /// Embassy ID for which to show availability
  final String embassyId;

  /// Allowed appointment types
  final List<AppointmentType> allowedTypes;

  /// Callback when appointment slot is selected
  final Function(DateTime, AppointmentTimeSlot, AppointmentType)?
      onAppointmentSelected;

  /// Minimum advance booking days
  final int minAdvanceDays;

  /// Maximum advance booking days
  final int maxAdvanceDays;

  const EmbassyAppointmentCalendar({
    super.key,
    required this.embassyId,
    required this.allowedTypes,
    this.onAppointmentSelected,
    this.minAdvanceDays = 1,
    this.maxAdvanceDays = 90,
  });

  @override
  ConsumerState<EmbassyAppointmentCalendar> createState() =>
      _EmbassyAppointmentCalendarState();
}

class _EmbassyAppointmentCalendarState
    extends ConsumerState<EmbassyAppointmentCalendar>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DateTime _focusedDay = DateTime.now();
  List<EmbassyAvailability> _availabilities = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAvailability();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Load embassy availability for the current month
  Future<void> _loadAvailability() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final appointmentService = ref.read(embassyAppointmentServiceProvider);
      final startDate = DateTime(_focusedDay.year, _focusedDay.month, 1);
      final endDate = DateTime(_focusedDay.year, _focusedDay.month + 1, 0);

      final availabilities = await appointmentService.getEmbassyAvailability(
        embassyId: widget.embassyId,
        startDate: startDate,
        endDate: endDate,
        appointmentTypes: widget.allowedTypes,
      );

      if (mounted) {
        setState(() {
          _availabilities = availabilities;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load availability: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Check if a day has available appointments
  bool _isDayAvailable(DateTime day) {
    // Check minimum advance booking
    final minDate = DateTime.now().add(Duration(days: widget.minAdvanceDays));
    if (day.isBefore(minDate)) return false;

    // Check maximum advance booking
    final maxDate = DateTime.now().add(Duration(days: widget.maxAdvanceDays));
    if (day.isAfter(maxDate)) return false;

    // Check if there's availability for this day
    final availability = _availabilities.firstWhere(
      (avail) => isSameDay(avail.date, day),
      orElse: () => EmbassyAvailability(
        embassyId: widget.embassyId,
        date: day,
        timeSlots: const [],
        isClosed: true,
        notes: null,
        closureReason: null,
      ),
    );

    return !availability.isClosed && availability.availableSlots.isNotEmpty;
  }

  /// Get available time slots for a specific date
  List<AppointmentTimeSlot> _getAvailableTimeSlots(DateTime date) {
    final availability = _availabilities.firstWhere(
      (avail) => isSameDay(avail.date, date),
      orElse: () => EmbassyAvailability(
        embassyId: widget.embassyId,
        date: date,
        timeSlots: const [],
        isClosed: true,
        notes: null,
        closureReason: null,
      ),
    );

    if (availability.isClosed) return [];

    return availability.availableSlots.where((slot) {
      return slot.allowedTypes
          .any((type) => widget.allowedTypes.contains(type));
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedDate = ref.watch(selectedAppointmentDateProvider);
    final selectedTimeSlot = ref.watch(selectedAppointmentTimeSlotProvider);
    final selectedType = ref.watch(selectedAppointmentTypeProvider);

    return Column(
      children: [
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Calendar'),
            Tab(text: 'Time Slots'),
            Tab(text: 'Appointment Type'),
          ],
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface,
          indicatorColor: theme.colorScheme.primary,
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 400,
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildCalendarTab(selectedDate),
              _buildTimeSlotsTab(selectedDate, selectedTimeSlot),
              _buildAppointmentTypeTab(selectedType),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCalendarTab(DateTime? selectedDate) {
    final theme = Theme.of(context);

    return Column(
      children: [
        if (_isLoading)
          const Center(child: CircularProgressIndicator())
        else
          Expanded(
            child: TableCalendar<String>(
              firstDay:
                  DateTime.now().add(Duration(days: widget.minAdvanceDays)),
              lastDay:
                  DateTime.now().add(Duration(days: widget.maxAdvanceDays)),
              focusedDay: _focusedDay,
              selectedDayPredicate: (day) => isSameDay(selectedDate, day),
              onDaySelected: (selectedDay, focusedDay) {
                if (!_isDayAvailable(selectedDay)) {
                  if (!mounted) return;
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content:
                          Text('No available appointment slots for this day'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                  return;
                }

                if (!mounted) return;

                setState(() {
                  _focusedDay = focusedDay;
                });

                ref.read(selectedAppointmentDateProvider.notifier).state =
                    selectedDay;
                ref.read(selectedAppointmentTimeSlotProvider.notifier).state =
                    null;
                ref.read(selectedAppointmentTypeProvider.notifier).state = null;

                // Switch to time slots tab
                _tabController.animateTo(1);
              },
              onPageChanged: (focusedDay) {
                if (!mounted) return;
                setState(() {
                  _focusedDay = focusedDay;
                });
                _loadAvailability();
              },
              calendarStyle: CalendarStyle(
                outsideDaysVisible: false,
                weekendTextStyle: TextStyle(color: theme.colorScheme.error),
                holidayTextStyle: TextStyle(color: theme.colorScheme.error),
              ),
              calendarBuilders: CalendarBuilders(
                defaultBuilder: (context, day, focusedDay) {
                  return _buildCalendarDay(day, _isDayAvailable(day));
                },
                selectedBuilder: (context, day, focusedDay) {
                  return Container(
                    margin: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${day.day}',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                  );
                },
                todayBuilder: (context, day, focusedDay) {
                  return Container(
                    margin: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.secondary.withAlpha(128),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${day.day}',
                        style: TextStyle(color: theme.colorScheme.onSecondary),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLegendItem(Colors.green, 'Available'),
            const SizedBox(width: 16),
            _buildLegendItem(Colors.grey, 'Unavailable'),
          ],
        ),
      ],
    );
  }

  Widget _buildCalendarDay(DateTime day, bool isAvailable) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: isAvailable
            ? Colors.green.withAlpha(51)
            : Colors.grey.withAlpha(51),
        shape: BoxShape.circle,
        border: Border.all(
          color: isAvailable ? Colors.green : Colors.grey,
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          '${day.day}',
          style: TextStyle(
            color: isAvailable ? theme.colorScheme.onSurface : Colors.grey,
            fontWeight: isAvailable ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildLegendItem(Color color, String label) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color.withAlpha(51),
            shape: BoxShape.circle,
            border: Border.all(color: color),
          ),
        ),
        const SizedBox(width: 4),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  Widget _buildTimeSlotsTab(
      DateTime? selectedDate, AppointmentTimeSlot? selectedTimeSlot) {
    if (selectedDate == null) {
      return const Center(
        child: Text('Please select a date first'),
      );
    }

    final availableSlots = _getAvailableTimeSlots(selectedDate);

    if (availableSlots.isEmpty) {
      return const Center(
        child: Text('No available time slots for this day'),
      );
    }

    final dateFormat = DateFormat('EEEE, MMMM d, yyyy');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Selected Date: ${dateFormat.format(selectedDate)}',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        Text(
          'Available Time Slots:',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        Expanded(
          child: ListView.builder(
            itemCount: availableSlots.length,
            itemBuilder: (context, index) {
              final slot = availableSlots[index];
              final isSelected = selectedTimeSlot == slot;
              final timeFormat = DateFormat('h:mm a');

              return Card(
                elevation: isSelected ? 4 : 1,
                margin: const EdgeInsets.only(bottom: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: isSelected
                      ? BorderSide(
                          color: Theme.of(context).colorScheme.primary,
                          width: 2,
                        )
                      : BorderSide.none,
                ),
                child: ListTile(
                  title: Text(
                    '${timeFormat.format(slot.startTime)} - ${timeFormat.format(slot.endTime)}',
                    style: TextStyle(
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Duration: ${slot.duration.inMinutes} minutes'),
                      Text('Available slots: ${slot.remainingSlots}'),
                      Text(
                          'Allowed types: ${slot.allowedTypes.map((t) => t.toString().split('.').last).join(', ')}'),
                    ],
                  ),
                  trailing: isSelected
                      ? Icon(
                          Icons.check_circle,
                          color: Theme.of(context).colorScheme.primary,
                        )
                      : null,
                  onTap: () {
                    ref
                        .read(selectedAppointmentTimeSlotProvider.notifier)
                        .state = slot;
                    ref.read(selectedAppointmentTypeProvider.notifier).state =
                        null;

                    // Switch to appointment type tab
                    _tabController.animateTo(2);
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAppointmentTypeTab(AppointmentType? selectedType) {
    final selectedTimeSlot = ref.watch(selectedAppointmentTimeSlotProvider);

    if (selectedTimeSlot == null) {
      return const Center(
        child: Text('Please select a time slot first'),
      );
    }

    // Filter allowed types based on the selected time slot
    final allowedTypesForSlot = widget.allowedTypes
        .where((type) => selectedTimeSlot.allowedTypes.contains(type))
        .toList();

    if (allowedTypesForSlot.isEmpty) {
      return const Center(
        child:
            Text('No appointment types available for the selected time slot'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Appointment Type:',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: allowedTypesForSlot.length,
            itemBuilder: (context, index) {
              final type = allowedTypesForSlot[index];
              final isSelected = selectedType == type;

              return Card(
                elevation: isSelected ? 4 : 1,
                margin: const EdgeInsets.only(bottom: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: isSelected
                      ? BorderSide(
                          color: Theme.of(context).colorScheme.primary,
                          width: 2,
                        )
                      : BorderSide.none,
                ),
                child: ListTile(
                  title: Text(
                    _getAppointmentTypeDisplayName(type),
                    style: TextStyle(
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                  subtitle: Text(_getAppointmentTypeDescription(type)),
                  trailing: isSelected
                      ? Icon(
                          Icons.check_circle,
                          color: Theme.of(context).colorScheme.primary,
                        )
                      : null,
                  onTap: () {
                    ref.read(selectedAppointmentTypeProvider.notifier).state =
                        type;

                    // Trigger callback if all selections are made
                    final selectedDate =
                        ref.read(selectedAppointmentDateProvider);
                    final selectedSlot =
                        ref.read(selectedAppointmentTimeSlotProvider);

                    if (selectedDate != null &&
                        selectedSlot != null &&
                        widget.onAppointmentSelected != null) {
                      widget.onAppointmentSelected!(
                          selectedDate, selectedSlot, type);
                    }
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  String _getAppointmentTypeDisplayName(AppointmentType type) {
    switch (type) {
      case AppointmentType.visaInterview:
        return 'Visa Interview';
      case AppointmentType.documentSubmission:
        return 'Document Submission';
      case AppointmentType.biometricCollection:
        return 'Biometric Collection';
      case AppointmentType.consultation:
        return 'Consultation';
      case AppointmentType.followUp:
        return 'Follow-up';
      case AppointmentType.emergency:
        return 'Emergency';
    }
  }

  String _getAppointmentTypeDescription(AppointmentType type) {
    switch (type) {
      case AppointmentType.visaInterview:
        return 'In-person interview with consular officer';
      case AppointmentType.documentSubmission:
        return 'Submit required documents for visa application';
      case AppointmentType.biometricCollection:
        return 'Fingerprint and photo collection';
      case AppointmentType.consultation:
        return 'General consultation about visa requirements';
      case AppointmentType.followUp:
        return 'Follow-up appointment for additional requirements';
      case AppointmentType.emergency:
        return 'Emergency appointment for urgent travel needs';
    }
  }
}
