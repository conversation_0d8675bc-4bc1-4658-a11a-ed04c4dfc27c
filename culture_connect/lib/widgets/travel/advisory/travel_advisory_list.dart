import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/advisory/travel_advisory_models.dart';
import 'package:culture_connect/services/travel/advisory/travel_advisory_service.dart';
import 'package:culture_connect/widgets/travel/advisory/travel_advisory_card.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_display.dart';

/// Widget for displaying a list of travel advisories
class TravelAdvisoryList extends ConsumerStatefulWidget {
  final List<String>? countryCodes;
  final List<AdvisoryType>? advisoryTypes;
  final List<AdvisorySeverity>? severityLevels;
  final bool showCountry;
  final bool compact;
  final String? emptyMessage;
  final VoidCallback? onRefresh;
  final Function(TravelAdvisory)? onAdvisoryTap;

  const TravelAdvisoryList({
    super.key,
    this.countryCodes,
    this.advisoryTypes,
    this.severityLevels,
    this.showCountry = true,
    this.compact = false,
    this.emptyMessage,
    this.onRefresh,
    this.onAdvisoryTap,
  });

  @override
  ConsumerState<TravelAdvisoryList> createState() => _TravelAdvisoryListState();
}

class _TravelAdvisoryListState extends ConsumerState<TravelAdvisoryList> {
  bool _isRefreshing = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Determine which provider to use based on parameters
    final advisoriesAsync = widget.countryCodes != null
        ? ref.watch(advisoriesByCountriesProvider(widget.countryCodes!))
        : ref.watch(allActiveAdvisoriesProvider);

    return advisoriesAsync.when(
      data: (advisories) {
        // Filter advisories based on criteria
        final filteredAdvisories = _filterAdvisories(advisories);

        if (filteredAdvisories.isEmpty) {
          return _buildEmptyState(theme);
        }

        return RefreshIndicator(
          onRefresh: _handleRefresh,
          child: ListView.builder(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.symmetric(
              vertical: widget.compact ? 8.0 : 16.0,
            ),
            itemCount: filteredAdvisories.length,
            itemBuilder: (context, index) {
              final advisory = filteredAdvisories[index];
              return TravelAdvisoryCard(
                advisory: advisory,
                showCountry: widget.showCountry,
                compact: widget.compact,
                onTap: () => widget.onAdvisoryTap?.call(advisory),
              );
            },
          ),
        );
      },
      loading: () => _buildLoadingState(),
      error: (error, stackTrace) => _buildErrorState(theme, error),
    );
  }

  List<TravelAdvisory> _filterAdvisories(List<TravelAdvisory> advisories) {
    return advisories.where((advisory) {
      // Filter by advisory types
      if (widget.advisoryTypes != null &&
          widget.advisoryTypes!.isNotEmpty &&
          !widget.advisoryTypes!.contains(advisory.type)) {
        return false;
      }

      // Filter by severity levels
      if (widget.severityLevels != null &&
          widget.severityLevels!.isNotEmpty &&
          !widget.severityLevels!.contains(advisory.severity)) {
        return false;
      }

      return true;
    }).toList();
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.travel_explore,
              size: 64,
              color: theme.colorScheme.onSurfaceVariant.withAlpha(128),
            ),
            const SizedBox(height: 16),
            Text(
              widget.emptyMessage ?? 'No travel advisories found',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Check back later for updates or try refreshing.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant.withAlpha(179),
              ),
              textAlign: TextAlign.center,
            ),
            if (widget.onRefresh != null) ...[
              const SizedBox(height: 24),
              FilledButton.icon(
                onPressed: _isRefreshing ? null : _handleRefresh,
                icon: _isRefreshing
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.refresh),
                label: Text(_isRefreshing ? 'Refreshing...' : 'Refresh'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32.0),
        child: LoadingIndicator(),
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme, Object error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: ErrorDisplay(
          message:
              'Unable to load travel advisories. Please check your connection and try again.',
          showRetry: true,
          onRetry: _handleRefresh,
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      if (widget.onRefresh != null) {
        widget.onRefresh!();
      } else {
        // Default refresh behavior
        final service = ref.read(travelAdvisoryServiceProvider);
        if (widget.countryCodes != null) {
          await service.refreshAdvisoriesForCountries(widget.countryCodes!);
        } else {
          await service.getAllActiveAdvisories();
        }
      }

      // Refresh the provider
      if (widget.countryCodes != null) {
        ref.invalidate(advisoriesByCountriesProvider(widget.countryCodes!));
      } else {
        ref.invalidate(allActiveAdvisoriesProvider);
      }
    } catch (e) {
      // Error handling is done by the provider
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }
}

/// Widget for displaying travel advisories in a horizontal scrollable list
class TravelAdvisoryHorizontalList extends ConsumerWidget {
  final List<String>? countryCodes;
  final List<AdvisoryType>? advisoryTypes;
  final List<AdvisorySeverity>? severityLevels;
  final Function(TravelAdvisory)? onAdvisoryTap;
  final String? title;
  final VoidCallback? onSeeAll;

  const TravelAdvisoryHorizontalList({
    super.key,
    this.countryCodes,
    this.advisoryTypes,
    this.severityLevels,
    this.onAdvisoryTap,
    this.title,
    this.onSeeAll,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Determine which provider to use based on parameters
    final advisoriesAsync = countryCodes != null
        ? ref.watch(advisoriesByCountriesProvider(countryCodes!))
        : ref.watch(allActiveAdvisoriesProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Text(
                  title!,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (onSeeAll != null)
                  TextButton(
                    onPressed: onSeeAll,
                    child: const Text('See All'),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],
        SizedBox(
          height: 160,
          child: advisoriesAsync.when(
            data: (advisories) {
              // Filter and limit advisories
              final filteredAdvisories =
                  _filterAdvisories(advisories).take(10).toList();

              if (filteredAdvisories.isEmpty) {
                return _buildEmptyHorizontalState(theme);
              }

              return ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                itemCount: filteredAdvisories.length,
                itemBuilder: (context, index) {
                  final advisory = filteredAdvisories[index];
                  return SizedBox(
                    width: 280,
                    child: TravelAdvisoryCard(
                      advisory: advisory,
                      showCountry: true,
                      compact: true,
                      onTap: () => onAdvisoryTap?.call(advisory),
                    ),
                  );
                },
              );
            },
            loading: () => const Center(
              child: LoadingIndicator(),
            ),
            error: (error, stackTrace) => Center(
              child: Text(
                'Failed to load advisories',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<TravelAdvisory> _filterAdvisories(List<TravelAdvisory> advisories) {
    return advisories.where((advisory) {
      // Filter by advisory types
      if (advisoryTypes != null &&
          advisoryTypes!.isNotEmpty &&
          !advisoryTypes!.contains(advisory.type)) {
        return false;
      }

      // Filter by severity levels
      if (severityLevels != null &&
          severityLevels!.isNotEmpty &&
          !severityLevels!.contains(advisory.severity)) {
        return false;
      }

      return true;
    }).toList();
  }

  Widget _buildEmptyHorizontalState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.travel_explore,
            size: 32,
            color: theme.colorScheme.onSurfaceVariant.withAlpha(128),
          ),
          const SizedBox(height: 8),
          Text(
            'No advisories available',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}
