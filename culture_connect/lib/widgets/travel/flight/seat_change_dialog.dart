import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/models/travel/flight/booking_info.dart';
import 'package:culture_connect/models/travel/flight/passenger_info.dart';
import 'package:culture_connect/services/travel/flight_search_service.dart';

/// Dialog for changing seat assignments for passengers
class SeatChangeDialog extends StatefulWidget {
  /// The booking information
  final BookingInfo booking;

  /// Callback when seat is changed
  final Function(PassengerInfo passenger, String newSeat) onSeatChanged;

  /// Creates a new seat change dialog
  const SeatChangeDialog({
    super.key,
    required this.booking,
    required this.onSeatChanged,
  });

  @override
  State<SeatChangeDialog> createState() => _SeatChangeDialogState();
}

class _SeatChangeDialogState extends State<SeatChangeDialog> {
  PassengerInfo? _selectedPassenger;
  String? _selectedSeat;
  bool _isLoading = false;
  List<Seat> _availableSeats = [];

  @override
  void initState() {
    super.initState();
    _loadAvailableSeats();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 500,
          maxHeight: 700,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(theme),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Flight information
                    _buildFlightInfo(theme),

                    const SizedBox(height: 24),

                    // Passenger selection
                    _buildPassengerSelection(theme),

                    const SizedBox(height: 24),

                    // Current seat info
                    if (_selectedPassenger != null)
                      _buildCurrentSeatInfo(theme),

                    const SizedBox(height: 24),

                    // Available seats
                    if (_selectedPassenger != null) _buildAvailableSeats(theme),
                  ],
                ),
              ),
            ),

            // Actions
            _buildActions(context, theme),
          ],
        ),
      ),
    );
  }

  /// Build dialog header
  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withAlpha(204),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.airline_seat_recline_normal,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Change Seat',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Select new seat assignment',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withAlpha(204),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white.withAlpha(51),
            ),
          ),
        ],
      ),
    );
  }

  /// Build flight information
  Widget _buildFlightInfo(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.flight,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Flight Information',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow(theme, 'Flight', widget.booking.flight.flightNumber),
            _buildInfoRow(theme, 'Route',
                '${widget.booking.flight.departureAirport} → ${widget.booking.flight.arrivalAirport}'),
            _buildInfoRow(theme, 'Date',
                _formatDate(widget.booking.flight.departureDateTime)),
          ],
        ),
      ),
    );
  }

  /// Build info row
  Widget _buildInfoRow(ThemeData theme, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build passenger selection
  Widget _buildPassengerSelection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Passenger',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        DropdownButtonFormField<PassengerInfo>(
          value: _selectedPassenger,
          decoration: const InputDecoration(
            labelText: 'Passenger',
            border: OutlineInputBorder(),
          ),
          items: widget.booking.passengers.map((passenger) {
            return DropdownMenuItem(
              value: passenger,
              child: Text('${passenger.firstName} ${passenger.lastName}'),
            );
          }).toList(),
          onChanged: (passenger) {
            setState(() {
              _selectedPassenger = passenger;
              _selectedSeat = null;
            });
          },
        ),
      ],
    );
  }

  /// Build current seat information
  Widget _buildCurrentSeatInfo(ThemeData theme) {
    final currentSeat = _getCurrentSeat(_selectedPassenger!);

    return Card(
      color: theme.colorScheme.surfaceContainerHighest.withAlpha(128),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.event_seat,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Current Seat',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              currentSeat ?? 'No seat assigned',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build available seats
  Widget _buildAvailableSeats(ThemeData theme) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Seats',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (_availableSeats.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'No available seats found. Please try again later.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          )
        else
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              childAspectRatio: 1.2,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: _availableSeats.length,
            itemBuilder: (context, index) {
              final seat = _availableSeats[index];
              return _buildSeatCard(theme, seat);
            },
          ),
      ],
    );
  }

  /// Build seat card
  Widget _buildSeatCard(ThemeData theme, Seat seat) {
    final isSelected = _selectedSeat == seat.id;

    return Card(
      elevation: isSelected ? 4 : 1,
      color: isSelected ? theme.colorScheme.primary : null,
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedSeat = seat.id;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getSeatIcon(seat.seatType),
                color: isSelected ? Colors.white : theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(height: 4),
              Text(
                seat.id,
                style: theme.textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Colors.white : null,
                ),
              ),
              if (seat.price > 0)
                Text(
                  '+\$${seat.price.toStringAsFixed(0)}',
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: isSelected
                        ? Colors.white.withAlpha(204)
                        : theme.colorScheme.onSurfaceVariant,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build action buttons
  Widget _buildActions(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _selectedPassenger != null && _selectedSeat != null
                  ? () => _confirmSeatChange()
                  : null,
              child: const Text('Change Seat'),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  Future<void> _loadAvailableSeats() async {
    setState(() {
      _isLoading = true;
    });

    // Mock available seats - in real app, this would come from API
    await Future.delayed(const Duration(milliseconds: 500));

    final mockSeats = [
      const Seat(
          id: '12A',
          row: 12,
          column: 0,
          seatClass: SeatClass.economy,
          seatType: SeatType.window,
          isAvailable: true,
          isExitRow: false,
          price: 20.0),
      const Seat(
          id: '12B',
          row: 12,
          column: 1,
          seatClass: SeatClass.economy,
          seatType: SeatType.middle,
          isAvailable: true,
          isExitRow: false,
          price: 0.0),
      const Seat(
          id: '12C',
          row: 12,
          column: 2,
          seatClass: SeatClass.economy,
          seatType: SeatType.aisle,
          isAvailable: true,
          isExitRow: false,
          price: 20.0),
      const Seat(
          id: '13A',
          row: 13,
          column: 0,
          seatClass: SeatClass.economy,
          seatType: SeatType.window,
          isAvailable: true,
          isExitRow: false,
          price: 20.0),
      const Seat(
          id: '13F',
          row: 13,
          column: 5,
          seatClass: SeatClass.economy,
          seatType: SeatType.aisle,
          isAvailable: true,
          isExitRow: false,
          price: 20.0),
      const Seat(
          id: '15A',
          row: 15,
          column: 0,
          seatClass: SeatClass.economy,
          seatType: SeatType.window,
          isAvailable: true,
          isExitRow: true,
          price: 30.0),
    ];

    setState(() {
      _availableSeats = mockSeats;
      _isLoading = false;
    });
  }

  String? _getCurrentSeat(PassengerInfo passenger) {
    // In real app, this would come from booking data
    return '11A'; // Mock current seat
  }

  IconData _getSeatIcon(SeatType seatType) {
    switch (seatType) {
      case SeatType.window:
        return Icons.airline_seat_individual_suite;
      case SeatType.aisle:
        return Icons.airline_seat_legroom_extra;
      case SeatType.exit:
        return Icons.exit_to_app;
      default:
        return Icons.airline_seat_recline_normal;
    }
  }

  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  void _confirmSeatChange() {
    if (_selectedPassenger != null && _selectedSeat != null) {
      HapticFeedback.lightImpact();
      widget.onSeatChanged(_selectedPassenger!, _selectedSeat!);
      Navigator.of(context).pop();
    }
  }
}
