import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/models/travel/flight/booking_info.dart';
import 'package:culture_connect/models/travel/flight/passenger_info.dart';

/// Dialog for managing special assistance requests for passengers
class SpecialAssistanceDialog extends StatefulWidget {
  /// The booking information
  final BookingInfo booking;

  /// Callback when special assistance is changed
  final Function(PassengerInfo passenger, bool assistance, String details)
      onAssistanceChanged;

  /// Creates a new special assistance dialog
  const SpecialAssistanceDialog({
    super.key,
    required this.booking,
    required this.onAssistanceChanged,
  });

  @override
  State<SpecialAssistanceDialog> createState() =>
      _SpecialAssistanceDialogState();
}

class _SpecialAssistanceDialogState extends State<SpecialAssistanceDialog> {
  PassengerInfo? _selectedPassenger;
  bool _requiresAssistance = false;
  final Set<String> _selectedAssistanceTypes = <String>{};
  final TextEditingController _detailsController = TextEditingController();

  // Available assistance types
  final List<AssistanceType> _assistanceTypes = [
    const AssistanceType('wheelchair', 'Wheelchair Assistance',
        Icons.accessible, 'Airport wheelchair service'),
    const AssistanceType('mobility', 'Mobility Assistance',
        Icons.directions_walk, 'Help with walking and mobility'),
    const AssistanceType('visual', 'Visual Impairment', Icons.visibility_off,
        'Assistance for visually impaired passengers'),
    const AssistanceType('hearing', 'Hearing Impairment',
        Icons.hearing_disabled, 'Assistance for hearing impaired passengers'),
    const AssistanceType('cognitive', 'Cognitive Assistance', Icons.psychology,
        'Support for cognitive disabilities'),
    const AssistanceType('medical', 'Medical Equipment', Icons.medical_services,
        'Assistance with medical devices'),
    const AssistanceType('boarding', 'Priority Boarding', Icons.flight_takeoff,
        'Early boarding assistance'),
    const AssistanceType('seating', 'Special Seating',
        Icons.airline_seat_recline_extra, 'Accessible seating requirements'),
  ];

  @override
  void dispose() {
    _detailsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 500,
          maxHeight: 700,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(theme),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Flight information
                    _buildFlightInfo(theme),

                    const SizedBox(height: 24),

                    // Passenger selection
                    _buildPassengerSelection(theme),

                    const SizedBox(height: 24),

                    // Current assistance status
                    if (_selectedPassenger != null)
                      _buildCurrentAssistanceInfo(theme),

                    const SizedBox(height: 24),

                    // Assistance options
                    if (_selectedPassenger != null)
                      _buildAssistanceOptions(theme),

                    const SizedBox(height: 24),

                    // Additional details
                    if (_selectedPassenger != null && _requiresAssistance)
                      _buildAdditionalDetails(theme),
                  ],
                ),
              ),
            ),

            // Actions
            _buildActions(context, theme),
          ],
        ),
      ),
    );
  }

  /// Build dialog header
  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withAlpha(204),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.accessible,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Special Assistance',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Request accessibility support',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withAlpha(204),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white.withAlpha(51),
            ),
          ),
        ],
      ),
    );
  }

  /// Build flight information
  Widget _buildFlightInfo(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.flight,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Flight Information',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow(theme, 'Flight', widget.booking.flight.flightNumber),
            _buildInfoRow(theme, 'Route',
                '${widget.booking.flight.departureAirport} → ${widget.booking.flight.arrivalAirport}'),
            _buildInfoRow(theme, 'Date',
                _formatDate(widget.booking.flight.departureDateTime)),
          ],
        ),
      ),
    );
  }

  /// Build info row
  Widget _buildInfoRow(ThemeData theme, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build passenger selection
  Widget _buildPassengerSelection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Passenger',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        DropdownButtonFormField<PassengerInfo>(
          value: _selectedPassenger,
          decoration: const InputDecoration(
            labelText: 'Passenger',
            border: OutlineInputBorder(),
          ),
          items: widget.booking.passengers.map((passenger) {
            return DropdownMenuItem(
              value: passenger,
              child: Text('${passenger.firstName} ${passenger.lastName}'),
            );
          }).toList(),
          onChanged: (passenger) {
            setState(() {
              _selectedPassenger = passenger;
              _requiresAssistance = passenger?.specialAssistance ?? false;
              _detailsController.text =
                  passenger?.specialAssistanceDetails ?? '';
              _selectedAssistanceTypes.clear();
            });
          },
        ),
      ],
    );
  }

  /// Build current assistance information
  Widget _buildCurrentAssistanceInfo(ThemeData theme) {
    final hasAssistance = _selectedPassenger!.specialAssistance;

    return Card(
      color: hasAssistance
          ? theme.colorScheme.primary.withAlpha(26)
          : theme.colorScheme.surfaceContainerHighest.withAlpha(128),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  hasAssistance ? Icons.check_circle : Icons.info_outline,
                  color:
                      hasAssistance ? Colors.green : theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Current Status',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              hasAssistance
                  ? 'Special assistance requested'
                  : 'No special assistance',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: hasAssistance
                    ? Colors.green
                    : theme.colorScheme.onSurfaceVariant,
              ),
            ),
            if (hasAssistance &&
                _selectedPassenger!.specialAssistanceDetails.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                _selectedPassenger!.specialAssistanceDetails,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build assistance options
  Widget _buildAssistanceOptions(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Assistance Required',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Switch(
              value: _requiresAssistance,
              onChanged: (value) {
                setState(() {
                  _requiresAssistance = value;
                  if (!value) {
                    _selectedAssistanceTypes.clear();
                    _detailsController.clear();
                  }
                });
              },
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_requiresAssistance) ...[
          Text(
            'Select Assistance Types',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ..._assistanceTypes
              .map((type) => _buildAssistanceTypeOption(theme, type)),
        ],
      ],
    );
  }

  /// Build individual assistance type option
  Widget _buildAssistanceTypeOption(ThemeData theme, AssistanceType type) {
    final isSelected = _selectedAssistanceTypes.contains(type.id);

    return Card(
      elevation: isSelected ? 2 : 1,
      color: isSelected ? theme.colorScheme.primary.withAlpha(26) : null,
      margin: const EdgeInsets.only(bottom: 8),
      child: CheckboxListTile(
        value: isSelected,
        onChanged: (value) {
          setState(() {
            if (value == true) {
              _selectedAssistanceTypes.add(type.id);
            } else {
              _selectedAssistanceTypes.remove(type.id);
            }
          });
        },
        title: Text(
          type.name,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          type.description,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        secondary: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colorScheme.primary.withAlpha(51)
                : theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            type.icon,
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
        ),
        controlAffinity: ListTileControlAffinity.trailing,
      ),
    );
  }

  /// Build additional details section
  Widget _buildAdditionalDetails(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Additional Details',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _detailsController,
          maxLines: 3,
          decoration: const InputDecoration(
            labelText: 'Specific requirements or details',
            hintText:
                'Please provide any additional information about your assistance needs...',
            border: OutlineInputBorder(),
          ),
        ),
      ],
    );
  }

  /// Build action buttons
  Widget _buildActions(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _selectedPassenger != null
                  ? () => _confirmAssistanceChange()
                  : null,
              child: const Text('Update Assistance'),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  void _confirmAssistanceChange() {
    if (_selectedPassenger != null) {
      HapticFeedback.lightImpact();
      final details = _requiresAssistance ? _detailsController.text : '';
      widget.onAssistanceChanged(
          _selectedPassenger!, _requiresAssistance, details);
      Navigator.of(context).pop();
    }
  }
}

/// Data class for assistance types
class AssistanceType {
  final String id;
  final String name;
  final IconData icon;
  final String description;

  const AssistanceType(this.id, this.name, this.icon, this.description);
}
