import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/models/travel/flight/flight_info.dart';

/// Dialog for contacting flight support with multiple contact methods
class FlightSupportContactDialog extends StatelessWidget {
  /// Flight number for context
  final String flightNumber;

  /// Departure date for context
  final DateTime departureDate;

  /// Flight information (optional)
  final FlightInfo? flightInfo;

  /// Creates a new flight support contact dialog
  const FlightSupportContactDialog({
    super.key,
    required this.flightNumber,
    required this.departureDate,
    this.flightInfo,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 600,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(theme),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Flight context
                    _buildFlightContext(theme),

                    const SizedBox(height: 24),

                    // Contact methods
                    _buildContactMethods(context, theme),

                    const SizedBox(height: 24),

                    // Support categories
                    _buildSupportCategories(context, theme),
                  ],
                ),
              ),
            ),

            // Actions
            _buildActions(context, theme),
          ],
        ),
      ),
    );
  }

  /// Build dialog header
  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withAlpha(204),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.support_agent,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Flight Support',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Get help with your flight',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withAlpha(204),
                  ),
                ),
              ],
            ),
          ),
          Builder(
            builder: (context) => IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
              style: IconButton.styleFrom(
                backgroundColor: Colors.white.withAlpha(51),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build flight context information
  Widget _buildFlightContext(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.flight,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Flight Information',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildContextRow(theme, 'Flight Number', flightNumber),
            _buildContextRow(theme, 'Date', _formatDate(departureDate)),
            if (flightInfo != null) ...[
              _buildContextRow(
                  theme, 'Status', _getStatusText(flightInfo!.status)),
              _buildContextRow(theme, 'Route',
                  '${flightInfo!.departureAirportCode} → ${flightInfo!.arrivalAirportCode}'),
            ],
          ],
        ),
      ),
    );
  }

  /// Build context row
  Widget _buildContextRow(ThemeData theme, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build contact methods section
  Widget _buildContactMethods(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Contact Methods',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildContactMethodCard(
                context,
                theme,
                'Phone',
                'Call support directly',
                Icons.phone,
                Colors.green,
                () => _contactByPhone(context),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildContactMethodCard(
                context,
                theme,
                'Email',
                'Send detailed inquiry',
                Icons.email,
                Colors.blue,
                () => _contactByEmail(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildContactMethodCard(
                context,
                theme,
                'Live Chat',
                'Instant messaging',
                Icons.chat,
                Colors.orange,
                () => _contactByChat(context),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildContactMethodCard(
                context,
                theme,
                'FAQ',
                'Common questions',
                Icons.help_outline,
                Colors.purple,
                () => _viewFAQ(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build contact method card
  Widget _buildContactMethodCard(
    BuildContext context,
    ThemeData theme,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build support categories section
  Widget _buildSupportCategories(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Support Categories',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildCategoryItem(context, theme, 'Flight Changes',
            'Reschedule or modify booking', Icons.schedule, false),
        _buildCategoryItem(context, theme, 'Cancellations',
            'Cancel flight and refunds', Icons.cancel, false),
        _buildCategoryItem(context, theme, 'Baggage Issues',
            'Lost or delayed baggage', Icons.luggage, false),
        _buildCategoryItem(context, theme, 'Special Assistance',
            'Accessibility and special needs', Icons.accessible, false),
        _buildCategoryItem(context, theme, 'Emergency Support',
            'Urgent travel assistance', Icons.emergency, true),
      ],
    );
  }

  /// Build category item
  Widget _buildCategoryItem(
    BuildContext context,
    ThemeData theme,
    String title,
    String description,
    IconData icon,
    bool isEmergency,
  ) {
    return Card(
      elevation: isEmergency ? 4 : 1,
      color: isEmergency ? Colors.red.withAlpha(26) : null,
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isEmergency
                ? Colors.red.withAlpha(51)
                : theme.colorScheme.primary.withAlpha(26),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: isEmergency ? Colors.red : theme.colorScheme.primary,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: isEmergency ? Colors.red.shade700 : null,
          ),
        ),
        subtitle: Text(
          description,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        trailing: Icon(
          Icons.chevron_right,
          color: theme.colorScheme.onSurfaceVariant,
        ),
        onTap: () => _handleCategoryTap(context, title, isEmergency),
      ),
    );
  }

  /// Build action buttons
  Widget _buildActions(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _contactByPhone(context),
              icon: const Icon(Icons.phone),
              label: const Text('Call Now'),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  String _getStatusText(FlightStatus status) {
    switch (status) {
      case FlightStatus.scheduled:
        return 'Scheduled';
      case FlightStatus.boarding:
        return 'Boarding';
      case FlightStatus.inAir:
        return 'In Air';
      case FlightStatus.landed:
        return 'Landed';
      case FlightStatus.delayed:
        return 'Delayed';
      case FlightStatus.cancelled:
        return 'Cancelled';
      case FlightStatus.diverted:
        return 'Diverted';
    }
  }

  // Contact method handlers
  void _contactByPhone(BuildContext context) {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Calling flight support: ******-FLY-HELP'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _contactByEmail(BuildContext context) {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening email to: <EMAIL>'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _contactByChat(BuildContext context) {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Starting live chat with support agent...'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _viewFAQ(BuildContext context) {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening Flight FAQ section...'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  void _handleCategoryTap(
      BuildContext context, String category, bool isEmergency) {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();

    final message = isEmergency
        ? 'Connecting to emergency support for: $category'
        : 'Getting help with: $category';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isEmergency ? Colors.red : Colors.blue,
      ),
    );
  }
}
