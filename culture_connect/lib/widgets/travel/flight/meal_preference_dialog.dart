import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/models/travel/flight/booking_info.dart';
import 'package:culture_connect/models/travel/flight/passenger_info.dart';

/// Dialog for changing meal preferences for passengers
class MealPreferenceDialog extends StatefulWidget {
  /// The booking information
  final BookingInfo booking;

  /// Callback when meal preference is changed
  final Function(PassengerInfo passenger, MealPreference newMeal) onMealChanged;

  /// Creates a new meal preference dialog
  const MealPreferenceDialog({
    super.key,
    required this.booking,
    required this.onMealChanged,
  });

  @override
  State<MealPreferenceDialog> createState() => _MealPreferenceDialogState();
}

class _MealPreferenceDialogState extends State<MealPreferenceDialog> {
  PassengerInfo? _selectedPassenger;
  MealPreference? _selectedMeal;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 500,
          maxHeight: 700,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(theme),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Flight information
                    _buildFlightInfo(theme),

                    const SizedBox(height: 24),

                    // Passenger selection
                    _buildPassengerSelection(theme),

                    const SizedBox(height: 24),

                    // Current meal preference
                    if (_selectedPassenger != null)
                      _buildCurrentMealInfo(theme),

                    const SizedBox(height: 24),

                    // Meal options
                    if (_selectedPassenger != null) _buildMealOptions(theme),
                  ],
                ),
              ),
            ),

            // Actions
            _buildActions(context, theme),
          ],
        ),
      ),
    );
  }

  /// Build dialog header
  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withAlpha(204),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.restaurant,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Meal Preference',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Select dietary preferences',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withAlpha(204),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white.withAlpha(51),
            ),
          ),
        ],
      ),
    );
  }

  /// Build flight information
  Widget _buildFlightInfo(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.flight,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Flight Information',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow(theme, 'Flight', widget.booking.flight.flightNumber),
            _buildInfoRow(theme, 'Route',
                '${widget.booking.flight.departureAirport} → ${widget.booking.flight.arrivalAirport}'),
            _buildInfoRow(theme, 'Date',
                _formatDate(widget.booking.flight.departureDateTime)),
          ],
        ),
      ),
    );
  }

  /// Build info row
  Widget _buildInfoRow(ThemeData theme, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build passenger selection
  Widget _buildPassengerSelection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Passenger',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        DropdownButtonFormField<PassengerInfo>(
          value: _selectedPassenger,
          decoration: const InputDecoration(
            labelText: 'Passenger',
            border: OutlineInputBorder(),
          ),
          items: widget.booking.passengers.map((passenger) {
            return DropdownMenuItem(
              value: passenger,
              child: Text('${passenger.firstName} ${passenger.lastName}'),
            );
          }).toList(),
          onChanged: (passenger) {
            setState(() {
              _selectedPassenger = passenger;
              _selectedMeal = passenger?.mealPreference;
            });
          },
        ),
      ],
    );
  }

  /// Build current meal preference information
  Widget _buildCurrentMealInfo(ThemeData theme) {
    final currentMeal = _selectedPassenger!.mealPreference;

    return Card(
      color: theme.colorScheme.surfaceContainerHighest.withAlpha(128),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.restaurant_menu,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Current Meal Preference',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              currentMeal.displayName,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build meal options
  Widget _buildMealOptions(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select New Meal Preference',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...MealPreference.values.map((meal) => _buildMealOption(theme, meal)),
      ],
    );
  }

  /// Build individual meal option
  Widget _buildMealOption(ThemeData theme, MealPreference meal) {
    final isSelected = _selectedMeal == meal;

    return Card(
      elevation: isSelected ? 4 : 1,
      color: isSelected ? theme.colorScheme.primary.withAlpha(26) : null,
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedMeal = meal;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primary.withAlpha(51)
                      : theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getMealIcon(meal),
                  color: isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurfaceVariant,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      meal.displayName,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isSelected ? theme.colorScheme.primary : null,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getMealDescription(meal),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build action buttons
  Widget _buildActions(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _selectedPassenger != null && _selectedMeal != null
                  ? () => _confirmMealChange()
                  : null,
              child: const Text('Update Preference'),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  IconData _getMealIcon(MealPreference meal) {
    switch (meal) {
      case MealPreference.vegetarian:
        return Icons.eco;
      case MealPreference.vegan:
        return Icons.grass;
      case MealPreference.kosher:
        return Icons.star;
      case MealPreference.halal:
        return Icons.mosque;
      case MealPreference.glutenFree:
        return Icons.no_food;
      case MealPreference.diabetic:
        return Icons.health_and_safety;
      case MealPreference.lowSodium:
        return Icons.water_drop;
      case MealPreference.lowCalorie:
        return Icons.fitness_center;
      case MealPreference.seafood:
        return Icons.set_meal;
      case MealPreference.child:
        return Icons.child_care;
      case MealPreference.infant:
        return Icons.baby_changing_station;
      case MealPreference.hindu:
        return Icons.temple_hindu;
      case MealPreference.lactoseFree:
        return Icons.no_drinks;
      default:
        return Icons.restaurant;
    }
  }

  String _getMealDescription(MealPreference meal) {
    switch (meal) {
      case MealPreference.none:
        return 'No specific meal preference';
      case MealPreference.regular:
        return 'Standard airline meal';
      case MealPreference.vegetarian:
        return 'No meat, fish, or poultry';
      case MealPreference.vegan:
        return 'No animal products';
      case MealPreference.kosher:
        return 'Prepared according to Jewish dietary laws';
      case MealPreference.halal:
        return 'Prepared according to Islamic dietary laws';
      case MealPreference.glutenFree:
        return 'No gluten-containing ingredients';
      case MealPreference.diabetic:
        return 'Low sugar, suitable for diabetics';
      case MealPreference.lowSodium:
        return 'Reduced sodium content';
      case MealPreference.lowCalorie:
        return 'Reduced calorie content';
      case MealPreference.seafood:
        return 'Fish and seafood based meal';
      case MealPreference.child:
        return 'Kid-friendly meal options';
      case MealPreference.infant:
        return 'Baby food and formula';
      case MealPreference.hindu:
        return 'Prepared according to Hindu dietary customs';
      case MealPreference.lactoseFree:
        return 'No dairy products';
    }
  }

  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  void _confirmMealChange() {
    if (_selectedPassenger != null && _selectedMeal != null) {
      HapticFeedback.lightImpact();
      widget.onMealChanged(_selectedPassenger!, _selectedMeal!);
      Navigator.of(context).pop();
    }
  }
}
