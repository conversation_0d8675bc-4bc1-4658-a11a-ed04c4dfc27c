import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A curved content container that creates a wave-like separation
/// between gradient background and content area
class CurvedContentContainer extends StatelessWidget {
  final Widget child;
  final double curveHeight;
  final Color backgroundColor;
  final EdgeInsets padding;
  final bool showShadow;
  
  const CurvedContentContainer({
    super.key,
    required this.child,
    this.curveHeight = 40.0,
    this.backgroundColor = AppTheme.authContentBackground,
    this.padding = const EdgeInsets.all(24.0),
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: _CurvedTopClipper(curveHeight: curveHeight),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          boxShadow: showShadow ? [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ] : null,
        ),
        child: Padding(
          padding: EdgeInsets.only(
            left: padding.left,
            right: padding.right,
            bottom: padding.bottom,
            top: padding.top + curveHeight,
          ),
          child: child,
        ),
      ),
    );
  }
}

/// Custom clipper for creating curved top edge
class _CurvedTopClipper extends CustomClipper<Path> {
  final double curveHeight;
  
  const _CurvedTopClipper({required this.curveHeight});

  @override
  Path getClip(Size size) {
    final path = Path();
    
    // Start from top-left
    path.moveTo(0, curveHeight);
    
    // Create smooth curve across the top
    path.quadraticBezierTo(
      size.width * 0.25, 0,           // Control point 1
      size.width * 0.5, curveHeight * 0.3,  // Mid point
    );
    
    path.quadraticBezierTo(
      size.width * 0.75, curveHeight * 0.6,  // Control point 2
      size.width, 0,                         // End point
    );
    
    // Complete the rectangle
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return oldClipper is _CurvedTopClipper && 
           oldClipper.curveHeight != curveHeight;
  }
}

/// Alternative curved container with simpler wave design
class SimpleCurvedContainer extends StatelessWidget {
  final Widget child;
  final double curveHeight;
  final Color backgroundColor;
  final EdgeInsets padding;
  
  const SimpleCurvedContainer({
    super.key,
    required this.child,
    this.curveHeight = 30.0,
    this.backgroundColor = AppTheme.authContentBackground,
    this.padding = const EdgeInsets.all(24.0),
  });

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: _SimpleCurveClipper(curveHeight: curveHeight),
      child: Container(
        color: backgroundColor,
        child: Padding(
          padding: EdgeInsets.only(
            left: padding.left,
            right: padding.right,
            bottom: padding.bottom,
            top: padding.top + curveHeight,
          ),
          child: child,
        ),
      ),
    );
  }
}

/// Simple curve clipper for a gentler wave effect
class _SimpleCurveClipper extends CustomClipper<Path> {
  final double curveHeight;
  
  const _SimpleCurveClipper({required this.curveHeight});

  @override
  Path getClip(Size size) {
    final path = Path();
    
    // Start from top-left
    path.moveTo(0, curveHeight);
    
    // Create single smooth curve
    path.quadraticBezierTo(
      size.width * 0.5, 0,      // Control point at center-top
      size.width, curveHeight,  // End point at top-right
    );
    
    // Complete the rectangle
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return oldClipper is _SimpleCurveClipper && 
           oldClipper.curveHeight != curveHeight;
  }
}
