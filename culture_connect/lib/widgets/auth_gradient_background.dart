import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A beautiful gradient background widget for authentication screens
/// Features coral/salmon gradient with subtle geometric patterns
class AuthGradientBackground extends StatelessWidget {
  final Widget child;
  final bool showPatterns;
  
  const AuthGradientBackground({
    super.key,
    required this.child,
    this.showPatterns = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppTheme.coralGradient,
      ),
      child: Stack(
        children: [
          // Geometric patterns overlay
          if (showPatterns) _buildPatternOverlay(),
          
          // Main content
          child,
        ],
      ),
    );
  }

  Widget _buildPatternOverlay() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _GeometricPatternPainter(),
      ),
    );
  }
}

/// Custom painter for subtle geometric patterns
class _GeometricPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withAlpha(25)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw subtle geometric lines
    _drawDiagonalLines(canvas, size, paint);
    _drawCircles(canvas, size, paint);
  }

  void _drawDiagonalLines(Canvas canvas, Size size, Paint paint) {
    const spacing = 80.0;
    
    // Diagonal lines from top-left to bottom-right
    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
    
    // Diagonal lines from top-right to bottom-left
    for (double i = 0; i < size.width + size.height; i += spacing * 1.5) {
      canvas.drawLine(
        Offset(size.width - i, 0),
        Offset(size.width - i - size.height, size.height),
        paint,
      );
    }
  }

  void _drawCircles(Canvas canvas, Size size, Paint paint) {
    final circlePaint = Paint()
      ..color = Colors.white.withAlpha(15)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw subtle circles
    const spacing = 120.0;
    const radius = 30.0;
    
    for (double x = radius; x < size.width; x += spacing) {
      for (double y = radius; y < size.height; y += spacing * 1.2) {
        canvas.drawCircle(
          Offset(x, y),
          radius,
          circlePaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
