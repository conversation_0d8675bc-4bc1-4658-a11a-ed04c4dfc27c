import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/translation/enhanced_pronunciation_guidance.dart';
import 'package:culture_connect/providers/voice_translation/enhanced_pronunciation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/custom_button.dart';

/// Widget for pronunciation practice with guidance and feedback
class PronunciationPracticeWidget extends ConsumerStatefulWidget {
  final EnhancedPronunciationGuidance guidance;
  final VoidCallback? onPracticeComplete;

  const PronunciationPracticeWidget({
    super.key,
    required this.guidance,
    this.onPracticeComplete,
  });

  @override
  ConsumerState<PronunciationPracticeWidget> createState() =>
      _PronunciationPracticeWidgetState();
}

class _PronunciationPracticeWidgetState
    extends ConsumerState<PronunciationPracticeWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _waveAnimation;

  bool _isRecording = false;
  bool _isPlaying = false;
  int _currentAttempt = 0;
  double _currentAccuracy = 0.0;
  DateTime? _practiceStartTime;
  List<String> _improvedSounds = [];
  List<String> _needsWorkSounds = [];

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _waveAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.linear),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 24),
          _buildTextDisplay(),
          const SizedBox(height: 24),
          _buildPhoneticGuide(),
          const SizedBox(height: 24),
          _buildAudioControls(),
          const SizedBox(height: 24),
          _buildRecordingSection(),
          if (_currentAccuracy > 0) ...[
            const SizedBox(height: 24),
            _buildFeedbackSection(),
          ],
          const SizedBox(height: 24),
          _buildProgressSection(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withAlpha(204),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.record_voice_over,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Pronunciation Practice',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getDifficultyColor().withAlpha(26),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      widget.guidance.difficulty.name.toUpperCase(),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: _getDifficultyColor(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.guidance.languageCode.toUpperCase(),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            'Attempt ${_currentAttempt + 1}',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextDisplay() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primaryContainer.withAlpha(77),
            Theme.of(context).colorScheme.primaryContainer.withAlpha(26),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withAlpha(77),
        ),
      ),
      child: Column(
        children: [
          Text(
            widget.guidance.text,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              height: 1.3,
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.guidance.syllableBreakdown.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildSyllableBreakdown(),
          ],
        ],
      ),
    );
  }

  Widget _buildSyllableBreakdown() {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      alignment: WrapAlignment.center,
      children: widget.guidance.syllableBreakdown.map((syllable) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: syllable.isPrimaryStress
                ? Theme.of(context).colorScheme.primary.withAlpha(51)
                : Theme.of(context).colorScheme.outline.withAlpha(26),
            borderRadius: BorderRadius.circular(8),
            border: syllable.isPrimaryStress
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary.withAlpha(128))
                : null,
          ),
          child: Text(
            syllable.syllable,
            style: TextStyle(
              fontSize: 14,
              fontWeight: syllable.isPrimaryStress
                  ? FontWeight.bold
                  : FontWeight.normal,
              color: syllable.isPrimaryStress
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPhoneticGuide() {
    if (widget.guidance.phoneticTranscription == null &&
        widget.guidance.simplifiedPhonetics == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.hearing,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              const Text(
                'Pronunciation Guide',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (widget.guidance.phoneticTranscription != null) ...[
            Text(
              'IPA: ${widget.guidance.phoneticTranscription}',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'monospace',
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
          ],
          if (widget.guidance.simplifiedPhonetics != null)
            Text(
              'Simplified: ${widget.guidance.simplifiedPhonetics}',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAudioControls() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: 'Play Native Speed',
            onPressed: widget.guidance.nativeAudioPath != null
                ? _playNativeAudio
                : null,
            icon: _isPlaying ? Icons.stop : Icons.play_arrow,
            isLoading: _isPlaying,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: CustomButton(
            text: 'Play Slow Speed',
            onPressed:
                widget.guidance.slowAudioPath != null ? _playSlowAudio : null,
            icon: Icons.slow_motion_video,
          ),
        ),
      ],
    );
  }

  Widget _buildRecordingSection() {
    return Column(
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _isRecording ? _pulseAnimation.value : 1.0,
              child: GestureDetector(
                onTapDown: (_) => _startRecording(),
                onTapUp: (_) => _stopRecording(),
                onTapCancel: () => _stopRecording(),
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: _isRecording
                        ? Colors.red
                        : Theme.of(context).colorScheme.primary,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: (_isRecording
                                ? Colors.red
                                : Theme.of(context).colorScheme.primary)
                            .withAlpha(77),
                        blurRadius: 20,
                        spreadRadius: _isRecording ? 4 : 0,
                      ),
                    ],
                  ),
                  child: Icon(
                    _isRecording ? Icons.stop : Icons.mic,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ),
            );
          },
        ),
        const SizedBox(height: 16),
        Text(
          _isRecording
              ? 'Recording... Release to stop'
              : 'Hold to record your pronunciation',
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
        if (_isRecording) ...[
          const SizedBox(height: 16),
          _buildWaveform(),
        ],
      ],
    );
  }

  Widget _buildWaveform() {
    return AnimatedBuilder(
      animation: _waveAnimation,
      builder: (context, child) {
        return Container(
          height: 40,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(20, (index) {
              final height = 4 +
                  (36 *
                      (0.5 +
                          0.5 *
                              (1 +
                                  (index * 0.1 +
                                      _waveAnimation.value * 2 * 3.14159)) /
                              2));
              return Container(
                width: 3,
                height: height,
                margin: const EdgeInsets.symmetric(horizontal: 1),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(2),
                ),
              );
            }),
          ),
        );
      },
    );
  }

  Widget _buildFeedbackSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getAccuracyColor().withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getAccuracyColor().withAlpha(77),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getAccuracyIcon(),
                color: _getAccuracyColor(),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Pronunciation Feedback',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: _getAccuracyColor(),
                ),
              ),
              const Spacer(),
              Text(
                '${(_currentAccuracy * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _getAccuracyColor(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: _currentAccuracy,
            backgroundColor:
                Theme.of(context).colorScheme.outline.withAlpha(77),
            valueColor: AlwaysStoppedAnimation<Color>(_getAccuracyColor()),
          ),
          const SizedBox(height: 12),
          Text(
            _getAccuracyFeedback(),
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    final practiceHistory = widget.guidance.practiceHistory;
    if (practiceHistory.isEmpty) return const SizedBox.shrink();

    final averageAccuracy = practiceHistory.fold<double>(
          0.0,
          (sum, progress) => sum + progress.accuracyScore,
        ) /
        practiceHistory.length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Practice Progress',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Sessions',
                  practiceHistory.length.toString(),
                  Icons.history,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Average',
                  '${(averageAccuracy * 100).toInt()}%',
                  Icons.trending_up,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Best',
                  '${(practiceHistory.map((p) => p.accuracyScore).reduce((a, b) => a > b ? a : b) * 100).toInt()}%',
                  Icons.star,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Color _getDifficultyColor() {
    switch (widget.guidance.difficulty) {
      case PronunciationDifficulty.beginner:
        return Colors.green;
      case PronunciationDifficulty.elementary:
        return Colors.lightGreen;
      case PronunciationDifficulty.intermediate:
        return Colors.orange;
      case PronunciationDifficulty.advanced:
        return Colors.red;
      case PronunciationDifficulty.expert:
        return Colors.deepPurple;
    }
  }

  Color _getAccuracyColor() {
    if (_currentAccuracy >= 0.8) return Colors.green;
    if (_currentAccuracy >= 0.6) return Colors.orange;
    return Colors.red;
  }

  IconData _getAccuracyIcon() {
    if (_currentAccuracy >= 0.8) return Icons.check_circle;
    if (_currentAccuracy >= 0.6) return Icons.warning;
    return Icons.error;
  }

  String _getAccuracyFeedback() {
    if (_currentAccuracy >= 0.9) return 'Excellent pronunciation! Keep it up!';
    if (_currentAccuracy >= 0.8)
      return 'Great job! Minor improvements possible.';
    if (_currentAccuracy >= 0.7)
      return 'Good effort! Focus on stressed syllables.';
    if (_currentAccuracy >= 0.6)
      return 'Getting better! Practice the difficult sounds.';
    return 'Keep practicing! Listen to the audio examples.';
  }

  void _playNativeAudio() async {
    setState(() {
      _isPlaying = true;
    });

    // Simulate audio playback
    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      setState(() {
        _isPlaying = false;
      });
    }
  }

  void _playSlowAudio() async {
    setState(() {
      _isPlaying = true;
    });

    // Simulate slow audio playback
    await Future.delayed(const Duration(seconds: 3));

    if (mounted) {
      setState(() {
        _isPlaying = false;
      });
    }
  }

  void _startRecording() {
    setState(() {
      _isRecording = true;
      _practiceStartTime = DateTime.now();
    });
    _pulseController.repeat(reverse: true);
    _waveController.repeat();
  }

  void _stopRecording() async {
    if (!_isRecording) return;

    setState(() {
      _isRecording = false;
    });
    _pulseController.stop();
    _waveController.stop();

    // Simulate pronunciation analysis
    await Future.delayed(const Duration(seconds: 1));

    // Mock accuracy score
    final accuracy =
        0.6 + (0.4 * (DateTime.now().millisecondsSinceEpoch % 100) / 100);

    setState(() {
      _currentAccuracy = accuracy;
      _currentAttempt++;
    });

    // Record practice session
    if (_practiceStartTime != null) {
      final practiceTime = DateTime.now().difference(_practiceStartTime!);

      try {
        await ref
            .read(enhancedPronunciationProvider.notifier)
            .recordPracticeSession(
              guidanceId: widget.guidance.id,
              accuracyScore: accuracy,
              attemptCount: _currentAttempt,
              practiceTime: practiceTime,
              improvedSounds: _improvedSounds,
              needsWorkSounds: _needsWorkSounds,
            );

        widget.onPracticeComplete?.call();
      } catch (e) {
        // Handle error
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error recording practice: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
