/// A widget for selecting a language with enhanced features
library language_selector;

// Package imports
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports - Models
import 'package:culture_connect/models/translation/language_model.dart';

// Project imports - Providers
import 'package:culture_connect/providers/voice_translation_provider.dart';

// Project imports - Theme
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for selecting a language
class LanguageSelector extends StatelessWidget {
  /// The currently selected language
  final LanguageModel selectedLanguage;

  /// Callback when a language is selected
  final Function(LanguageModel) onSelected;

  /// The label text for the selector
  final String labelText;

  /// Creates a new language selector
  const LanguageSelector({
    super.key,
    required this.selectedLanguage,
    required this.onSelected,
    required this.labelText,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _showLanguageSelectionDialog(context),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Text(
              labelText,
              style: const TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  selectedLanguage.flag,
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    selectedLanguage.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Icon(
                  Icons.arrow_drop_down,
                  size: 16,
                  color: AppTheme.textSecondaryColor,
                ),
              ],
            ),
            if (selectedLanguage.isOfflineAvailable) ...[
              const SizedBox(height: 4),
              const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.offline_bolt,
                    size: 12,
                    color: Colors.green,
                  ),
                  SizedBox(width: 4),
                  Text(
                    'Offline',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Show the language selection dialog
  void _showLanguageSelectionDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      builder: (context) => _LanguageSelectionBottomSheet(
        currentLanguage: selectedLanguage,
        onLanguageSelected: (language) {
          onSelected(language);
          Navigator.pop(context);
        },
        title: 'Select $labelText Language',
      ),
    );
  }
}

/// A bottom sheet for selecting a language
class _LanguageSelectionBottomSheet extends StatelessWidget {
  /// The currently selected language
  final LanguageModel currentLanguage;

  /// Callback when a language is selected
  final Function(LanguageModel) onLanguageSelected;

  /// The title of the bottom sheet
  final String title;

  /// Creates a new language selection bottom sheet
  const _LanguageSelectionBottomSheet({
    required this.currentLanguage,
    required this.onLanguageSelected,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),

          const Divider(height: 16),

          // Language list
          Expanded(
            child: ListView.builder(
              itemCount: supportedLanguages.length,
              itemBuilder: (context, index) {
                final language = supportedLanguages[index];
                final isSelected = language.code == currentLanguage.code;

                return ListTile(
                  leading: Text(
                    language.flag,
                    style: const TextStyle(
                      fontSize: 24,
                    ),
                  ),
                  title: Text(
                    language.name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  subtitle: language.isOfflineAvailable
                      ? const Row(
                          children: [
                            Icon(
                              Icons.offline_bolt,
                              size: 12,
                              color: Colors.green,
                            ),
                            SizedBox(width: 4),
                            Text(
                              'Available offline',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        )
                      : null,
                  trailing: isSelected
                      ? const Icon(
                          Icons.check_circle,
                          color: AppTheme.primaryColor,
                          size: 24,
                        )
                      : null,
                  onTap: () => onLanguageSelected(language),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// A Riverpod-compatible wrapper for the LanguageSelector
/// Maintains backward compatibility with existing code
class RiverpodLanguageSelector extends ConsumerWidget {
  /// Creates a new Riverpod language selector
  const RiverpodLanguageSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sourceLanguage = ref.watch(sourceLanguageProvider);
    final targetLanguage = ref.watch(targetLanguageProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Row(
        children: [
          // Source language
          Expanded(
            child: LanguageSelector(
              selectedLanguage: sourceLanguage,
              onSelected: (language) {
                ref.read(sourceLanguageProvider.notifier).state = language;
              },
              labelText: 'Source',
            ),
          ),

          // Swap button
          IconButton(
            icon: const Icon(
              Icons.swap_horiz,
              color: AppTheme.primaryColor,
              size: 24,
            ),
            onPressed: () => _swapLanguages(ref),
            tooltip: 'Swap languages',
          ),

          // Target language
          Expanded(
            child: LanguageSelector(
              selectedLanguage: targetLanguage,
              onSelected: (language) {
                ref.read(targetLanguageProvider.notifier).state = language;
              },
              labelText: 'Target',
            ),
          ),
        ],
      ),
    );
  }

  void _swapLanguages(WidgetRef ref) {
    final sourceLanguage = ref.read(sourceLanguageProvider);
    final targetLanguage = ref.read(targetLanguageProvider);

    ref.read(sourceLanguageProvider.notifier).state = targetLanguage;
    ref.read(targetLanguageProvider.notifier).state = sourceLanguage;
  }
}
