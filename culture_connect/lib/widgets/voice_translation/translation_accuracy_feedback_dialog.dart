import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/translation/translation_accuracy_feedback.dart';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:culture_connect/providers/voice_translation/translation_accuracy_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/custom_button.dart';
import 'package:culture_connect/widgets/common/custom_text_field.dart';

/// Dialog for collecting translation accuracy feedback
class TranslationAccuracyFeedbackDialog extends ConsumerStatefulWidget {
  final VoiceTranslationModel translation;
  final VoidCallback? onFeedbackSubmitted;

  const TranslationAccuracyFeedbackDialog({
    super.key,
    required this.translation,
    this.onFeedbackSubmitted,
  });

  @override
  ConsumerState<TranslationAccuracyFeedbackDialog> createState() =>
      _TranslationAccuracyFeedbackDialogState();
}

class _TranslationAccuracyFeedbackDialogState
    extends ConsumerState<TranslationAccuracyFeedbackDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  TranslationAccuracyRating? _selectedRating;
  TranslationFeedbackType _selectedFeedbackType =
      TranslationFeedbackType.accuracy;
  double _userConfidence = 0.8;
  LanguageProficiencyLevel? _userProficiency;

  final TextEditingController _commentsController = TextEditingController();
  final TextEditingController _correctionController = TextEditingController();

  bool _isSubmitting = false;
  int _currentStep = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
        parent: _animationController, curve: Curves.easeOutBack));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _commentsController.dispose();
    _correctionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                constraints:
                    const BoxConstraints(maxWidth: 500, maxHeight: 700),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(context),
                    Flexible(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(24),
                        child: _buildContent(context),
                      ),
                    ),
                    _buildActions(context),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withAlpha(204),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.feedback_outlined,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Translation Feedback',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Help us improve translation quality',
                  style: TextStyle(
                    color: Colors.white.withAlpha(204),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    switch (_currentStep) {
      case 0:
        return _buildTranslationReview();
      case 1:
        return _buildRatingStep();
      case 2:
        return _buildDetailsStep();
      default:
        return _buildTranslationReview();
    }
  }

  Widget _buildTranslationReview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Translation Review',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Original text
        _buildTextSection(
          'Original (${widget.translation.getSourceLanguageName()})',
          widget.translation.originalText ?? '',
          Theme.of(context).colorScheme.primary.withAlpha(26),
        ),

        const SizedBox(height: 16),

        // Translated text
        _buildTextSection(
          'Translation (${widget.translation.getTargetLanguageName()})',
          widget.translation.translatedText ?? '',
          Theme.of(context).colorScheme.secondary.withAlpha(26),
        ),

        const SizedBox(height: 24),

        // Confidence indicator
        if (widget.translation.dialectConfidence != null)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.psychology_outlined,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'AI Confidence',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: widget.translation.dialectConfidence,
                        backgroundColor:
                            Theme.of(context).colorScheme.outline.withAlpha(77),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '${((widget.translation.dialectConfidence ?? 0) * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildRatingStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Rate Translation Quality',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'How accurate is this translation?',
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 24),

        // Rating buttons
        ...TranslationAccuracyRating.values
            .map((rating) => _buildRatingOption(rating))
            .toList(),

        const SizedBox(height: 24),

        // Feedback type
        const Text(
          'Feedback Type',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: TranslationFeedbackType.values
              .map(
                (type) => FilterChip(
                  label: Text(_getFeedbackTypeLabel(type)),
                  selected: _selectedFeedbackType == type,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedFeedbackType = type;
                      });
                    }
                  },
                ),
              )
              .toList(),
        ),

        const SizedBox(height: 24),

        // User confidence
        const Text(
          'Your Confidence in This Feedback',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            const Text('Low'),
            Expanded(
              child: Slider(
                value: _userConfidence,
                onChanged: (value) {
                  setState(() {
                    _userConfidence = value;
                  });
                },
                divisions: 10,
                label: '${(_userConfidence * 100).toInt()}%',
              ),
            ),
            const Text('High'),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailsStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Additional Details',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Language proficiency
        const Text(
          'Your Language Proficiency',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        DropdownButtonFormField<LanguageProficiencyLevel>(
          value: _userProficiency,
          decoration: const InputDecoration(
            hintText: 'Select your proficiency level',
            border: OutlineInputBorder(),
          ),
          items: LanguageProficiencyLevel.values
              .map(
                (level) => DropdownMenuItem(
                  value: level,
                  child: Text(_getProficiencyLabel(level)),
                ),
              )
              .toList(),
          onChanged: (value) {
            setState(() {
              _userProficiency = value;
            });
          },
        ),

        const SizedBox(height: 16),

        // Suggested correction
        CustomTextField(
          controller: _correctionController,
          label: 'Suggested Correction (Optional)',
          hint: 'Provide a better translation if you have one',
          maxLines: 3,
        ),

        const SizedBox(height: 16),

        // Comments
        CustomTextField(
          controller: _commentsController,
          label: 'Additional Comments (Optional)',
          hint: 'Any other feedback or observations',
          maxLines: 4,
        ),
      ],
    );
  }

  Widget _buildTextSection(String title, String text, Color backgroundColor) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withAlpha(77),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            text,
            style: const TextStyle(
              fontSize: 16,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingOption(TranslationAccuracyRating rating) {
    final isSelected = _selectedRating == rating;
    final color = _getRatingColor(rating);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            setState(() {
              _selectedRating = rating;
            });
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected ? color.withAlpha(26) : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? color
                    : Theme.of(context).colorScheme.outline.withAlpha(77),
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isSelected ? color : Colors.transparent,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: color,
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getRatingLabel(rating),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isSelected ? color : null,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getRatingDescription(rating),
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: CustomButton(
                text: 'Back',
                onPressed: () {
                  setState(() {
                    _currentStep--;
                  });
                },
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: CustomButton(
              text: _currentStep == 2 ? 'Submit Feedback' : 'Next',
              onPressed: _canProceed() ? _handleNext : null,
              isLoading: _isSubmitting,
            ),
          ),
        ],
      ),
    );
  }

  bool _canProceed() {
    switch (_currentStep) {
      case 0:
        return true;
      case 1:
        return _selectedRating != null;
      case 2:
        return true;
      default:
        return false;
    }
  }

  void _handleNext() async {
    if (_currentStep < 2) {
      setState(() {
        _currentStep++;
      });
    } else {
      await _submitFeedback();
    }
  }

  Future<void> _submitFeedback() async {
    if (_selectedRating == null) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      await ref.read(translationAccuracyServiceProvider).submitFeedback(
            translationId: widget.translation.id,
            originalText: widget.translation.originalText ?? '',
            translatedText: widget.translation.translatedText ?? '',
            sourceLanguage: widget.translation.sourceLanguage,
            targetLanguage: widget.translation.targetLanguage,
            accuracyRating: _selectedRating!,
            feedbackType: _selectedFeedbackType,
            userConfidence: _userConfidence,
            comments: _commentsController.text.isNotEmpty
                ? _commentsController.text
                : null,
            suggestedCorrection: _correctionController.text.isNotEmpty
                ? _correctionController.text
                : null,
            originalConfidence: widget.translation.dialectConfidence,
            userProficiency: _userProficiency,
          );

      if (mounted) {
        Navigator.of(context).pop();
        widget.onFeedbackSubmitted?.call();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Thank you for your feedback!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting feedback: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  Color _getRatingColor(TranslationAccuracyRating rating) {
    switch (rating) {
      case TranslationAccuracyRating.excellent:
        return Colors.green;
      case TranslationAccuracyRating.good:
        return Colors.lightGreen;
      case TranslationAccuracyRating.fair:
        return Colors.orange;
      case TranslationAccuracyRating.poor:
        return Colors.deepOrange;
      case TranslationAccuracyRating.terrible:
        return Colors.red;
    }
  }

  String _getRatingLabel(TranslationAccuracyRating rating) {
    switch (rating) {
      case TranslationAccuracyRating.excellent:
        return 'Excellent';
      case TranslationAccuracyRating.good:
        return 'Good';
      case TranslationAccuracyRating.fair:
        return 'Fair';
      case TranslationAccuracyRating.poor:
        return 'Poor';
      case TranslationAccuracyRating.terrible:
        return 'Terrible';
    }
  }

  String _getRatingDescription(TranslationAccuracyRating rating) {
    switch (rating) {
      case TranslationAccuracyRating.excellent:
        return 'Perfect translation, captures all meaning and nuance';
      case TranslationAccuracyRating.good:
        return 'Good translation with minor issues';
      case TranslationAccuracyRating.fair:
        return 'Acceptable but has some problems';
      case TranslationAccuracyRating.poor:
        return 'Many issues, meaning partially lost';
      case TranslationAccuracyRating.terrible:
        return 'Incorrect translation, meaning lost';
    }
  }

  String _getFeedbackTypeLabel(TranslationFeedbackType type) {
    switch (type) {
      case TranslationFeedbackType.accuracy:
        return 'Accuracy';
      case TranslationFeedbackType.fluency:
        return 'Fluency';
      case TranslationFeedbackType.cultural:
        return 'Cultural';
      case TranslationFeedbackType.pronunciation:
        return 'Pronunciation';
      case TranslationFeedbackType.slang:
        return 'Slang';
      case TranslationFeedbackType.technical:
        return 'Technical';
      case TranslationFeedbackType.general:
        return 'General';
    }
  }

  String _getProficiencyLabel(LanguageProficiencyLevel level) {
    switch (level) {
      case LanguageProficiencyLevel.beginner:
        return 'Beginner';
      case LanguageProficiencyLevel.elementary:
        return 'Elementary';
      case LanguageProficiencyLevel.intermediate:
        return 'Intermediate';
      case LanguageProficiencyLevel.upperIntermediate:
        return 'Upper Intermediate';
      case LanguageProficiencyLevel.advanced:
        return 'Advanced';
      case LanguageProficiencyLevel.proficient:
        return 'Proficient';
      case LanguageProficiencyLevel.native:
        return 'Native';
    }
  }
}
