import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/translation/enhanced_cultural_context.dart';
import 'package:culture_connect/models/translation/cultural_context_model.dart';

/// Tooltip widget for displaying cultural context information
class CulturalContextTooltip extends ConsumerStatefulWidget {
  final EnhancedCulturalContext culturalContext;
  final Widget child;
  final bool showOnTap;
  final VoidCallback? onTooltipTap;

  const CulturalContextTooltip({
    super.key,
    required this.culturalContext,
    required this.child,
    this.showOnTap = false,
    this.onTooltipTap,
  });

  @override
  ConsumerState<CulturalContextTooltip> createState() =>
      _CulturalContextTooltipState();
}

class _CulturalContextTooltipState extends ConsumerState<CulturalContextTooltip>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  bool _isTooltipVisible = false;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _hideTooltip();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.showOnTap ? _toggleTooltip : widget.onTooltipTap,
      onLongPress: widget.showOnTap ? null : _toggleTooltip,
      child: Stack(
        children: [
          widget.child,
          if (_hasImportantContext())
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getContextIndicatorColor(),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.surface,
                    width: 2,
                  ),
                ),
              ).animate(onPlay: (controller) => controller.repeat()).shimmer(
                  duration: 2000.ms, color: Colors.white.withAlpha(128)),
            ),
        ],
      ),
    );
  }

  bool _hasImportantContext() {
    return widget.culturalContext.sensitivityWarnings.isNotEmpty ||
        widget.culturalContext.adaptations.isNotEmpty ||
        widget.culturalContext.regionalVariations.isNotEmpty;
  }

  Color _getContextIndicatorColor() {
    if (widget.culturalContext.sensitivityWarnings.isNotEmpty) {
      final highestSeverity = widget.culturalContext.sensitivityWarnings
          .map((w) => w.severity)
          .reduce((a, b) => a.index > b.index ? a : b);

      switch (highestSeverity) {
        case SeverityLevel.critical:
          return Colors.red;
        case SeverityLevel.warning:
          return Colors.orange;
        case SeverityLevel.info:
          return Colors.blue;
      }
    }

    if (widget.culturalContext.adaptations.isNotEmpty) {
      return Colors.purple;
    }

    if (widget.culturalContext.regionalVariations.isNotEmpty) {
      return Colors.green;
    }

    return Colors.blue;
  }

  void _toggleTooltip() {
    if (_isTooltipVisible) {
      _hideTooltip();
    } else {
      _showTooltip();
    }
  }

  void _showTooltip() {
    if (_isTooltipVisible) return;

    _isTooltipVisible = true;
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    _animationController.forward();
  }

  void _hideTooltip() {
    if (!_isTooltipVisible) return;

    _isTooltipVisible = false;
    _animationController.reverse().then((_) {
      _overlayEntry?.remove();
      _overlayEntry = null;
    });
  }

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);
    final screenSize = MediaQuery.of(context).size;

    return OverlayEntry(
      builder: (context) => Positioned(
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
        child: GestureDetector(
          onTap: _hideTooltip,
          child: Container(
            color: Colors.black.withAlpha(77),
            child: Stack(
              children: [
                Positioned(
                  left: _calculateTooltipLeft(offset, size, screenSize),
                  top: _calculateTooltipTop(offset, size, screenSize),
                  child: AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return FadeTransition(
                        opacity: _fadeAnimation,
                        child: ScaleTransition(
                          scale: _scaleAnimation,
                          child: _buildTooltipContent(),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  double _calculateTooltipLeft(Offset offset, Size size, Size screenSize) {
    const tooltipWidth = 320.0;
    double left = offset.dx + size.width / 2 - tooltipWidth / 2;

    // Ensure tooltip stays within screen bounds
    if (left < 16) left = 16;
    if (left + tooltipWidth > screenSize.width - 16) {
      left = screenSize.width - tooltipWidth - 16;
    }

    return left;
  }

  double _calculateTooltipTop(Offset offset, Size size, Size screenSize) {
    const tooltipMaxHeight = 400.0;
    double top = offset.dy + size.height + 8;

    // If tooltip would go off bottom of screen, show it above the widget
    if (top + tooltipMaxHeight > screenSize.height - 16) {
      top = offset.dy - tooltipMaxHeight - 8;
    }

    // Ensure tooltip doesn't go off top of screen
    if (top < 16) top = 16;

    return top;
  }

  Widget _buildTooltipContent() {
    return Container(
      width: 320,
      constraints: const BoxConstraints(maxHeight: 400),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(51),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTooltipHeader(),
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: _buildTooltipBody(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTooltipHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getContextIndicatorColor(),
            _getContextIndicatorColor().withAlpha(204),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.info_outline,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Cultural Context',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${widget.culturalContext.sourceLanguage} → ${widget.culturalContext.targetLanguage}',
                  style: TextStyle(
                    color: Colors.white.withAlpha(204),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${(widget.culturalContext.confidenceScore * 100).toInt()}%',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTooltipBody() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sensitivity warnings
        if (widget.culturalContext.sensitivityWarnings.isNotEmpty) ...[
          _buildSectionTitle('⚠️ Cultural Sensitivity'),
          ...widget.culturalContext.sensitivityWarnings.map(_buildWarningItem),
          const SizedBox(height: 16),
        ],

        // Cultural adaptations
        if (widget.culturalContext.adaptations.isNotEmpty) ...[
          _buildSectionTitle('🔄 Cultural Adaptations'),
          ...widget.culturalContext.adaptations.map(_buildAdaptationItem),
          const SizedBox(height: 16),
        ],

        // Regional variations
        if (widget.culturalContext.regionalVariations.isNotEmpty) ...[
          _buildSectionTitle('🌍 Regional Variations'),
          ...widget.culturalContext.regionalVariations.map(_buildVariationItem),
          const SizedBox(height: 16),
        ],

        // Cultural notes
        if (widget.culturalContext.contextNotes.isNotEmpty) ...[
          _buildSectionTitle('📝 Cultural Notes'),
          ...widget.culturalContext.contextNotes.map(_buildNoteItem),
          const SizedBox(height: 16),
        ],

        // Custom notes
        if (widget.culturalContext.customNotes.isNotEmpty) ...[
          _buildSectionTitle('💡 Local Customs'),
          ...widget.culturalContext.customNotes.map(_buildCustomNoteItem),
        ],
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildWarningItem(CulturalSensitivityWarning warning) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getSeverityColor(warning.severity).withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getSeverityColor(warning.severity).withAlpha(77),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            warning.warning,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: _getSeverityColor(warning.severity),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            warning.explanation,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdaptationItem(CulturalAdaptation adaptation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.purple.withAlpha(77),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            adaptation.type,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Colors.purple,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            adaptation.reason,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Suggestion: ${adaptation.adaptedPhrase}',
            style: const TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVariationItem(RegionalVariation variation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.green.withAlpha(77),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                variation.regionName,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(51),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '${(variation.popularity * 100).toInt()}%',
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: Colors.green,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            variation.alternativeText,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            variation.explanation,
            style: TextStyle(
              fontSize: 11,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoteItem(CulturalContextNote note) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            note.type.displayName,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            note.explanation,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomNoteItem(LocalCustomNote note) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.amber.withAlpha(77),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                note.custom,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Colors.amber,
                ),
              ),
              if (note.isEssential) ...[
                const SizedBox(width: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(51),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Text(
                    'Essential',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 4),
          Text(
            note.description,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Color _getSeverityColor(SeverityLevel severity) {
    switch (severity) {
      case SeverityLevel.critical:
        return Colors.red;
      case SeverityLevel.warning:
        return Colors.orange;
      case SeverityLevel.info:
        return Colors.blue;
    }
  }
}
