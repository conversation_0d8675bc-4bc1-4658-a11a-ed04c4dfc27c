import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/message_model.dart';

import 'package:culture_connect/providers/auth_provider.dart';

/// Context menu for message actions
class MessageContextMenu extends ConsumerWidget {
  final MessageModel message;
  final VoidCallback? onReply;
  final VoidCallback? onForward;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onReact;
  final VoidCallback? onCopy;
  final VoidCallback? onInfo;

  const MessageContextMenu({
    super.key,
    required this.message,
    this.onReply,
    this.onForward,
    this.onEdit,
    this.onDelete,
    this.onReact,
    this.onCopy,
    this.onInfo,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final currentUser = ref.watch(authStateProvider).user;
    final isMyMessage = currentUser?.id == message.senderId;

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withAlpha(26),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Quick reactions row
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildQuickReaction(context, '👍'),
                const SizedBox(width: 8),
                _buildQuickReaction(context, '❤️'),
                const SizedBox(width: 8),
                _buildQuickReaction(context, '😂'),
                const SizedBox(width: 8),
                _buildQuickReaction(context, '😮'),
                const SizedBox(width: 8),
                _buildQuickReaction(context, '😢'),
                const SizedBox(width: 8),
                _buildQuickReaction(context, '😡'),
                const SizedBox(width: 8),
                _buildMoreReactionsButton(context),
              ],
            ),
          ),

          const Divider(height: 1),

          // Action items
          _buildMenuItem(
            context,
            icon: Icons.reply,
            title: 'Reply',
            onTap: onReply,
          ),

          _buildMenuItem(
            context,
            icon: Icons.forward,
            title: 'Forward',
            onTap: onForward,
          ),

          _buildMenuItem(
            context,
            icon: Icons.copy,
            title: 'Copy',
            onTap: onCopy,
          ),

          if (isMyMessage) ...[
            _buildMenuItem(
              context,
              icon: Icons.edit,
              title: 'Edit',
              onTap: onEdit,
            ),
            _buildMenuItem(
              context,
              icon: Icons.delete,
              title: 'Delete',
              onTap: onDelete,
              isDestructive: true,
            ),
          ],

          _buildMenuItem(
            context,
            icon: Icons.info_outline,
            title: 'Info',
            onTap: onInfo,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickReaction(BuildContext context, String emoji) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
        // Reaction functionality will be implemented in future enhancement
      },
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Center(
          child: Text(
            emoji,
            style: const TextStyle(fontSize: 16),
          ),
        ),
      ),
    );
  }

  Widget _buildMoreReactionsButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
        onReact?.call();
      },
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Icon(
          Icons.add,
          size: 16,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    VoidCallback? onTap,
    bool isDestructive = false,
  }) {
    final theme = Theme.of(context);
    final color =
        isDestructive ? theme.colorScheme.error : theme.colorScheme.onSurface;

    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
        onTap?.call();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 20,
              color: color,
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show context menu
  static void show(
    BuildContext context, {
    required MessageModel message,
    required Offset position,
    VoidCallback? onReply,
    VoidCallback? onForward,
    VoidCallback? onEdit,
    VoidCallback? onDelete,
    VoidCallback? onReact,
    VoidCallback? onCopy,
    VoidCallback? onInfo,
  }) {
    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (context) => Stack(
        children: [
          // Invisible barrier to close menu
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
          ),

          // Context menu
          Positioned(
            left: position.dx,
            top: position.dy,
            child: MessageContextMenu(
              message: message,
              onReply: onReply,
              onForward: onForward,
              onEdit: onEdit,
              onDelete: onDelete,
              onReact: onReact,
              onCopy: onCopy,
              onInfo: onInfo,
            ),
          ),
        ],
      ),
    );
  }
}

/// Message action buttons for enhanced message bubble
class MessageActionButtons extends StatelessWidget {
  final MessageModel message;
  final bool isMe;
  final VoidCallback? onReply;
  final VoidCallback? onReact;
  final VoidCallback? onMore;

  const MessageActionButtons({
    super.key,
    required this.message,
    required this.isMe,
    this.onReply,
    this.onReact,
    this.onMore,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        left: isMe ? 0 : 8,
        right: isMe ? 8 : 0,
        top: 4,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!isMe) ...[
            _buildActionButton(
              context,
              icon: Icons.reply,
              onTap: onReply,
            ),
            const SizedBox(width: 4),
          ],
          _buildActionButton(
            context,
            icon: Icons.add_reaction_outlined,
            onTap: onReact,
          ),
          const SizedBox(width: 4),
          _buildActionButton(
            context,
            icon: Icons.more_horiz,
            onTap: onMore,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 28,
        height: 28,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface.withAlpha(230),
          borderRadius: BorderRadius.circular(14),
          border: Border.all(
            color: theme.colorScheme.outline.withAlpha(51),
            width: 0.5,
          ),
        ),
        child: Icon(
          icon,
          size: 16,
          color: theme.colorScheme.onSurface.withAlpha(179),
        ),
      ),
    );
  }
}
