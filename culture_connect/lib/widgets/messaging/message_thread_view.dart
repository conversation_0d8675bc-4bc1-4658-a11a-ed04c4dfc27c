import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/messaging/message_thread_model.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/providers/messaging/advanced_messaging_provider.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/widgets/messaging/enhanced_message_bubble.dart';

/// Widget for displaying message threads and replies
class MessageThreadView extends ConsumerStatefulWidget {
  final MessageModel parentMessage;
  final String chatId;

  const MessageThreadView({
    super.key,
    required this.parentMessage,
    required this.chatId,
  });

  @override
  ConsumerState<MessageThreadView> createState() => _MessageThreadViewState();
}

class _MessageThreadViewState extends ConsumerState<MessageThreadView> {
  final TextEditingController _replyController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isReplying = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadThread();
    });
  }

  @override
  void dispose() {
    _replyController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadThread() {
    final threadNotifier = ref.read(messageThreadProvider.notifier);
    threadNotifier.loadThread(widget.parentMessage.id);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final threadState = ref.watch(messageThreadProvider);
    final currentUser = ref.watch(authStateProvider).user;

    final thread = threadState.threads[widget.parentMessage.id];
    final replies = thread != null
        ? threadState.threadReplies[thread.id] ?? []
        : <MessageReplyModel>[];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Thread'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          if (thread != null)
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: Center(
                child: Text(
                  '${thread.replyCount} ${thread.replyCount == 1 ? 'reply' : 'replies'}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(153),
                  ),
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Parent message
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest.withAlpha(77),
              border: Border(
                bottom: BorderSide(
                  color: theme.colorScheme.outline.withAlpha(51),
                  width: 0.5,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.forum_outlined,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Original Message',
                      style: theme.textTheme.labelMedium?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                EnhancedMessageBubble(
                  message: widget.parentMessage,
                  isMe: currentUser?.id == widget.parentMessage.senderId,
                  showAvatar: true,
                  showTimestamp: true,
                ),
              ],
            ),
          ),

          // Thread replies
          Expanded(
            child: thread == null
                ? _buildEmptyThread(context)
                : replies.isEmpty
                    ? _buildNoReplies(context)
                    : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        itemCount: replies.length,
                        itemBuilder: (context, index) {
                          final reply = replies[index];
                          return Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 4,
                            ),
                            child: _buildReplyBubble(context, reply),
                          );
                        },
                      ),
          ),

          // Reply input
          _buildReplyInput(context, thread),
        ],
      ),
    );
  }

  Widget _buildEmptyThread(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.forum_outlined,
            size: 48,
            color: theme.colorScheme.onSurface.withAlpha(128),
          ),
          const SizedBox(height: 16),
          Text(
            'Start a thread',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(153),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Be the first to reply to this message',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(128),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoReplies(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 48,
            color: theme.colorScheme.onSurface.withAlpha(128),
          ),
          const SizedBox(height: 16),
          Text(
            'No replies yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(153),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start the conversation',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(128),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReplyBubble(BuildContext context, MessageReplyModel reply) {
    final theme = Theme.of(context);
    final currentUser = ref.watch(authStateProvider).user;
    final isMe = currentUser?.id == reply.senderId;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Thread line
          Container(
            width: 2,
            height: 40,
            margin: const EdgeInsets.only(right: 12, top: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(77),
              borderRadius: BorderRadius.circular(1),
            ),
          ),

          // Reply bubble
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isMe
                    ? theme.colorScheme.primary.withAlpha(26)
                    : theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Reply header
                  Row(
                    children: [
                      Text(
                        isMe ? 'You' : 'User ${reply.senderId.substring(0, 8)}',
                        style: theme.textTheme.labelSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _formatTime(reply.timestamp),
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(128),
                        ),
                      ),
                      if (reply.isEdited) ...[
                        const SizedBox(width: 4),
                        Text(
                          '(edited)',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.onSurface.withAlpha(102),
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ],
                  ),

                  const SizedBox(height: 4),

                  // Reply content
                  Text(
                    reply.text,
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReplyInput(BuildContext context, MessageThreadModel? thread) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withAlpha(51),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _replyController,
              decoration: InputDecoration(
                hintText: 'Reply to thread...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: theme.colorScheme.outline.withAlpha(77),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: theme.colorScheme.outline.withAlpha(77),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: theme.colorScheme.primary,
                    width: 2,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: null,
              textCapitalization: TextCapitalization.sentences,
            ),
          ),
          const SizedBox(width: 8),
          FloatingActionButton.small(
            onPressed: _isReplying ? null : _sendReply,
            backgroundColor: theme.colorScheme.primary,
            child: _isReplying
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.onPrimary,
                      ),
                    ),
                  )
                : Icon(
                    Icons.send,
                    color: theme.colorScheme.onPrimary,
                    size: 20,
                  ),
          ),
        ],
      ),
    );
  }

  Future<void> _sendReply() async {
    if (_replyController.text.trim().isEmpty) return;

    setState(() {
      _isReplying = true;
    });

    try {
      final currentUser = ref.read(authStateProvider).user;
      if (currentUser == null) return;

      final threadNotifier = ref.read(messageThreadProvider.notifier);

      // Create thread if it doesn't exist
      MessageThreadModel? thread =
          threadNotifier.getThread(widget.parentMessage.id);
      if (thread == null) {
        thread = await threadNotifier.createThread(
          parentMessageId: widget.parentMessage.id,
          chatId: widget.chatId,
        );
        if (thread == null) return;
      }

      // Add reply to thread
      await threadNotifier.addReply(
        threadId: thread.id,
        parentMessageId: widget.parentMessage.id,
        replyToMessageId: widget.parentMessage.id,
        chatId: widget.chatId,
        senderId: currentUser.id,
        text: _replyController.text.trim(),
        type: MessageType.text,
      );

      _replyController.clear();

      // Scroll to bottom
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    } finally {
      setState(() {
        _isReplying = false;
      });
    }
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else {
      return '${difference.inDays}d';
    }
  }
}
