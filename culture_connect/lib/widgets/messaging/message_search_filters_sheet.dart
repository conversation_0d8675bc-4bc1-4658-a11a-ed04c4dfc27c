import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/message_search_model.dart';
import 'package:culture_connect/providers/messaging/message_search_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

class MessageSearchFiltersSheet extends ConsumerStatefulWidget {
  const MessageSearchFiltersSheet({super.key});

  @override
  ConsumerState<MessageSearchFiltersSheet> createState() => _MessageSearchFiltersSheetState();
}

class _MessageSearchFiltersSheetState extends ConsumerState<MessageSearchFiltersSheet> {
  late MessageSearchFilters _filters;
  late Map<String, dynamic> _sortOptions;

  @override
  void initState() {
    super.initState();
    _filters = ref.read(searchFiltersProvider);
    _sortOptions = Map.from(ref.read(searchSortProvider));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                const Text(
                  'Search Filters',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _resetFilters,
                  child: const Text('Reset'),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // Filters content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date range filter
                  _buildDateRangeFilter(),
                  
                  const SizedBox(height: 24),
                  
                  // Message type filter
                  _buildMessageTypeFilter(),
                  
                  const SizedBox(height: 24),
                  
                  // Media filter
                  _buildMediaFilter(),
                  
                  const SizedBox(height: 24),
                  
                  // Translation filter
                  _buildTranslationFilter(),
                  
                  const SizedBox(height: 24),
                  
                  // Sort options
                  _buildSortOptions(),
                ],
              ),
            ),
          ),
          
          // Action buttons
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(top: BorderSide(color: Colors.grey[200]!)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Apply Filters'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Date Range',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                label: 'From',
                date: _filters.startDate,
                onTap: () => _selectDate(isStartDate: true),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDateField(
                label: 'To',
                date: _filters.endDate,
                onTap: () => _selectDate(isStartDate: false),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              date != null
                  ? DateFormat('MMM d, y').format(date)
                  : 'Select date',
              style: TextStyle(
                fontSize: 14,
                color: date != null
                    ? AppTheme.textPrimaryColor
                    : AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Message Type',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: MessageType.values.map((type) {
            final isSelected = _filters.messageType == type;
            return FilterChip(
              label: Text(_getMessageTypeLabel(type)),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _filters = _filters.copyWith(
                    messageType: selected ? type : null,
                  );
                });
              },
              selectedColor: AppTheme.primaryColor.withAlpha(51),
              checkmarkColor: AppTheme.primaryColor,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildMediaFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Media',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        CheckboxListTile(
          title: const Text('Has Media'),
          subtitle: const Text('Images, videos, audio files'),
          value: _filters.hasMedia,
          tristate: true,
          onChanged: (value) {
            setState(() {
              _filters = _filters.copyWith(hasMedia: value);
            });
          },
          activeColor: AppTheme.primaryColor,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildTranslationFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Translation',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        CheckboxListTile(
          title: const Text('Translated Messages'),
          subtitle: const Text('Messages that have been translated'),
          value: _filters.isTranslated,
          tristate: true,
          onChanged: (value) {
            setState(() {
              _filters = _filters.copyWith(isTranslated: value);
            });
          },
          activeColor: AppTheme.primaryColor,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildSortOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Sort By',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        Column(
          children: [
            RadioListTile<String>(
              title: const Text('Relevance'),
              value: 'relevance',
              groupValue: _sortOptions['sortBy'],
              onChanged: (value) {
                setState(() {
                  _sortOptions['sortBy'] = value;
                });
              },
              activeColor: AppTheme.primaryColor,
              contentPadding: EdgeInsets.zero,
            ),
            RadioListTile<String>(
              title: const Text('Date'),
              value: 'date',
              groupValue: _sortOptions['sortBy'],
              onChanged: (value) {
                setState(() {
                  _sortOptions['sortBy'] = value;
                });
              },
              activeColor: AppTheme.primaryColor,
              contentPadding: EdgeInsets.zero,
            ),
            RadioListTile<String>(
              title: const Text('Sender'),
              value: 'sender',
              groupValue: _sortOptions['sortBy'],
              onChanged: (value) {
                setState(() {
                  _sortOptions['sortBy'] = value;
                });
              },
              activeColor: AppTheme.primaryColor,
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: const Text('Ascending Order'),
          value: _sortOptions['sortAscending'] as bool,
          onChanged: (value) {
            setState(() {
              _sortOptions['sortAscending'] = value;
            });
          },
          activeColor: AppTheme.primaryColor,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Future<void> _selectDate({required bool isStartDate}) async {
    final initialDate = isStartDate ? _filters.startDate : _filters.endDate;
    final firstDate = DateTime(2020);
    final lastDate = DateTime.now();

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppTheme.primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      setState(() {
        if (isStartDate) {
          _filters = _filters.copyWith(startDate: selectedDate);
        } else {
          _filters = _filters.copyWith(endDate: selectedDate);
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      _filters = const MessageSearchFilters();
      _sortOptions = {
        'sortBy': 'relevance',
        'sortAscending': false,
      };
    });
  }

  void _applyFilters() {
    ref.read(searchFiltersProvider.notifier).state = _filters;
    ref.read(searchSortProvider.notifier).state = _sortOptions;
    Navigator.of(context).pop();
  }

  String _getMessageTypeLabel(MessageType type) {
    switch (type) {
      case MessageType.text:
        return 'Text';
      case MessageType.image:
        return 'Image';
      case MessageType.video:
        return 'Video';
      case MessageType.audio:
        return 'Audio';
      case MessageType.location:
        return 'Location';
      case MessageType.contact:
        return 'Contact';
      case MessageType.file:
        return 'File';
      case MessageType.system:
        return 'System';
    }
  }
}
