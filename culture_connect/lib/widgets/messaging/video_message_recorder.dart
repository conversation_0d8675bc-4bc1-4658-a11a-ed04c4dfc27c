import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:camera/camera.dart';
import 'package:culture_connect/providers/messaging/video_message_provider.dart';
import 'package:culture_connect/models/messaging/video_message_model.dart';

/// Widget for recording video messages
class VideoMessageRecorder extends ConsumerStatefulWidget {
  final Function(VideoMessageModel) onVideoMessageRecorded;
  final VideoRecordingConfig config;
  final Color? primaryColor;
  final Color? backgroundColor;

  const VideoMessageRecorder({
    super.key,
    required this.onVideoMessageRecorded,
    this.config = const VideoRecordingConfig(),
    this.primaryColor,
    this.backgroundColor,
  });

  @override
  ConsumerState<VideoMessageRecorder> createState() =>
      _VideoMessageRecorderState();
}

class _VideoMessageRecorderState extends ConsumerState<VideoMessageRecorder>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final recordingState = ref.watch(videoRecordingStateProvider);
    final recordingNotifier = ref.read(videoRecordingStateProvider.notifier);

    // Listen to recording duration
    ref.listen(videoRecordingDurationProvider, (previous, next) {
      next.whenData((duration) {
        recordingNotifier.updateDuration(duration);
      });
    });

    // Handle recording state changes
    ref.listen(videoRecordingStateProvider, (previous, current) {
      if (current.isRecording && !_pulseController.isAnimating) {
        _pulseController.repeat(reverse: true);
      } else if (!current.isRecording && _pulseController.isAnimating) {
        _pulseController.stop();
        _pulseController.reset();
      }
    });

    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withAlpha(26),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Camera preview
          Container(
            height: 300,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
            ),
            child: ClipRRect(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(24)),
              child: recordingState.isInitialized &&
                      recordingState.cameraController != null
                  ? CameraPreview(recordingState.cameraController!)
                  : Container(
                      color: theme.colorScheme.surfaceContainerHigh,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.videocam_off,
                              size: 48,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              recordingState.error ?? 'Initializing camera...',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
            ),
          ),

          // Recording overlay
          if (recordingState.isRecording)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer,
                borderRadius:
                    const BorderRadius.vertical(bottom: Radius.circular(24)),
              ),
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'REC',
                    style: theme.textTheme.titleSmall?.copyWith(
                      color: theme.colorScheme.onErrorContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _formatDuration(recordingState.duration),
                    style: theme.textTheme.titleSmall?.copyWith(
                      color: theme.colorScheme.onErrorContainer,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

          // Controls
          Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Header
                if (!recordingState.isRecording)
                  Row(
                    children: [
                      Icon(
                        Icons.videocam,
                        color: widget.primaryColor ?? theme.colorScheme.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Video Message',
                        style: theme.textTheme.titleSmall?.copyWith(
                          color:
                              widget.primaryColor ?? theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),

                if (!recordingState.isRecording) const SizedBox(height: 16),

                // Recording controls
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Cancel button (only show when recording)
                    if (recordingState.isRecording)
                      _buildControlButton(
                        icon: Icons.close,
                        label: 'Cancel',
                        color: theme.colorScheme.error,
                        onPressed: () => _cancelRecording(recordingNotifier),
                      ),

                    // Record/Stop button
                    GestureDetector(
                      onTapDown: (_) => _scaleController.forward(),
                      onTapUp: (_) => _scaleController.reverse(),
                      onTapCancel: () => _scaleController.reverse(),
                      child: AnimatedBuilder(
                        animation: Listenable.merge(
                            [_pulseAnimation, _scaleAnimation]),
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _scaleAnimation.value,
                            child: Transform.scale(
                              scale: recordingState.isRecording
                                  ? _pulseAnimation.value
                                  : 1.0,
                              child: Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  color: recordingState.isRecording
                                      ? theme.colorScheme.error
                                      : (widget.primaryColor ??
                                          theme.colorScheme.primary),
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: (recordingState.isRecording
                                              ? theme.colorScheme.error
                                              : (widget.primaryColor ??
                                                  theme.colorScheme.primary))
                                          .withAlpha(77),
                                      blurRadius: 12,
                                      spreadRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(40),
                                    onTap: recordingState.isRecording
                                        ? () =>
                                            _stopRecording(recordingNotifier)
                                        : () =>
                                            _startRecording(recordingNotifier),
                                    child: Icon(
                                      recordingState.isRecording
                                          ? Icons.stop
                                          : Icons.videocam,
                                      color: Colors.white,
                                      size: 32,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                    // Send button (only show when recording)
                    if (recordingState.isRecording)
                      _buildControlButton(
                        icon: Icons.send,
                        label: 'Send',
                        color: theme.colorScheme.primary,
                        onPressed: () => _sendRecording(recordingNotifier),
                      ),
                  ],
                ),

                // Error message
                if (recordingState.error != null &&
                    !recordingState.isInitialized)
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Text(
                      recordingState.error!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.error,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                // Recording tips
                if (!recordingState.isRecording && recordingState.isInitialized)
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Text(
                      'Tap to start recording your video message',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: color.withAlpha(26),
            shape: BoxShape.circle,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(24),
              onTap: onPressed,
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w500,
              ),
        ),
      ],
    );
  }

  Future<void> _startRecording(VideoRecordingNotifier notifier) async {
    HapticFeedback.lightImpact();
    await notifier.startRecording(config: widget.config);
  }

  Future<void> _stopRecording(VideoRecordingNotifier notifier) async {
    HapticFeedback.mediumImpact();
    final path = await notifier.stopRecording();
    if (path != null) {
      await _processRecording(path);
    }
  }

  Future<void> _cancelRecording(VideoRecordingNotifier notifier) async {
    HapticFeedback.lightImpact();
    await notifier.stopRecording();
    notifier.reset();
  }

  Future<void> _sendRecording(VideoRecordingNotifier notifier) async {
    HapticFeedback.mediumImpact();
    final path = await notifier.stopRecording();
    if (path != null) {
      await _processRecording(path);
    }
  }

  Future<void> _processRecording(String filePath) async {
    try {
      // Create video message model
      final videoMessage = await ref.read(videoMessageCreationProvider(
        VideoMessageCreationParams(
          messageId: DateTime.now().millisecondsSinceEpoch.toString(),
          filePath: filePath,
        ),
      ).future);

      if (videoMessage != null) {
        widget.onVideoMessageRecorded(videoMessage);
        ref.read(videoRecordingStateProvider.notifier).reset();
      }
    } catch (e) {
      ref.read(videoRecordingStateProvider.notifier).setError(
            'Failed to process recording: $e',
          );
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
