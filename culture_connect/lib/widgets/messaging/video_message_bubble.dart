import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/messaging/video_message_model.dart';
import 'package:culture_connect/widgets/messaging/video_message_player.dart';

/// Video message bubble for chat integration
class VideoMessageBubble extends ConsumerWidget {
  final VideoMessageModel videoMessage;
  final bool isFromCurrentUser;
  final Color? primaryColor;
  final Color? backgroundColor;
  final VoidCallback? onLongPress;
  final VoidCallback? onDoubleTap;

  const VideoMessageBubble({
    super.key,
    required this.videoMessage,
    required this.isFromCurrentUser,
    this.primaryColor,
    this.backgroundColor,
    this.onLongPress,
    this.onDoubleTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return GestureDetector(
      onLongPress: onLongPress,
      onDoubleTap: onDoubleTap,
      child: Container(
        margin: EdgeInsets.only(
          left: isFromCurrentUser ? 64 : 16,
          right: isFromCurrentUser ? 16 : 64,
          bottom: 8,
        ),
        child: Column(
          crossAxisAlignment: isFromCurrentUser
              ? CrossAxisAlignment.end
              : CrossAxisAlignment.start,
          children: [
            // Message bubble
            Container(
              constraints: const BoxConstraints(
                minWidth: 240,
                maxWidth: 320,
              ),
              decoration: BoxDecoration(
                color: backgroundColor ??
                    (isFromCurrentUser
                        ? (primaryColor ?? theme.colorScheme.primary)
                            .withAlpha(26)
                        : theme.colorScheme.surfaceContainerHighest),
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(20),
                  topRight: const Radius.circular(20),
                  bottomLeft: Radius.circular(isFromCurrentUser ? 20 : 4),
                  bottomRight: Radius.circular(isFromCurrentUser ? 4 : 20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.shadow.withAlpha(26),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(20),
                  topRight: const Radius.circular(20),
                  bottomLeft: Radius.circular(isFromCurrentUser ? 20 : 4),
                  bottomRight: Radius.circular(isFromCurrentUser ? 4 : 20),
                ),
                child: VideoMessagePlayer(
                  videoMessage: videoMessage,
                  primaryColor: primaryColor ?? theme.colorScheme.primary,
                  backgroundColor: Colors.transparent,
                ),
              ),
            ),

            // Message status and timestamp
            Padding(
              padding: const EdgeInsets.only(top: 4, left: 8, right: 8),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Upload status
                  if (videoMessage.status == VideoMessageStatus.uploading)
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(
                            strokeWidth: 1.5,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Uploading...',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    )
                  else if (videoMessage.status == VideoMessageStatus.error)
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 12,
                          color: theme.colorScheme.error,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Failed',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.error,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    )
                  else
                    // Timestamp and file size
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _formatTimestamp(videoMessage.createdAt),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 10,
                          ),
                        ),
                        if (videoMessage.fileSize > 0)
                          Text(
                            _formatFileSize(videoMessage.fileSize),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                              fontSize: 9,
                            ),
                          ),
                      ],
                    ),

                  // Message status indicators for sent messages
                  if (isFromCurrentUser &&
                      videoMessage.status == VideoMessageStatus.uploaded) ...[
                    const SizedBox(width: 4),
                    Icon(
                      Icons.done_all,
                      size: 12,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}

/// Compact video message bubble for group chats
class CompactVideoMessageBubble extends ConsumerWidget {
  final VideoMessageModel videoMessage;
  final bool isFromCurrentUser;
  final String? senderName;
  final Color? primaryColor;

  const CompactVideoMessageBubble({
    super.key,
    required this.videoMessage,
    required this.isFromCurrentUser,
    this.senderName,
    this.primaryColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Avatar placeholder
          if (!isFromCurrentUser)
            Container(
              width: 32,
              height: 32,
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                color: primaryColor?.withAlpha(26) ??
                    theme.colorScheme.primary.withAlpha(26),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  senderName?.substring(0, 1).toUpperCase() ?? '?',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: primaryColor ?? theme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),

          // Message content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Sender name
                if (!isFromCurrentUser && senderName != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      senderName!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: primaryColor ?? theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                // Video message
                Container(
                  constraints: const BoxConstraints(maxWidth: 280),
                  decoration: BoxDecoration(
                    color: isFromCurrentUser
                        ? (primaryColor ?? theme.colorScheme.primary)
                            .withAlpha(26)
                        : theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: VideoMessagePlayer(
                      videoMessage: videoMessage,
                      primaryColor: primaryColor ?? theme.colorScheme.primary,
                      backgroundColor: Colors.transparent,
                    ),
                  ),
                ),

                // Timestamp and file info
                Padding(
                  padding: const EdgeInsets.only(top: 2, left: 4),
                  child: Row(
                    children: [
                      Text(
                        _formatTimestamp(videoMessage.createdAt),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontSize: 10,
                        ),
                      ),
                      if (videoMessage.fileSize > 0) ...[
                        const SizedBox(width: 8),
                        Text(
                          _formatFileSize(videoMessage.fileSize),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}

/// Video message bubble with reply functionality
class ReplyableVideoMessageBubble extends ConsumerWidget {
  final VideoMessageModel videoMessage;
  final bool isFromCurrentUser;
  final VoidCallback? onReply;
  final VoidCallback? onForward;
  final VoidCallback? onDelete;
  final VoidCallback? onDownload;
  final Color? primaryColor;

  const ReplyableVideoMessageBubble({
    super.key,
    required this.videoMessage,
    required this.isFromCurrentUser,
    this.onReply,
    this.onForward,
    this.onDelete,
    this.onDownload,
    this.primaryColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onLongPress: () => _showMessageOptions(context),
      child: VideoMessageBubble(
        videoMessage: videoMessage,
        isFromCurrentUser: isFromCurrentUser,
        primaryColor: primaryColor,
      ),
    );
  }

  void _showMessageOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (onReply != null)
              ListTile(
                leading: const Icon(Icons.reply),
                title: const Text('Reply'),
                onTap: () {
                  Navigator.pop(context);
                  onReply?.call();
                },
              ),
            if (onForward != null)
              ListTile(
                leading: const Icon(Icons.forward),
                title: const Text('Forward'),
                onTap: () {
                  Navigator.pop(context);
                  onForward?.call();
                },
              ),
            if (onDownload != null)
              ListTile(
                leading: const Icon(Icons.download),
                title: const Text('Download'),
                onTap: () {
                  Navigator.pop(context);
                  onDownload?.call();
                },
              ),
            if (onDelete != null)
              ListTile(
                leading: Icon(Icons.delete,
                    color: Theme.of(context).colorScheme.error),
                title: Text('Delete',
                    style:
                        TextStyle(color: Theme.of(context).colorScheme.error)),
                onTap: () {
                  Navigator.pop(context);
                  onDelete?.call();
                },
              ),
          ],
        ),
      ),
    );
  }
}
