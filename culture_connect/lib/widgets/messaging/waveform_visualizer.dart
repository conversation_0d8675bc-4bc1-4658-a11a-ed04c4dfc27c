import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Widget for visualizing audio waveforms
class WaveformVisualizer extends StatefulWidget {
  final List<double> waveform;
  final Color color;
  final Color? backgroundColor;
  final bool isAnimating;
  final double height;
  final double barWidth;
  final double barSpacing;
  final int maxBars;
  final bool showProgress;
  final double progress;

  const WaveformVisualizer({
    super.key,
    required this.waveform,
    required this.color,
    this.backgroundColor,
    this.isAnimating = false,
    this.height = 60,
    this.barWidth = 3,
    this.barSpacing = 2,
    this.maxBars = 50,
    this.showProgress = false,
    this.progress = 0.0,
  });

  @override
  State<WaveformVisualizer> createState() => _WaveformVisualizerState();
}

class _WaveformVisualizerState extends State<WaveformVisualizer>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.isAnimating) {
      _animationController.repeat();
    }
  }

  @override
  void didUpdateWidget(WaveformVisualizer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isAnimating != oldWidget.isAnimating) {
      if (widget.isAnimating) {
        _animationController.repeat();
      } else {
        _animationController.stop();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            painter: WaveformPainter(
              waveform: widget.waveform,
              color: widget.color,
              barWidth: widget.barWidth,
              barSpacing: widget.barSpacing,
              maxBars: widget.maxBars,
              animationValue: widget.isAnimating ? _animation.value : 1.0,
              showProgress: widget.showProgress,
              progress: widget.progress,
            ),
            size: Size.infinite,
          );
        },
      ),
    );
  }
}

/// Custom painter for drawing waveforms
class WaveformPainter extends CustomPainter {
  final List<double> waveform;
  final Color color;
  final double barWidth;
  final double barSpacing;
  final int maxBars;
  final double animationValue;
  final bool showProgress;
  final double progress;

  WaveformPainter({
    required this.waveform,
    required this.color,
    required this.barWidth,
    required this.barSpacing,
    required this.maxBars,
    required this.animationValue,
    required this.showProgress,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (waveform.isEmpty) {
      _drawPlaceholder(canvas, size);
      return;
    }

    final paint = Paint()
      ..color = color
      ..strokeCap = StrokeCap.round;

    final progressPaint = Paint()
      ..color = color.withAlpha(128)
      ..strokeCap = StrokeCap.round;

    final totalBarWidth = barWidth + barSpacing;
    final availableWidth = size.width;
    final barsToShow = math.min(maxBars, (availableWidth / totalBarWidth).floor());
    
    // Calculate which waveform data to show
    final dataPoints = _getDataPoints(barsToShow);
    
    // Calculate starting position to center the waveform
    final totalWaveformWidth = barsToShow * totalBarWidth - barSpacing;
    final startX = (availableWidth - totalWaveformWidth) / 2;
    
    final centerY = size.height / 2;
    final maxBarHeight = size.height * 0.8;

    for (int i = 0; i < dataPoints.length; i++) {
      final x = startX + (i * totalBarWidth);
      final amplitude = dataPoints[i];
      
      // Apply animation effect
      final animatedAmplitude = amplitude * animationValue;
      
      // Calculate bar height
      final barHeight = math.max(2.0, animatedAmplitude * maxBarHeight);
      
      // Determine paint based on progress
      final currentPaint = showProgress && (i / dataPoints.length) <= progress
          ? paint
          : (showProgress ? progressPaint : paint);
      
      // Draw the bar
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromCenter(
            center: Offset(x + barWidth / 2, centerY),
            width: barWidth,
            height: barHeight,
          ),
          Radius.circular(barWidth / 2),
        ),
        currentPaint,
      );
    }
  }

  List<double> _getDataPoints(int barsToShow) {
    if (waveform.length <= barsToShow) {
      return waveform;
    }

    // Downsample the waveform data
    final step = waveform.length / barsToShow;
    final dataPoints = <double>[];

    for (int i = 0; i < barsToShow; i++) {
      final startIndex = (i * step).floor();
      final endIndex = math.min(((i + 1) * step).floor(), waveform.length);
      
      // Take the maximum amplitude in this range
      double maxAmplitude = 0.0;
      for (int j = startIndex; j < endIndex; j++) {
        maxAmplitude = math.max(maxAmplitude, waveform[j]);
      }
      
      dataPoints.add(maxAmplitude);
    }

    return dataPoints;
  }

  void _drawPlaceholder(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withAlpha(77)
      ..strokeCap = StrokeCap.round;

    final totalBarWidth = barWidth + barSpacing;
    final availableWidth = size.width;
    final barsToShow = math.min(maxBars, (availableWidth / totalBarWidth).floor());
    
    final totalWaveformWidth = barsToShow * totalBarWidth - barSpacing;
    final startX = (availableWidth - totalWaveformWidth) / 2;
    
    final centerY = size.height / 2;
    final maxBarHeight = size.height * 0.8;

    // Draw placeholder bars with random heights
    final random = math.Random(42); // Fixed seed for consistent placeholder
    
    for (int i = 0; i < barsToShow; i++) {
      final x = startX + (i * totalBarWidth);
      final amplitude = 0.1 + (random.nextDouble() * 0.4); // Random height between 0.1 and 0.5
      
      // Apply animation effect for pulsing placeholder
      final animatedAmplitude = amplitude * (0.5 + 0.5 * animationValue);
      
      final barHeight = math.max(2.0, animatedAmplitude * maxBarHeight);
      
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromCenter(
            center: Offset(x + barWidth / 2, centerY),
            width: barWidth,
            height: barHeight,
          ),
          Radius.circular(barWidth / 2),
        ),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(WaveformPainter oldDelegate) {
    return oldDelegate.waveform != waveform ||
        oldDelegate.color != color ||
        oldDelegate.animationValue != animationValue ||
        oldDelegate.progress != progress ||
        oldDelegate.showProgress != showProgress;
  }
}

/// Simplified waveform widget for message bubbles
class MiniWaveformVisualizer extends StatelessWidget {
  final List<double> waveform;
  final Color color;
  final double height;
  final bool showProgress;
  final double progress;

  const MiniWaveformVisualizer({
    super.key,
    required this.waveform,
    required this.color,
    this.height = 24,
    this.showProgress = false,
    this.progress = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    return WaveformVisualizer(
      waveform: waveform,
      color: color,
      height: height,
      barWidth: 2,
      barSpacing: 1,
      maxBars: 30,
      showProgress: showProgress,
      progress: progress,
    );
  }
}

/// Animated waveform for recording state
class AnimatedWaveformVisualizer extends StatelessWidget {
  final Color color;
  final double height;

  const AnimatedWaveformVisualizer({
    super.key,
    required this.color,
    this.height = 40,
  });

  @override
  Widget build(BuildContext context) {
    // Generate some sample waveform data for animation
    final sampleWaveform = List.generate(20, (index) {
      return 0.2 + (math.sin(index * 0.5) * 0.3).abs();
    });

    return WaveformVisualizer(
      waveform: sampleWaveform,
      color: color,
      height: height,
      isAnimating: true,
      barWidth: 3,
      barSpacing: 2,
      maxBars: 20,
    );
  }
}
