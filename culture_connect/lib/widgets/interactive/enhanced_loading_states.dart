import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shimmer/shimmer.dart';
import 'package:culture_connect/services/interactive_features_service.dart'
    as interactive;

/// Enhanced loading state widget with haptic feedback and animations
class EnhancedLoadingState extends ConsumerStatefulWidget {
  final bool isLoading;
  final Widget child;
  final Widget? loadingWidget;
  final String? loadingText;
  final bool enableHapticFeedback;
  final Duration animationDuration;

  const EnhancedLoadingState({
    super.key,
    required this.isLoading,
    required this.child,
    this.loadingWidget,
    this.loadingText,
    this.enableHapticFeedback = true,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  ConsumerState<EnhancedLoadingState> createState() =>
      _EnhancedLoadingStateState();
}

class _EnhancedLoadingStateState extends ConsumerState<EnhancedLoadingState>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _previousLoadingState = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _previousLoadingState = widget.isLoading;
    if (widget.isLoading) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(EnhancedLoadingState oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading != _previousLoadingState) {
      _handleLoadingStateChange();
      _previousLoadingState = widget.isLoading;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handleLoadingStateChange() async {
    if (widget.enableHapticFeedback) {
      final interactiveService =
          ref.read(interactive.interactiveFeaturesServiceProvider);
      if (widget.isLoading) {
        await interactiveService
            .triggerStateFeedback(interactive.StateType.loading);
        _animationController.forward();
      } else {
        await interactiveService
            .triggerStateFeedback(interactive.StateType.success);
        _animationController.reverse();
      }
    } else {
      if (widget.isLoading) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (widget.isLoading)
          AnimatedBuilder(
            animation: _fadeAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _fadeAnimation.value,
                child: Container(
                  color: Colors.black.withAlpha(26), // 0.1 opacity
                  child: Center(
                    child: widget.loadingWidget ?? _buildDefaultLoadingWidget(),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  Widget _buildDefaultLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 opacity
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CircularProgressIndicator(),
          if (widget.loadingText != null) ...[
            const SizedBox(height: 16),
            Text(
              widget.loadingText!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Enhanced skeleton loading widget with shimmer effect
class EnhancedSkeletonLoader extends StatelessWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final Color? baseColor;
  final Color? highlightColor;
  final Widget? child;

  const EnhancedSkeletonLoader({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    this.baseColor,
    this.highlightColor,
    this.child,
  });

  const EnhancedSkeletonLoader.rectangular({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
    this.baseColor,
    this.highlightColor,
  }) : child = null;

  const EnhancedSkeletonLoader.circular({
    super.key,
    required double size,
    this.baseColor,
    this.highlightColor,
  })  : width = size,
        height = size,
        borderRadius = null,
        child = null;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Shimmer.fromColors(
      baseColor: baseColor ?? (isDark ? Colors.grey[800]! : Colors.grey[300]!),
      highlightColor:
          highlightColor ?? (isDark ? Colors.grey[700]! : Colors.grey[100]!),
      child: child ??
          Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: borderRadius ??
                  (width == height
                      ? BorderRadius.circular(width! / 2)
                      : BorderRadius.circular(8)),
            ),
          ),
    );
  }
}

/// Enhanced skeleton card for experience loading
class EnhancedExperienceSkeletonCard extends StatelessWidget {
  const EnhancedExperienceSkeletonCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 opacity
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image skeleton
          EnhancedSkeletonLoader.rectangular(
            width: double.infinity,
            height: 200,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          SizedBox(height: 16),
          // Title skeleton
          EnhancedSkeletonLoader.rectangular(
            width: double.infinity,
            height: 20,
            borderRadius: BorderRadius.all(Radius.circular(4)),
          ),
          SizedBox(height: 8),
          // Subtitle skeleton
          EnhancedSkeletonLoader.rectangular(
            width: 200,
            height: 16,
            borderRadius: BorderRadius.all(Radius.circular(4)),
          ),
          SizedBox(height: 16),
          // Rating and price row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  EnhancedSkeletonLoader.circular(size: 16),
                  SizedBox(width: 8),
                  EnhancedSkeletonLoader.rectangular(
                    width: 60,
                    height: 16,
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                  ),
                ],
              ),
              EnhancedSkeletonLoader.rectangular(
                width: 80,
                height: 20,
                borderRadius: BorderRadius.all(Radius.circular(4)),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Enhanced list skeleton loader
class EnhancedListSkeletonLoader extends StatelessWidget {
  final int itemCount;
  final Widget Function(int index) itemBuilder;
  final EdgeInsetsGeometry? padding;

  const EnhancedListSkeletonLoader({
    super.key,
    this.itemCount = 5,
    required this.itemBuilder,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: padding,
      itemCount: itemCount,
      itemBuilder: (context, index) => itemBuilder(index),
    );
  }
}

/// Enhanced grid skeleton loader
class EnhancedGridSkeletonLoader extends StatelessWidget {
  final int itemCount;
  final Widget Function(int index) itemBuilder;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final EdgeInsetsGeometry? padding;

  const EnhancedGridSkeletonLoader({
    super.key,
    this.itemCount = 6,
    required this.itemBuilder,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = 16,
    this.mainAxisSpacing = 16,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
        childAspectRatio: 0.8,
      ),
      itemCount: itemCount,
      itemBuilder: (context, index) => itemBuilder(index),
    );
  }
}
