import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/interactive_features_service.dart' as interactive;

/// Enhanced error state widget with haptic feedback and recovery actions
class EnhancedErrorState extends ConsumerStatefulWidget {
  final String title;
  final String message;
  final IconData? icon;
  final VoidCallback? onRetry;
  final VoidCallback? onGoBack;
  final String? retryButtonText;
  final String? backButtonText;
  final bool enableHapticFeedback;
  final ErrorType errorType;
  final List<Widget>? additionalActions;

  const EnhancedErrorState({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.onRetry,
    this.onGoBack,
    this.retryButtonText,
    this.backButtonText,
    this.enableHapticFeedback = true,
    this.errorType = ErrorType.general,
    this.additionalActions,
  });

  @override
  ConsumerState<EnhancedErrorState> createState() => _EnhancedErrorStateState();
}

class _EnhancedErrorStateState extends ConsumerState<EnhancedErrorState>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.elasticOut),
    ));

    _animationController.forward();
    _triggerErrorFeedback();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _triggerErrorFeedback() async {
    if (widget.enableHapticFeedback) {
      final interactiveService = ref.read(interactive.interactiveFeaturesServiceProvider);
      await interactiveService.triggerStateFeedback(interactive.StateType.error);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Error icon with animation
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: _getErrorColor(colorScheme).withAlpha(26), // 0.1 opacity
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        widget.icon ?? _getErrorIcon(),
                        size: 40,
                        color: _getErrorColor(colorScheme),
                      ),
                    ),
                    const SizedBox(height: 24),
                    
                    // Error title
                    Text(
                      widget.title,
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),
                    
                    // Error message
                    Text(
                      widget.message,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface.withAlpha(179), // 0.7 opacity
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 32),
                    
                    // Action buttons
                    Column(
                      children: [
                        if (widget.onRetry != null)
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: () async {
                                if (widget.enableHapticFeedback) {
                                  final interactiveService = ref.read(interactive.interactiveFeaturesServiceProvider);
                                  await interactiveService.triggerButtonFeedback(
                                    buttonType: interactive.ButtonType.primary,
                                    action: interactive.ButtonAction.press,
                                  );
                                }
                                widget.onRetry!();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: colorScheme.primary,
                                foregroundColor: colorScheme.onPrimary,
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Text(
                                widget.retryButtonText ?? 'Try Again',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        
                        if (widget.onRetry != null && widget.onGoBack != null)
                          const SizedBox(height: 12),
                        
                        if (widget.onGoBack != null)
                          SizedBox(
                            width: double.infinity,
                            child: OutlinedButton(
                              onPressed: () async {
                                if (widget.enableHapticFeedback) {
                                  final interactiveService = ref.read(interactive.interactiveFeaturesServiceProvider);
                                  await interactiveService.triggerButtonFeedback(
                                    buttonType: interactive.ButtonType.secondary,
                                    action: interactive.ButtonAction.press,
                                  );
                                }
                                widget.onGoBack!();
                              },
                              style: OutlinedButton.styleFrom(
                                foregroundColor: colorScheme.primary,
                                side: BorderSide(color: colorScheme.outline),
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Text(
                                widget.backButtonText ?? 'Go Back',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        
                        // Additional actions
                        if (widget.additionalActions != null) ...[
                          const SizedBox(height: 16),
                          ...widget.additionalActions!,
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getErrorColor(ColorScheme colorScheme) {
    switch (widget.errorType) {
      case ErrorType.network:
        return Colors.orange;
      case ErrorType.server:
        return Colors.red;
      case ErrorType.authentication:
        return Colors.amber;
      case ErrorType.permission:
        return Colors.deepOrange;
      case ErrorType.general:
      default:
        return colorScheme.error;
    }
  }

  IconData _getErrorIcon() {
    switch (widget.errorType) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.server:
        return Icons.error_outline;
      case ErrorType.authentication:
        return Icons.lock_outline;
      case ErrorType.permission:
        return Icons.block;
      case ErrorType.general:
      default:
        return Icons.warning_amber_outlined;
    }
  }
}

/// Error type enumeration
enum ErrorType {
  general,
  network,
  server,
  authentication,
  permission,
}

/// Enhanced network error widget
class EnhancedNetworkError extends StatelessWidget {
  final VoidCallback? onRetry;
  final bool enableHapticFeedback;

  const EnhancedNetworkError({
    super.key,
    this.onRetry,
    this.enableHapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedErrorState(
      title: 'No Internet Connection',
      message: 'Please check your internet connection and try again.',
      errorType: ErrorType.network,
      onRetry: onRetry,
      retryButtonText: 'Retry',
      enableHapticFeedback: enableHapticFeedback,
    );
  }
}

/// Enhanced server error widget
class EnhancedServerError extends StatelessWidget {
  final VoidCallback? onRetry;
  final VoidCallback? onGoBack;
  final bool enableHapticFeedback;

  const EnhancedServerError({
    super.key,
    this.onRetry,
    this.onGoBack,
    this.enableHapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedErrorState(
      title: 'Server Error',
      message: 'Something went wrong on our end. Please try again later.',
      errorType: ErrorType.server,
      onRetry: onRetry,
      onGoBack: onGoBack,
      retryButtonText: 'Try Again',
      backButtonText: 'Go Back',
      enableHapticFeedback: enableHapticFeedback,
    );
  }
}

/// Enhanced empty state widget
class EnhancedEmptyState extends ConsumerWidget {
  final String title;
  final String message;
  final IconData? icon;
  final VoidCallback? onAction;
  final String? actionButtonText;
  final bool enableHapticFeedback;

  const EnhancedEmptyState({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.onAction,
    this.actionButtonText,
    this.enableHapticFeedback = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Empty state icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon ?? Icons.inbox_outlined,
                size: 40,
                color: colorScheme.onSurface.withAlpha(153), // 0.6 opacity
              ),
            ),
            const SizedBox(height: 24),
            
            // Empty state title
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            
            // Empty state message
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withAlpha(179), // 0.7 opacity
              ),
              textAlign: TextAlign.center,
            ),
            
            if (onAction != null) ...[
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () async {
                  if (enableHapticFeedback) {
                    final interactiveService = ref.read(interactive.interactiveFeaturesServiceProvider);
                    await interactiveService.triggerButtonFeedback(
                      buttonType: interactive.ButtonType.primary,
                      action: interactive.ButtonAction.press,
                    );
                  }
                  onAction!();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  actionButtonText ?? 'Get Started',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
