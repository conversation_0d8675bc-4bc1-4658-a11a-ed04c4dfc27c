import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/interactive_features_service.dart'
    as interactive;

/// A wrapper widget that adds interactive features to child widgets
class InteractiveWrapper extends ConsumerWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onDoubleTap;
  final bool enableHapticFeedback;
  final interactive.ButtonType? buttonType;
  final interactive.GestureType? gestureType;
  final Duration animationDuration;
  final Curve animationCurve;

  const InteractiveWrapper({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.onDoubleTap,
    this.enableHapticFeedback = true,
    this.buttonType,
    this.gestureType,
    this.animationDuration = const Duration(milliseconds: 200),
    this.animationCurve = Curves.easeInOut,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final interactiveService =
        ref.watch(interactive.interactiveFeaturesServiceProvider);

    return AnimatedContainer(
      duration: animationDuration,
      curve: animationCurve,
      child: GestureDetector(
        onTap: onTap != null
            ? () async {
                if (enableHapticFeedback) {
                  if (buttonType != null) {
                    await interactiveService.triggerButtonFeedback(
                      buttonType: buttonType!,
                      action: interactive.ButtonAction.press,
                    );
                  } else {
                    await interactiveService.triggerHapticFeedback(
                      interactive.HapticFeedbackType.light,
                    );
                  }
                }
                onTap!();
              }
            : null,
        onLongPress: onLongPress != null
            ? () async {
                if (enableHapticFeedback) {
                  await interactiveService.triggerGestureFeedback(
                    interactive.GestureType.longPress,
                  );
                }
                onLongPress!();
              }
            : null,
        onDoubleTap: onDoubleTap != null
            ? () async {
                if (enableHapticFeedback) {
                  await interactiveService.triggerGestureFeedback(
                    interactive.GestureType.doubleTap,
                  );
                }
                onDoubleTap!();
              }
            : null,
        child: child,
      ),
    );
  }
}

/// Enhanced button with haptic feedback
class InteractiveButton extends ConsumerStatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final interactive.ButtonType buttonType;
  final IconData? icon;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;

  const InteractiveButton({
    super.key,
    required this.text,
    this.onPressed,
    this.buttonType = interactive.ButtonType.primary,
    this.icon,
    this.isLoading = false,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
  });

  @override
  ConsumerState<InteractiveButton> createState() => _InteractiveButtonState();
}

class _InteractiveButtonState extends ConsumerState<InteractiveButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final interactiveService =
        ref.watch(interactive.interactiveFeaturesServiceProvider);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: ElevatedButton(
            onPressed: widget.isLoading || widget.onPressed == null
                ? null
                : () async {
                    // Trigger press animation
                    await _animationController.forward();
                    await _animationController.reverse();

                    // Trigger haptic feedback
                    await interactiveService.triggerButtonFeedback(
                      buttonType: widget.buttonType,
                      action: interactive.ButtonAction.press,
                    );

                    // Execute callback
                    widget.onPressed!();
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.backgroundColor ??
                  _getButtonColor(theme, widget.buttonType),
              foregroundColor: widget.foregroundColor ??
                  _getButtonForegroundColor(theme, widget.buttonType),
              padding: widget.padding ??
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
              ),
            ),
            child: widget.isLoading
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        widget.foregroundColor ?? Colors.white,
                      ),
                    ),
                  )
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.icon != null) ...[
                        Icon(widget.icon, size: 18),
                        const SizedBox(width: 8),
                      ],
                      Text(widget.text),
                    ],
                  ),
          ),
        );
      },
    );
  }

  Color _getButtonColor(ThemeData theme, interactive.ButtonType buttonType) {
    switch (buttonType) {
      case interactive.ButtonType.primary:
        return theme.colorScheme.primary;
      case interactive.ButtonType.secondary:
        return theme.colorScheme.secondary;
      case interactive.ButtonType.destructive:
        return theme.colorScheme.error;
      case interactive.ButtonType.floating:
        return theme.colorScheme.primaryContainer;
    }
  }

  Color _getButtonForegroundColor(
      ThemeData theme, interactive.ButtonType buttonType) {
    switch (buttonType) {
      case interactive.ButtonType.primary:
        return theme.colorScheme.onPrimary;
      case interactive.ButtonType.secondary:
        return theme.colorScheme.onSecondary;
      case interactive.ButtonType.destructive:
        return theme.colorScheme.onError;
      case interactive.ButtonType.floating:
        return theme.colorScheme.onPrimaryContainer;
    }
  }
}
