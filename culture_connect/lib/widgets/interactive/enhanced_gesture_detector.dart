import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/interactive_features_service.dart'
    as interactive;

/// Enhanced gesture detector with haptic feedback and animations
class EnhancedGestureDetector extends ConsumerStatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onSwipeLeft;
  final VoidCallback? onSwipeRight;
  final VoidCallback? onSwipeUp;
  final VoidCallback? onSwipeDown;
  final bool enableHapticFeedback;
  final bool enableSwipeGestures;
  final double swipeThreshold;
  final Duration animationDuration;

  const EnhancedGestureDetector({
    super.key,
    required this.child,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.onSwipeLeft,
    this.onSwipeRight,
    this.onSwipeUp,
    this.onSwipeDown,
    this.enableHapticFeedback = true,
    this.enableSwipeGestures = true,
    this.swipeThreshold = 50.0,
    this.animationDuration = const Duration(milliseconds: 200),
  });

  @override
  ConsumerState<EnhancedGestureDetector> createState() =>
      _EnhancedGestureDetectorState();
}

class _EnhancedGestureDetectorState
    extends ConsumerState<EnhancedGestureDetector>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  Offset? _panStartPosition;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: widget.onTap != null ? _handleTap : null,
            onDoubleTap: widget.onDoubleTap != null ? _handleDoubleTap : null,
            onLongPress: widget.onLongPress != null ? _handleLongPress : null,
            onPanStart: widget.enableSwipeGestures ? _handlePanStart : null,
            onPanEnd: widget.enableSwipeGestures ? _handlePanEnd : null,
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            child: widget.child,
          ),
        );
      },
    );
  }

  Future<void> _handleTap() async {
    if (widget.enableHapticFeedback) {
      final interactiveService =
          ref.read(interactive.interactiveFeaturesServiceProvider);
      await interactiveService
          .triggerHapticFeedback(interactive.HapticFeedbackType.light);
    }
    widget.onTap?.call();
  }

  Future<void> _handleDoubleTap() async {
    if (widget.enableHapticFeedback) {
      final interactiveService =
          ref.read(interactive.interactiveFeaturesServiceProvider);
      await interactiveService
          .triggerGestureFeedback(interactive.GestureType.doubleTap);
    }
    widget.onDoubleTap?.call();
  }

  Future<void> _handleLongPress() async {
    if (widget.enableHapticFeedback) {
      final interactiveService =
          ref.read(interactive.interactiveFeaturesServiceProvider);
      await interactiveService
          .triggerGestureFeedback(interactive.GestureType.longPress);
    }
    widget.onLongPress?.call();
  }

  void _handlePanStart(DragStartDetails details) {
    _panStartPosition = details.localPosition;
  }

  Future<void> _handlePanEnd(DragEndDetails details) async {
    if (_panStartPosition == null) return;

    final velocity = details.velocity.pixelsPerSecond;
    final dx = velocity.dx.abs();
    final dy = velocity.dy.abs();

    // Determine swipe direction based on velocity
    if (dx > dy && dx > widget.swipeThreshold) {
      // Horizontal swipe
      if (velocity.dx > 0) {
        // Swipe right
        if (widget.onSwipeRight != null) {
          await _triggerSwipeFeedback();
          widget.onSwipeRight!();
        }
      } else {
        // Swipe left
        if (widget.onSwipeLeft != null) {
          await _triggerSwipeFeedback();
          widget.onSwipeLeft!();
        }
      }
    } else if (dy > dx && dy > widget.swipeThreshold) {
      // Vertical swipe
      if (velocity.dy > 0) {
        // Swipe down
        if (widget.onSwipeDown != null) {
          await _triggerSwipeFeedback();
          widget.onSwipeDown!();
        }
      } else {
        // Swipe up
        if (widget.onSwipeUp != null) {
          await _triggerSwipeFeedback();
          widget.onSwipeUp!();
        }
      }
    }

    _panStartPosition = null;
  }

  Future<void> _triggerSwipeFeedback() async {
    if (widget.enableHapticFeedback) {
      final interactiveService =
          ref.read(interactive.interactiveFeaturesServiceProvider);
      await interactiveService
          .triggerGestureFeedback(interactive.GestureType.swipe);
    }
  }
}

/// Enhanced pull-to-refresh widget with haptic feedback
class EnhancedRefreshIndicator extends ConsumerWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final bool enableHapticFeedback;
  final Color? color;
  final Color? backgroundColor;

  const EnhancedRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.enableHapticFeedback = true,
    this.color,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return RefreshIndicator(
      onRefresh: () async {
        if (enableHapticFeedback) {
          final interactiveService =
              ref.read(interactive.interactiveFeaturesServiceProvider);
          await interactiveService
              .triggerGestureFeedback(interactive.GestureType.pullToRefresh);
        }
        await onRefresh();
        if (enableHapticFeedback) {
          final interactiveService =
              ref.read(interactive.interactiveFeaturesServiceProvider);
          await interactiveService
              .triggerStateFeedback(interactive.StateType.success);
        }
      },
      color: color,
      backgroundColor: backgroundColor,
      child: child,
    );
  }
}

/// Swipeable card widget with gesture actions
class SwipeableCard extends ConsumerStatefulWidget {
  final Widget child;
  final VoidCallback? onSwipeLeft;
  final VoidCallback? onSwipeRight;
  final String? leftActionLabel;
  final String? rightActionLabel;
  final IconData? leftActionIcon;
  final IconData? rightActionIcon;
  final Color? leftActionColor;
  final Color? rightActionColor;
  final double swipeThreshold;

  const SwipeableCard({
    super.key,
    required this.child,
    this.onSwipeLeft,
    this.onSwipeRight,
    this.leftActionLabel,
    this.rightActionLabel,
    this.leftActionIcon,
    this.rightActionIcon,
    this.leftActionColor,
    this.rightActionColor,
    this.swipeThreshold = 0.3,
  });

  @override
  ConsumerState<SwipeableCard> createState() => _SwipeableCardState();
}

class _SwipeableCardState extends ConsumerState<SwipeableCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  double _dragExtent = 0.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(1.0, 0.0),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onHorizontalDragUpdate: _handleDragUpdate,
      onHorizontalDragEnd: _handleDragEnd,
      child: AnimatedBuilder(
        animation: _slideAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(_dragExtent, 0),
            child: widget.child,
          );
        },
      ),
    );
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    setState(() {
      _dragExtent += details.delta.dx;
      _dragExtent = _dragExtent.clamp(-200.0, 200.0);
    });
  }

  Future<void> _handleDragEnd(DragEndDetails details) async {
    final screenWidth = MediaQuery.of(context).size.width;
    final threshold = screenWidth * widget.swipeThreshold;

    if (_dragExtent.abs() > threshold) {
      final interactiveService =
          ref.read(interactive.interactiveFeaturesServiceProvider);
      await interactiveService
          .triggerGestureFeedback(interactive.GestureType.swipe);

      if (_dragExtent > 0 && widget.onSwipeRight != null) {
        widget.onSwipeRight!();
      } else if (_dragExtent < 0 && widget.onSwipeLeft != null) {
        widget.onSwipeLeft!();
      }
    }

    // Reset position
    setState(() {
      _dragExtent = 0.0;
    });
  }
}
