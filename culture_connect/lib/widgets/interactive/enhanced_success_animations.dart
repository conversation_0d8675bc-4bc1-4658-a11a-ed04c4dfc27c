import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:confetti/confetti.dart';
import 'package:culture_connect/services/interactive_features_service.dart' as interactive;

/// Enhanced success animation widget with confetti and haptic feedback
class EnhancedSuccessAnimation extends ConsumerStatefulWidget {
  final String title;
  final String message;
  final IconData? icon;
  final VoidCallback? onComplete;
  final Duration animationDuration;
  final bool showConfetti;
  final bool enableHapticFeedback;
  final SuccessType successType;
  final Widget? customContent;

  const EnhancedSuccessAnimation({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.onComplete,
    this.animationDuration = const Duration(milliseconds: 2000),
    this.showConfetti = true,
    this.enableHapticFeedback = true,
    this.successType = SuccessType.general,
    this.customContent,
  });

  @override
  ConsumerState<EnhancedSuccessAnimation> createState() => _EnhancedSuccessAnimationState();
}

class _EnhancedSuccessAnimationState extends ConsumerState<EnhancedSuccessAnimation>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late ConfettiController _confettiController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _confettiController = ConfettiController(
      duration: const Duration(seconds: 3),
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    _startAnimation();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  Future<void> _startAnimation() async {
    // Trigger haptic feedback
    if (widget.enableHapticFeedback) {
      final interactiveService = ref.read(interactive.interactiveFeaturesServiceProvider);
      await interactiveService.triggerStateFeedback(interactive.StateType.success);
    }

    // Start animations with staggered timing
    _scaleController.forward();
    await Future.delayed(const Duration(milliseconds: 200));
    _fadeController.forward();
    await Future.delayed(const Duration(milliseconds: 100));
    _slideController.forward();

    // Start confetti
    if (widget.showConfetti) {
      await Future.delayed(const Duration(milliseconds: 300));
      _confettiController.play();
    }

    // Complete callback after animation duration
    Future.delayed(widget.animationDuration, () {
      if (mounted) {
        widget.onComplete?.call();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: Colors.black.withAlpha(128), // 0.5 opacity
      body: Stack(
        children: [
          // Confetti overlay
          if (widget.showConfetti)
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirection: 1.5708, // radians for downward
                particleDrag: 0.05,
                emissionFrequency: 0.05,
                numberOfParticles: 50,
                gravity: 0.05,
                shouldLoop: false,
                colors: [
                  _getSuccessColor(colorScheme),
                  colorScheme.primary,
                  colorScheme.secondary,
                  Colors.amber,
                  Colors.green,
                ],
              ),
            ),

          // Success content
          Center(
            child: AnimatedBuilder(
              animation: Listenable.merge([_scaleController, _fadeController, _slideController]),
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: ScaleTransition(
                      scale: _scaleAnimation,
                      child: Container(
                        margin: const EdgeInsets.all(32),
                        padding: const EdgeInsets.all(32),
                        decoration: BoxDecoration(
                          color: colorScheme.surface,
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(51), // 0.2 opacity
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: widget.customContent ?? Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Success icon with animation
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: _getSuccessColor(colorScheme).withAlpha(26), // 0.1 opacity
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                widget.icon ?? _getSuccessIcon(),
                                size: 40,
                                color: _getSuccessColor(colorScheme),
                              ),
                            ),
                            const SizedBox(height: 24),
                            
                            // Success title
                            Text(
                              widget.title,
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: colorScheme.onSurface,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 12),
                            
                            // Success message
                            Text(
                              widget.message,
                              style: theme.textTheme.bodyLarge?.copyWith(
                                color: colorScheme.onSurface.withAlpha(179), // 0.7 opacity
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Color _getSuccessColor(ColorScheme colorScheme) {
    switch (widget.successType) {
      case SuccessType.booking:
        return Colors.green;
      case SuccessType.payment:
        return Colors.blue;
      case SuccessType.profile:
        return Colors.purple;
      case SuccessType.achievement:
        return Colors.amber;
      case SuccessType.general:
      default:
        return colorScheme.primary;
    }
  }

  IconData _getSuccessIcon() {
    switch (widget.successType) {
      case SuccessType.booking:
        return Icons.event_available;
      case SuccessType.payment:
        return Icons.payment;
      case SuccessType.profile:
        return Icons.person_outline;
      case SuccessType.achievement:
        return Icons.emoji_events;
      case SuccessType.general:
      default:
        return Icons.check_circle_outline;
    }
  }
}

/// Success type enumeration
enum SuccessType {
  general,
  booking,
  payment,
  profile,
  achievement,
}

/// Enhanced success dialog
class EnhancedSuccessDialog extends StatelessWidget {
  final String title;
  final String message;
  final IconData? icon;
  final VoidCallback? onConfirm;
  final String? confirmButtonText;
  final SuccessType successType;
  final bool enableHapticFeedback;

  const EnhancedSuccessDialog({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.onConfirm,
    this.confirmButtonText,
    this.successType = SuccessType.general,
    this.enableHapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: EnhancedSuccessAnimation(
        title: title,
        message: message,
        icon: icon,
        successType: successType,
        enableHapticFeedback: enableHapticFeedback,
        showConfetti: false,
        customContent: _buildDialogContent(context),
        onComplete: onConfirm,
      ),
    );
  }

  Widget _buildDialogContent(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Success icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: _getSuccessColor(colorScheme).withAlpha(26), // 0.1 opacity
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon ?? _getSuccessIcon(),
              size: 30,
              color: _getSuccessColor(colorScheme),
            ),
          ),
          const SizedBox(height: 20),
          
          // Title
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          
          // Message
          Text(
            message,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withAlpha(179), // 0.7 opacity
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          
          // Confirm button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onConfirm?.call();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: _getSuccessColor(colorScheme),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                confirmButtonText ?? 'Continue',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getSuccessColor(ColorScheme colorScheme) {
    switch (successType) {
      case SuccessType.booking:
        return Colors.green;
      case SuccessType.payment:
        return Colors.blue;
      case SuccessType.profile:
        return Colors.purple;
      case SuccessType.achievement:
        return Colors.amber;
      case SuccessType.general:
      default:
        return colorScheme.primary;
    }
  }

  IconData _getSuccessIcon() {
    switch (successType) {
      case SuccessType.booking:
        return Icons.event_available;
      case SuccessType.payment:
        return Icons.payment;
      case SuccessType.profile:
        return Icons.person_outline;
      case SuccessType.achievement:
        return Icons.emoji_events;
      case SuccessType.general:
      default:
        return Icons.check_circle_outline;
    }
  }
}
