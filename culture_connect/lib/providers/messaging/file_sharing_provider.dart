import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/messaging/file_message_model.dart';
import 'package:culture_connect/services/messaging/file_sharing_service.dart';

/// Provider for file sharing service
final fileSharingServiceProvider = Provider<FileSharingService>((ref) {
  return FileSharingService();
});

/// State for file upload
class FileUploadState {
  final bool isUploading;
  final double progress;
  final String? error;
  final FileMessageModel? uploadedFile;

  const FileUploadState({
    this.isUploading = false,
    this.progress = 0.0,
    this.error,
    this.uploadedFile,
  });

  FileUploadState copyWith({
    bool? isUploading,
    double? progress,
    String? error,
    FileMessageModel? uploadedFile,
  }) {
    return FileUploadState(
      isUploading: isUploading ?? this.isUploading,
      progress: progress ?? this.progress,
      error: error,
      uploadedFile: uploadedFile ?? this.uploadedFile,
    );
  }
}

/// State notifier for file upload
class FileUploadNotifier extends StateNotifier<FileUploadState> {
  final FileSharingService _fileSharingService;

  FileUploadNotifier(this._fileSharingService) : super(const FileUploadState());

  /// Upload a file
  Future<FileMessageModel?> uploadFile({
    required File file,
    required String messageId,
    required FilePermissions permissions,
    bool compressFile = true,
  }) async {
    state = state.copyWith(isUploading: true, progress: 0.0, error: null);

    try {
      final uploadedFile = await _fileSharingService.uploadFile(
        file: file,
        messageId: messageId,
        permissions: permissions,
        compressFile: compressFile,
        onProgress: (progress) {
          state = state.copyWith(progress: progress);
        },
      );

      if (uploadedFile != null) {
        state = state.copyWith(
          isUploading: false,
          progress: 1.0,
          uploadedFile: uploadedFile,
        );
        return uploadedFile;
      } else {
        state = state.copyWith(
          isUploading: false,
          error: 'Failed to upload file',
        );
        return null;
      }
    } catch (e) {
      state = state.copyWith(
        isUploading: false,
        error: e.toString(),
      );
      return null;
    }
  }

  /// Reset upload state
  void reset() {
    state = const FileUploadState();
  }
}

/// Provider for file upload state
final fileUploadProvider = StateNotifierProvider<FileUploadNotifier, FileUploadState>((ref) {
  final service = ref.watch(fileSharingServiceProvider);
  return FileUploadNotifier(service);
});

/// State for file download
class FileDownloadState {
  final Map<String, bool> isDownloading;
  final Map<String, double> progress;
  final Map<String, String> errors;
  final Map<String, String> downloadedPaths;

  const FileDownloadState({
    this.isDownloading = const {},
    this.progress = const {},
    this.errors = const {},
    this.downloadedPaths = const {},
  });

  FileDownloadState copyWith({
    Map<String, bool>? isDownloading,
    Map<String, double>? progress,
    Map<String, String>? errors,
    Map<String, String>? downloadedPaths,
  }) {
    return FileDownloadState(
      isDownloading: isDownloading ?? this.isDownloading,
      progress: progress ?? this.progress,
      errors: errors ?? this.errors,
      downloadedPaths: downloadedPaths ?? this.downloadedPaths,
    );
  }

  /// Check if file is downloading
  bool isFileDownloading(String fileId) => isDownloading[fileId] ?? false;

  /// Get download progress for file
  double getDownloadProgress(String fileId) => progress[fileId] ?? 0.0;

  /// Get download error for file
  String? getDownloadError(String fileId) => errors[fileId];

  /// Get downloaded path for file
  String? getDownloadedPath(String fileId) => downloadedPaths[fileId];
}

/// State notifier for file downloads
class FileDownloadNotifier extends StateNotifier<FileDownloadState> {
  final FileSharingService _fileSharingService;

  FileDownloadNotifier(this._fileSharingService) : super(const FileDownloadState());

  /// Download a file
  Future<String?> downloadFile(FileMessageModel fileMessage) async {
    final fileId = fileMessage.id;
    
    // Update downloading state
    state = state.copyWith(
      isDownloading: {...state.isDownloading, fileId: true},
      progress: {...state.progress, fileId: 0.0},
      errors: {...state.errors}..remove(fileId),
    );

    try {
      final downloadedPath = await _fileSharingService.downloadFile(
        fileMessage: fileMessage,
        onProgress: (progress) {
          state = state.copyWith(
            progress: {...state.progress, fileId: progress},
          );
        },
      );

      if (downloadedPath != null) {
        state = state.copyWith(
          isDownloading: {...state.isDownloading, fileId: false},
          progress: {...state.progress, fileId: 1.0},
          downloadedPaths: {...state.downloadedPaths, fileId: downloadedPath},
        );
        return downloadedPath;
      } else {
        state = state.copyWith(
          isDownloading: {...state.isDownloading, fileId: false},
          errors: {...state.errors, fileId: 'Failed to download file'},
        );
        return null;
      }
    } catch (e) {
      state = state.copyWith(
        isDownloading: {...state.isDownloading, fileId: false},
        errors: {...state.errors, fileId: e.toString()},
      );
      return null;
    }
  }

  /// Clear download state for file
  void clearFileState(String fileId) {
    state = state.copyWith(
      isDownloading: {...state.isDownloading}..remove(fileId),
      progress: {...state.progress}..remove(fileId),
      errors: {...state.errors}..remove(fileId),
      downloadedPaths: {...state.downloadedPaths}..remove(fileId),
    );
  }

  /// Clear all download states
  void clearAll() {
    state = const FileDownloadState();
  }
}

/// Provider for file download state
final fileDownloadProvider = StateNotifierProvider<FileDownloadNotifier, FileDownloadState>((ref) {
  final service = ref.watch(fileSharingServiceProvider);
  return FileDownloadNotifier(service);
});

/// Provider for file messages by message ID
final fileMessagesProvider = FutureProvider.family<List<FileMessageModel>, String>((ref, messageId) async {
  final service = ref.watch(fileSharingServiceProvider);
  return service.getFileMessagesForMessage(messageId);
});

/// Provider for single file message by ID
final fileMessageProvider = FutureProvider.family<FileMessageModel?, String>((ref, fileId) async {
  final service = ref.watch(fileSharingServiceProvider);
  return service.getFileMessage(fileId);
});

/// State for file management
class FileManagementState {
  final bool isDeleting;
  final bool isUpdatingPermissions;
  final String? error;
  final String? successMessage;

  const FileManagementState({
    this.isDeleting = false,
    this.isUpdatingPermissions = false,
    this.error,
    this.successMessage,
  });

  FileManagementState copyWith({
    bool? isDeleting,
    bool? isUpdatingPermissions,
    String? error,
    String? successMessage,
  }) {
    return FileManagementState(
      isDeleting: isDeleting ?? this.isDeleting,
      isUpdatingPermissions: isUpdatingPermissions ?? this.isUpdatingPermissions,
      error: error,
      successMessage: successMessage,
    );
  }
}

/// State notifier for file management
class FileManagementNotifier extends StateNotifier<FileManagementState> {
  final FileSharingService _fileSharingService;

  FileManagementNotifier(this._fileSharingService) : super(const FileManagementState());

  /// Delete a file message
  Future<bool> deleteFile(String fileId) async {
    state = state.copyWith(isDeleting: true, error: null);

    try {
      final success = await _fileSharingService.deleteFileMessage(fileId);
      
      if (success) {
        state = state.copyWith(
          isDeleting: false,
          successMessage: 'File deleted successfully',
        );
      } else {
        state = state.copyWith(
          isDeleting: false,
          error: 'Failed to delete file',
        );
      }
      
      return success;
    } catch (e) {
      state = state.copyWith(
        isDeleting: false,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Update file permissions
  Future<bool> updatePermissions(String fileId, FilePermissions permissions) async {
    state = state.copyWith(isUpdatingPermissions: true, error: null);

    try {
      final success = await _fileSharingService.updateFilePermissions(fileId, permissions);
      
      if (success) {
        state = state.copyWith(
          isUpdatingPermissions: false,
          successMessage: 'Permissions updated successfully',
        );
      } else {
        state = state.copyWith(
          isUpdatingPermissions: false,
          error: 'Failed to update permissions',
        );
      }
      
      return success;
    } catch (e) {
      state = state.copyWith(
        isUpdatingPermissions: false,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Clear state
  void clearState() {
    state = const FileManagementState();
  }
}

/// Provider for file management state
final fileManagementProvider = StateNotifierProvider<FileManagementNotifier, FileManagementState>((ref) {
  final service = ref.watch(fileSharingServiceProvider);
  return FileManagementNotifier(service);
});
