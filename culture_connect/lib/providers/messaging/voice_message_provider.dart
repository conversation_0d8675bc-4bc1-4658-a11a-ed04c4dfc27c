import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/messaging/voice_message_model.dart';
import 'package:culture_connect/services/messaging/voice_message_service.dart';

/// Provider for voice message service
final voiceMessageServiceProvider = Provider<VoiceMessageService>((ref) {
  return VoiceMessageService();
});

/// Provider for voice recording state
final voiceRecordingStateProvider =
    StateNotifierProvider<VoiceRecordingNotifier, VoiceRecordingState>((ref) {
  final service = ref.watch(voiceMessageServiceProvider);
  return VoiceRecordingNotifier(service);
});

/// Provider for voice playback state
final voicePlaybackStateProvider = StreamProvider<VoicePlaybackState>((ref) {
  final service = ref.watch(voiceMessageServiceProvider);
  return service.playbackStateStream;
});

/// Provider for recording duration
final recordingDurationProvider = StreamProvider<Duration>((ref) {
  final service = ref.watch(voiceMessageServiceProvider);
  return service.recordingDurationStream;
});

/// Provider for amplitude data
final amplitudeProvider = StreamProvider<List<double>>((ref) {
  final service = ref.watch(voiceMessageServiceProvider);
  return service.amplitudeStream;
});

/// Voice recording state
class VoiceRecordingState {
  final bool isRecording;
  final bool isInitialized;
  final Duration duration;
  final String? recordingPath;
  final String? error;
  final List<double> waveform;

  const VoiceRecordingState({
    this.isRecording = false,
    this.isInitialized = false,
    this.duration = Duration.zero,
    this.recordingPath,
    this.error,
    this.waveform = const [],
  });

  VoiceRecordingState copyWith({
    bool? isRecording,
    bool? isInitialized,
    Duration? duration,
    String? recordingPath,
    String? error,
    List<double>? waveform,
  }) {
    return VoiceRecordingState(
      isRecording: isRecording ?? this.isRecording,
      isInitialized: isInitialized ?? this.isInitialized,
      duration: duration ?? this.duration,
      recordingPath: recordingPath ?? this.recordingPath,
      error: error ?? this.error,
      waveform: waveform ?? this.waveform,
    );
  }
}

/// Voice recording state notifier
class VoiceRecordingNotifier extends StateNotifier<VoiceRecordingState> {
  final VoiceMessageService _service;

  VoiceRecordingNotifier(this._service) : super(const VoiceRecordingState()) {
    _initialize();
  }

  /// Initialize the service
  Future<void> _initialize() async {
    final initialized = await _service.initialize();
    state = state.copyWith(
      isInitialized: initialized,
      error: initialized ? null : 'Failed to initialize voice recording',
    );
  }

  /// Start recording
  Future<void> startRecording({
    VoiceRecordingConfig config = const VoiceRecordingConfig(),
  }) async {
    if (state.isRecording) return;

    try {
      final path = await _service.startRecording(config: config);
      if (path != null) {
        state = state.copyWith(
          isRecording: true,
          recordingPath: path,
          error: null,
          duration: Duration.zero,
        );
      } else {
        state = state.copyWith(
          error: 'Failed to start recording',
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Recording error: $e',
      );
    }
  }

  /// Stop recording
  Future<String?> stopRecording() async {
    if (!state.isRecording) return null;

    try {
      final path = await _service.stopRecording();
      state = state.copyWith(
        isRecording: false,
        recordingPath: path,
      );
      return path;
    } catch (e) {
      state = state.copyWith(
        isRecording: false,
        error: 'Failed to stop recording: $e',
      );
      return null;
    }
  }

  /// Update recording duration
  void updateDuration(Duration duration) {
    state = state.copyWith(duration: duration);
  }

  /// Update waveform data
  void updateWaveform(List<double> waveform) {
    state = state.copyWith(waveform: [...state.waveform, ...waveform]);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Set error
  void setError(String error) {
    state = state.copyWith(error: error);
  }

  /// Reset state
  void reset() {
    state = const VoiceRecordingState();
  }
}

/// Provider for voice message playback notifier
final voicePlaybackNotifierProvider =
    StateNotifierProvider<VoicePlaybackNotifier, VoicePlaybackNotifierState>(
        (ref) {
  final service = ref.watch(voiceMessageServiceProvider);
  return VoicePlaybackNotifier(service);
});

/// Voice playback notifier state
class VoicePlaybackNotifierState {
  final String? currentMessageId;
  final bool isPlaying;
  final bool isLoading;
  final Duration position;
  final Duration duration;
  final double playbackSpeed;
  final String? error;

  const VoicePlaybackNotifierState({
    this.currentMessageId,
    this.isPlaying = false,
    this.isLoading = false,
    this.position = Duration.zero,
    this.duration = Duration.zero,
    this.playbackSpeed = 1.0,
    this.error,
  });

  VoicePlaybackNotifierState copyWith({
    String? currentMessageId,
    bool? isPlaying,
    bool? isLoading,
    Duration? position,
    Duration? duration,
    double? playbackSpeed,
    String? error,
  }) {
    return VoicePlaybackNotifierState(
      currentMessageId: currentMessageId ?? this.currentMessageId,
      isPlaying: isPlaying ?? this.isPlaying,
      isLoading: isLoading ?? this.isLoading,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
      error: error ?? this.error,
    );
  }
}

/// Voice playback notifier
class VoicePlaybackNotifier extends StateNotifier<VoicePlaybackNotifierState> {
  final VoiceMessageService _service;

  VoicePlaybackNotifier(this._service)
      : super(const VoicePlaybackNotifierState()) {
    // Listen to playback state changes
    _service.playbackStateStream.listen((playbackState) {
      state = state.copyWith(
        currentMessageId: playbackState.currentMessageId,
        isPlaying: playbackState.isPlaying,
        isLoading: playbackState.isLoading,
        position: playbackState.position,
        duration: playbackState.duration,
        playbackSpeed: playbackState.playbackSpeed,
      );
    });
  }

  /// Play voice message
  Future<void> playVoiceMessage(VoiceMessageModel voiceMessage) async {
    try {
      state = state.copyWith(
        isLoading: true,
        error: null,
      );

      final success = await _service.playVoiceMessage(voiceMessage);
      if (!success) {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to play voice message',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Playback error: $e',
      );
    }
  }

  /// Pause voice message
  Future<void> pauseVoiceMessage() async {
    try {
      await _service.pauseVoiceMessage();
    } catch (e) {
      state = state.copyWith(error: 'Failed to pause: $e');
    }
  }

  /// Resume voice message
  Future<void> resumeVoiceMessage() async {
    try {
      await _service.resumeVoiceMessage();
    } catch (e) {
      state = state.copyWith(error: 'Failed to resume: $e');
    }
  }

  /// Stop voice message
  Future<void> stopVoiceMessage() async {
    try {
      await _service.stopVoiceMessage();
      state = const VoicePlaybackNotifierState();
    } catch (e) {
      state = state.copyWith(error: 'Failed to stop: $e');
    }
  }

  /// Seek to position
  Future<void> seekToPosition(Duration position) async {
    try {
      await _service.seekToPosition(position);
    } catch (e) {
      state = state.copyWith(error: 'Failed to seek: $e');
    }
  }

  /// Set playback speed
  Future<void> setPlaybackSpeed(double speed) async {
    try {
      await _service.setPlaybackSpeed(speed);
      state = state.copyWith(playbackSpeed: speed);
    } catch (e) {
      state = state.copyWith(error: 'Failed to set speed: $e');
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for voice message creation
final voiceMessageCreationProvider =
    FutureProvider.family<VoiceMessageModel?, VoiceMessageCreationParams>(
        (ref, params) async {
  final service = ref.watch(voiceMessageServiceProvider);
  return await service.createVoiceMessage(
    messageId: params.messageId,
    filePath: params.filePath,
    metadata: params.metadata,
  );
});

/// Parameters for voice message creation
class VoiceMessageCreationParams {
  final String messageId;
  final String filePath;
  final Map<String, dynamic>? metadata;

  const VoiceMessageCreationParams({
    required this.messageId,
    required this.filePath,
    this.metadata,
  });
}

/// Provider for voice message by ID
final voiceMessageProvider = FutureProvider.family<VoiceMessageModel?, String>(
    (ref, voiceMessageId) async {
  final service = ref.watch(voiceMessageServiceProvider);
  return await service.getVoiceMessage(voiceMessageId);
});
