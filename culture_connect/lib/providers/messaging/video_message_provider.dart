import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:camera/camera.dart';
import 'package:culture_connect/models/messaging/video_message_model.dart';
import 'package:culture_connect/services/messaging/video_message_service.dart';

/// Provider for video message service
final videoMessageServiceProvider = Provider<VideoMessageService>((ref) {
  return VideoMessageService();
});

/// Provider for video recording state
final videoRecordingStateProvider =
    StateNotifierProvider<VideoRecordingNotifier, VideoRecordingState>((ref) {
  final service = ref.watch(videoMessageServiceProvider);
  return VideoRecordingNotifier(service);
});

/// Provider for video playback state
final videoPlaybackStateProvider = StreamProvider<VideoPlaybackState>((ref) {
  final service = ref.watch(videoMessageServiceProvider);
  return service.playbackStateStream;
});

/// Provider for recording duration
final videoRecordingDurationProvider = StreamProvider<Duration>((ref) {
  final service = ref.watch(videoMessageServiceProvider);
  return service.recordingDurationStream;
});

/// Provider for recording state stream
final videoRecordingStreamProvider = StreamProvider<bool>((ref) {
  final service = ref.watch(videoMessageServiceProvider);
  return service.recordingStateStream;
});

/// Video recording state
class VideoRecordingState {
  final bool isRecording;
  final bool isInitialized;
  final Duration duration;
  final String? recordingPath;
  final String? error;
  final CameraController? cameraController;

  const VideoRecordingState({
    this.isRecording = false,
    this.isInitialized = false,
    this.duration = Duration.zero,
    this.recordingPath,
    this.error,
    this.cameraController,
  });

  VideoRecordingState copyWith({
    bool? isRecording,
    bool? isInitialized,
    Duration? duration,
    String? recordingPath,
    String? error,
    CameraController? cameraController,
  }) {
    return VideoRecordingState(
      isRecording: isRecording ?? this.isRecording,
      isInitialized: isInitialized ?? this.isInitialized,
      duration: duration ?? this.duration,
      recordingPath: recordingPath ?? this.recordingPath,
      error: error ?? this.error,
      cameraController: cameraController ?? this.cameraController,
    );
  }
}

/// Video recording state notifier
class VideoRecordingNotifier extends StateNotifier<VideoRecordingState> {
  final VideoMessageService _service;

  VideoRecordingNotifier(this._service) : super(const VideoRecordingState()) {
    _initialize();
  }

  /// Initialize the service
  Future<void> _initialize() async {
    final initialized = await _service.initialize();
    state = state.copyWith(
      isInitialized: initialized,
      cameraController: _service.cameraController,
      error: initialized ? null : 'Failed to initialize video recording',
    );
  }

  /// Start recording
  Future<void> startRecording({
    VideoRecordingConfig config = const VideoRecordingConfig(),
  }) async {
    if (state.isRecording) return;

    try {
      final path = await _service.startRecording(config: config);
      if (path != null) {
        state = state.copyWith(
          isRecording: true,
          recordingPath: path,
          error: null,
          duration: Duration.zero,
        );
      } else {
        state = state.copyWith(
          error: 'Failed to start recording',
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Recording error: $e',
      );
    }
  }

  /// Stop recording
  Future<String?> stopRecording() async {
    if (!state.isRecording) return null;

    try {
      final path = await _service.stopRecording();
      state = state.copyWith(
        isRecording: false,
        recordingPath: path,
      );
      return path;
    } catch (e) {
      state = state.copyWith(
        isRecording: false,
        error: 'Failed to stop recording: $e',
      );
      return null;
    }
  }

  /// Update recording duration
  void updateDuration(Duration duration) {
    state = state.copyWith(duration: duration);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Set error
  void setError(String error) {
    state = state.copyWith(error: error);
  }

  /// Reset state
  void reset() {
    state = const VideoRecordingState();
  }
}

/// Provider for video message playback notifier
final videoPlaybackNotifierProvider =
    StateNotifierProvider<VideoPlaybackNotifier, VideoPlaybackNotifierState>(
        (ref) {
  final service = ref.watch(videoMessageServiceProvider);
  return VideoPlaybackNotifier(service);
});

/// Video playback notifier state
class VideoPlaybackNotifierState {
  final String? currentMessageId;
  final bool isPlaying;
  final bool isLoading;
  final Duration position;
  final Duration duration;
  final double volume;
  final String? error;

  const VideoPlaybackNotifierState({
    this.currentMessageId,
    this.isPlaying = false,
    this.isLoading = false,
    this.position = Duration.zero,
    this.duration = Duration.zero,
    this.volume = 1.0,
    this.error,
  });

  VideoPlaybackNotifierState copyWith({
    String? currentMessageId,
    bool? isPlaying,
    bool? isLoading,
    Duration? position,
    Duration? duration,
    double? volume,
    String? error,
  }) {
    return VideoPlaybackNotifierState(
      currentMessageId: currentMessageId ?? this.currentMessageId,
      isPlaying: isPlaying ?? this.isPlaying,
      isLoading: isLoading ?? this.isLoading,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      volume: volume ?? this.volume,
      error: error ?? this.error,
    );
  }
}

/// Video playback notifier
class VideoPlaybackNotifier extends StateNotifier<VideoPlaybackNotifierState> {
  final VideoMessageService _service;

  VideoPlaybackNotifier(this._service)
      : super(const VideoPlaybackNotifierState()) {
    // Listen to playback state changes
    _service.playbackStateStream.listen((playbackState) {
      state = state.copyWith(
        currentMessageId: playbackState.currentMessageId,
        isPlaying: playbackState.isPlaying,
        isLoading: playbackState.isLoading,
        position: playbackState.position,
        duration: playbackState.duration,
        volume: playbackState.volume,
      );
    });
  }

  /// Play video message
  Future<void> playVideoMessage(VideoMessageModel videoMessage) async {
    try {
      state = state.copyWith(
        isLoading: true,
        error: null,
      );

      final success = await _service.playVideoMessage(videoMessage);
      if (!success) {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to play video message',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Playback error: $e',
      );
    }
  }

  /// Pause video message
  Future<void> pauseVideoMessage() async {
    try {
      await _service.pauseVideoMessage();
    } catch (e) {
      state = state.copyWith(error: 'Failed to pause: $e');
    }
  }

  /// Resume video message
  Future<void> resumeVideoMessage() async {
    try {
      await _service.resumeVideoMessage();
    } catch (e) {
      state = state.copyWith(error: 'Failed to resume: $e');
    }
  }

  /// Stop video message
  Future<void> stopVideoMessage() async {
    try {
      await _service.stopVideoMessage();
      state = const VideoPlaybackNotifierState();
    } catch (e) {
      state = state.copyWith(error: 'Failed to stop: $e');
    }
  }

  /// Seek to position
  Future<void> seekToPosition(Duration position) async {
    try {
      await _service.seekToPosition(position);
    } catch (e) {
      state = state.copyWith(error: 'Failed to seek: $e');
    }
  }

  /// Set volume
  Future<void> setVolume(double volume) async {
    try {
      await _service.setVolume(volume);
      state = state.copyWith(volume: volume);
    } catch (e) {
      state = state.copyWith(error: 'Failed to set volume: $e');
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for video message creation
final videoMessageCreationProvider =
    FutureProvider.family<VideoMessageModel?, VideoMessageCreationParams>(
        (ref, params) async {
  final service = ref.watch(videoMessageServiceProvider);
  return await service.createVideoMessage(
    messageId: params.messageId,
    filePath: params.filePath,
    metadata: params.metadata,
  );
});

/// Parameters for video message creation
class VideoMessageCreationParams {
  final String messageId;
  final String filePath;
  final Map<String, dynamic>? metadata;

  const VideoMessageCreationParams({
    required this.messageId,
    required this.filePath,
    this.metadata,
  });
}

/// Provider for video message by ID
final videoMessageProvider = FutureProvider.family<VideoMessageModel?, String>(
    (ref, videoMessageId) async {
  final service = ref.watch(videoMessageServiceProvider);
  return await service.getVideoMessage(videoMessageId);
});
