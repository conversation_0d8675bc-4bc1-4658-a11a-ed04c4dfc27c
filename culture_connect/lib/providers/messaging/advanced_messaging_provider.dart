import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/messaging/message_thread_model.dart';
import 'package:culture_connect/models/messaging/message_edit_model.dart';
import 'package:culture_connect/models/messaging/message_schedule_model.dart';
import 'package:culture_connect/models/messaging/message_template_model.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/services/messaging/message_thread_service.dart';
import 'package:culture_connect/services/messaging/message_edit_service.dart';
import 'package:culture_connect/services/messaging/message_schedule_service.dart';
import 'package:culture_connect/services/messaging/message_template_service.dart';

/// Provider for MessageThreadService
final messageThreadServiceProvider = Provider<MessageThreadService>((ref) {
  return MessageThreadService();
});

/// Provider for MessageEditService
final messageEditServiceProvider = Provider<MessageEditService>((ref) {
  return MessageEditService();
});

/// Provider for MessageScheduleService
final messageScheduleServiceProvider = Provider<MessageScheduleService>((ref) {
  return MessageScheduleService();
});

/// Provider for MessageTemplateService
final messageTemplateServiceProvider = Provider<MessageTemplateService>((ref) {
  return MessageTemplateService();
});

/// State class for message threading
class MessageThreadState {
  final Map<String, MessageThreadModel> threads;
  final Map<String, List<MessageReplyModel>> threadReplies;
  final bool isLoading;
  final String? error;

  const MessageThreadState({
    this.threads = const {},
    this.threadReplies = const {},
    this.isLoading = false,
    this.error,
  });

  MessageThreadState copyWith({
    Map<String, MessageThreadModel>? threads,
    Map<String, List<MessageReplyModel>>? threadReplies,
    bool? isLoading,
    String? error,
  }) {
    return MessageThreadState(
      threads: threads ?? this.threads,
      threadReplies: threadReplies ?? this.threadReplies,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// Provider for message threading state
class MessageThreadNotifier extends StateNotifier<MessageThreadState> {
  final MessageThreadService _threadService;

  MessageThreadNotifier(this._threadService)
      : super(const MessageThreadState());

  /// Create a new thread
  Future<MessageThreadModel?> createThread({
    required String parentMessageId,
    required String chatId,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final thread = await _threadService.createThread(
        parentMessageId: parentMessageId,
        chatId: chatId,
      );

      if (thread != null) {
        final updatedThreads =
            Map<String, MessageThreadModel>.from(state.threads);
        updatedThreads[parentMessageId] = thread;

        state = state.copyWith(
          threads: updatedThreads,
          isLoading: false,
        );
      }

      return thread;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return null;
    }
  }

  /// Add reply to thread
  Future<MessageReplyModel?> addReply({
    required String threadId,
    required String parentMessageId,
    required String replyToMessageId,
    required String chatId,
    required String senderId,
    required String text,
    required MessageType type,
    String? mediaUrl,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final reply = await _threadService.addReply(
        threadId: threadId,
        parentMessageId: parentMessageId,
        replyToMessageId: replyToMessageId,
        chatId: chatId,
        senderId: senderId,
        text: text,
        type: type,
        mediaUrl: mediaUrl,
        metadata: metadata,
      );

      if (reply != null) {
        final updatedReplies =
            Map<String, List<MessageReplyModel>>.from(state.threadReplies);
        final currentReplies = updatedReplies[threadId] ?? [];
        updatedReplies[threadId] = [...currentReplies, reply];

        state = state.copyWith(threadReplies: updatedReplies);
      }

      return reply;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return null;
    }
  }

  /// Load thread for message
  Future<void> loadThread(String parentMessageId) async {
    if (state.threads.containsKey(parentMessageId)) return;

    try {
      final thread = await _threadService.getThread(parentMessageId);
      if (thread != null) {
        final updatedThreads =
            Map<String, MessageThreadModel>.from(state.threads);
        updatedThreads[parentMessageId] = thread;

        state = state.copyWith(threads: updatedThreads);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Load thread replies
  Future<void> loadThreadReplies(String threadId) async {
    if (state.threadReplies.containsKey(threadId)) return;

    try {
      final replies = await _threadService.getThreadReplies(threadId);
      final updatedReplies =
          Map<String, List<MessageReplyModel>>.from(state.threadReplies);
      updatedReplies[threadId] = replies;

      state = state.copyWith(threadReplies: updatedReplies);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Get thread for message
  MessageThreadModel? getThread(String parentMessageId) {
    return state.threads[parentMessageId];
  }

  /// Get replies for thread
  List<MessageReplyModel> getThreadReplies(String threadId) {
    return state.threadReplies[threadId] ?? [];
  }

  /// Check if message has thread
  bool hasThread(String parentMessageId) {
    return state.threads.containsKey(parentMessageId);
  }
}

final messageThreadProvider =
    StateNotifierProvider<MessageThreadNotifier, MessageThreadState>((ref) {
  final threadService = ref.watch(messageThreadServiceProvider);
  return MessageThreadNotifier(threadService);
});

/// State class for message editing
class MessageEditState {
  final Map<String, List<MessageEditModel>> editHistory;
  final Map<String, MessageDeletionModel> deletions;
  final bool isLoading;
  final String? error;

  const MessageEditState({
    this.editHistory = const {},
    this.deletions = const {},
    this.isLoading = false,
    this.error,
  });

  MessageEditState copyWith({
    Map<String, List<MessageEditModel>>? editHistory,
    Map<String, MessageDeletionModel>? deletions,
    bool? isLoading,
    String? error,
  }) {
    return MessageEditState(
      editHistory: editHistory ?? this.editHistory,
      deletions: deletions ?? this.deletions,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// Provider for message editing state
class MessageEditNotifier extends StateNotifier<MessageEditState> {
  final MessageEditService _editService;

  MessageEditNotifier(this._editService) : super(const MessageEditState());

  /// Edit a message
  Future<bool> editMessage({
    required String messageId,
    required String chatId,
    required String editorUserId,
    required String newText,
    String? editReason,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final success = await _editService.editMessage(
        messageId: messageId,
        chatId: chatId,
        editorUserId: editorUserId,
        newText: newText,
        editReason: editReason,
      );

      if (success) {
        // Refresh edit history
        await loadEditHistory(messageId);
      }

      state = state.copyWith(isLoading: false);
      return success;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Delete a message
  Future<bool> deleteMessage({
    required String messageId,
    required String chatId,
    required String deletedByUserId,
    MessageDeletionType deletionType = MessageDeletionType.soft,
    String? deletionReason,
    bool isRecoverable = true,
    Duration? recoverableFor,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final success = await _editService.deleteMessage(
        messageId: messageId,
        chatId: chatId,
        deletedByUserId: deletedByUserId,
        deletionType: deletionType,
        deletionReason: deletionReason,
        isRecoverable: isRecoverable,
        recoverableFor: recoverableFor,
      );

      if (success) {
        // Load deletion record
        await loadDeletionRecord(messageId);
      }

      state = state.copyWith(isLoading: false);
      return success;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Load edit history for message
  Future<void> loadEditHistory(String messageId) async {
    try {
      final history = await _editService.getEditHistory(messageId);
      final updatedHistory =
          Map<String, List<MessageEditModel>>.from(state.editHistory);
      updatedHistory[messageId] = history;

      state = state.copyWith(editHistory: updatedHistory);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Load deletion record for message
  Future<void> loadDeletionRecord(String messageId) async {
    try {
      final deletion = await _editService.getDeletionRecord(messageId);
      if (deletion != null) {
        final updatedDeletions =
            Map<String, MessageDeletionModel>.from(state.deletions);
        updatedDeletions[messageId] = deletion;

        state = state.copyWith(deletions: updatedDeletions);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Check if message can be edited
  Future<bool> canEditMessage(String messageId, String userId) async {
    return await _editService.canEditMessage(messageId, userId);
  }

  /// Check if message can be deleted
  Future<bool> canDeleteMessage(String messageId, String userId) async {
    return await _editService.canDeleteMessage(messageId, userId);
  }

  /// Get edit history for message
  List<MessageEditModel> getEditHistory(String messageId) {
    return state.editHistory[messageId] ?? [];
  }

  /// Get deletion record for message
  MessageDeletionModel? getDeletionRecord(String messageId) {
    return state.deletions[messageId];
  }
}

final messageEditProvider =
    StateNotifierProvider<MessageEditNotifier, MessageEditState>((ref) {
  final editService = ref.watch(messageEditServiceProvider);
  return MessageEditNotifier(editService);
});

/// State class for scheduled messages
class MessageScheduleState {
  final List<MessageScheduleModel> scheduledMessages;
  final bool isLoading;
  final String? error;

  const MessageScheduleState({
    this.scheduledMessages = const [],
    this.isLoading = false,
    this.error,
  });

  MessageScheduleState copyWith({
    List<MessageScheduleModel>? scheduledMessages,
    bool? isLoading,
    String? error,
  }) {
    return MessageScheduleState(
      scheduledMessages: scheduledMessages ?? this.scheduledMessages,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// Provider for message scheduling state
class MessageScheduleNotifier extends StateNotifier<MessageScheduleState> {
  final MessageScheduleService _scheduleService;

  MessageScheduleNotifier(this._scheduleService)
      : super(const MessageScheduleState());

  /// Schedule a message
  Future<MessageScheduleModel?> scheduleMessage({
    required String chatId,
    required String senderId,
    required String recipientId,
    required String text,
    required MessageType type,
    required DateTime scheduledFor,
    String? mediaUrl,
    Map<String, dynamic>? metadata,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final scheduledMessage = await _scheduleService.scheduleMessage(
        chatId: chatId,
        senderId: senderId,
        recipientId: recipientId,
        text: text,
        type: type,
        scheduledFor: scheduledFor,
        mediaUrl: mediaUrl,
        metadata: metadata,
      );

      if (scheduledMessage != null) {
        final updatedMessages = [...state.scheduledMessages, scheduledMessage];
        state = state.copyWith(
          scheduledMessages: updatedMessages,
          isLoading: false,
        );
      }

      return scheduledMessage;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return null;
    }
  }

  /// Load user scheduled messages
  Future<void> loadUserScheduledMessages(String userId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final messages = await _scheduleService.getUserScheduledMessages(userId);
      state = state.copyWith(
        scheduledMessages: messages,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Cancel scheduled message
  Future<bool> cancelScheduledMessage(String scheduleId,
      {required String userId}) async {
    try {
      final success = await _scheduleService.cancelScheduledMessage(scheduleId,
          userId: userId);
      if (success) {
        final updatedMessages = state.scheduledMessages
            .where((msg) => msg.id != scheduleId)
            .toList();
        state = state.copyWith(scheduledMessages: updatedMessages);
      }
      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Reschedule message
  Future<bool> rescheduleMessage({
    required String scheduleId,
    required DateTime newScheduledTime,
    required String userId,
  }) async {
    try {
      return await _scheduleService.rescheduleMessage(
        scheduleId: scheduleId,
        newScheduledTime: newScheduledTime,
        userId: userId,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }
}

final messageScheduleProvider =
    StateNotifierProvider<MessageScheduleNotifier, MessageScheduleState>((ref) {
  final scheduleService = ref.watch(messageScheduleServiceProvider);
  return MessageScheduleNotifier(scheduleService);
});

/// State class for message templates
class MessageTemplateState {
  final List<MessageTemplateModel> templates;
  final List<QuickReplyModel> quickReplies;
  final bool isLoading;
  final String? error;

  const MessageTemplateState({
    this.templates = const [],
    this.quickReplies = const [],
    this.isLoading = false,
    this.error,
  });

  MessageTemplateState copyWith({
    List<MessageTemplateModel>? templates,
    List<QuickReplyModel>? quickReplies,
    bool? isLoading,
    String? error,
  }) {
    return MessageTemplateState(
      templates: templates ?? this.templates,
      quickReplies: quickReplies ?? this.quickReplies,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// Provider for message templates state
class MessageTemplateNotifier extends StateNotifier<MessageTemplateState> {
  final MessageTemplateService _templateService;

  MessageTemplateNotifier(this._templateService)
      : super(const MessageTemplateState());

  /// Create a new template
  Future<MessageTemplateModel?> createTemplate({
    required String userId,
    required String name,
    required String content,
    String? description,
    required MessageTemplateCategory category,
    List<String> tags = const [],
    bool isPublic = false,
    Map<String, String>? variables,
    Map<String, dynamic>? metadata,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final template = await _templateService.createTemplate(
        userId: userId,
        name: name,
        content: content,
        description: description,
        category: category,
        tags: tags,
        isPublic: isPublic,
        variables: variables,
        metadata: metadata,
      );

      if (template != null) {
        final updatedTemplates = [...state.templates, template];
        state = state.copyWith(
          templates: updatedTemplates,
          isLoading: false,
        );
      }

      return template;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return null;
    }
  }

  /// Load user templates
  Future<void> loadUserTemplates(String userId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final templates = await _templateService.getUserTemplates(userId);
      state = state.copyWith(
        templates: templates,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Search templates
  Future<void> searchTemplates({
    required String userId,
    required String query,
    MessageTemplateCategory? category,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final templates = await _templateService.searchTemplates(
        userId: userId,
        query: query,
        category: category,
      );
      state = state.copyWith(
        templates: templates,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Use a template
  Future<void> useTemplate(String templateId) async {
    try {
      await _templateService.useTemplate(templateId);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Load quick replies
  void loadQuickReplies({
    MessageTemplateCategory? category,
    String? contextMessage,
  }) {
    try {
      final quickReplies = _templateService.getQuickReplies(
        category: category,
        contextMessage: contextMessage,
      );
      state = state.copyWith(quickReplies: quickReplies);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Get contextual quick replies
  void loadContextualQuickReplies(String messageContent) {
    try {
      final quickReplies =
          _templateService.getContextualQuickReplies(messageContent);
      state = state.copyWith(quickReplies: quickReplies);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Delete template
  Future<bool> deleteTemplate(String templateId) async {
    try {
      final success = await _templateService.deleteTemplate(templateId);
      if (success) {
        final updatedTemplates = state.templates
            .where((template) => template.id != templateId)
            .toList();
        state = state.copyWith(templates: updatedTemplates);
      }
      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }
}

final messageTemplateProvider =
    StateNotifierProvider<MessageTemplateNotifier, MessageTemplateState>((ref) {
  final templateService = ref.watch(messageTemplateServiceProvider);
  return MessageTemplateNotifier(templateService);
});
