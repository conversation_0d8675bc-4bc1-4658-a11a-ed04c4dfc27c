// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/services/flight/flight_status_notification_service.dart';
import 'package:culture_connect/services/flight/flight_management_service.dart';
import 'package:culture_connect/services/notification_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// Provider for flight status notification service
final flightStatusNotificationServiceProvider =
    Provider<FlightStatusNotificationService>((ref) {
  final flightService = ref.watch(flightManagementServiceProvider);
  final notificationService = NotificationService();
  final loggingService = LoggingService();
  final prefs = ref.watch(sharedPreferencesProvider);

  return FlightStatusNotificationService(
    flightService: flightService,
    notificationService: notificationService,
    loggingService: loggingService,
    prefs: prefs,
  );
});

/// Provider for initializing flight status notification service
final flightStatusNotificationInitProvider = FutureProvider<void>((ref) async {
  final service = ref.watch(flightStatusNotificationServiceProvider);
  await service.initialize();
});

/// Provider for tracking a specific flight
final trackFlightProvider =
    FutureProvider.family<void, TrackFlightParams>((ref, params) async {
  final service = ref.watch(flightStatusNotificationServiceProvider);

  await service.trackFlight(
    flightNumber: params.flightNumber,
    departureDate: params.departureDate,
    bookingReference: params.bookingReference,
    settings: params.settings,
  );
});

/// Provider for stopping flight tracking
final stopTrackingFlightProvider =
    FutureProvider.family<void, StopTrackingParams>((ref, params) async {
  final service = ref.watch(flightStatusNotificationServiceProvider);

  await service.stopTrackingFlight(
    params.flightNumber,
    params.departureDate,
  );
});

/// Provider for getting flight notification settings
final flightNotificationSettingsProvider =
    FutureProvider.family<FlightNotificationSettings?, FlightSettingsParams>(
        (ref, params) async {
  final service = ref.watch(flightStatusNotificationServiceProvider);

  return service.getFlightNotificationSettings(
    params.flightNumber,
    params.departureDate,
  );
});

/// Provider for updating flight notification settings
final updateFlightNotificationSettingsProvider =
    FutureProvider.family<void, UpdateSettingsParams>((ref, params) async {
  final service = ref.watch(flightStatusNotificationServiceProvider);

  await service.updateFlightNotificationSettings(
    flightNumber: params.flightNumber,
    departureDate: params.departureDate,
    settings: params.settings,
  );
});

/// Parameters for tracking a flight
class TrackFlightParams {
  final String flightNumber;
  final DateTime departureDate;
  final String bookingReference;
  final FlightNotificationSettings? settings;

  const TrackFlightParams({
    required this.flightNumber,
    required this.departureDate,
    required this.bookingReference,
    this.settings,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TrackFlightParams &&
          runtimeType == other.runtimeType &&
          flightNumber == other.flightNumber &&
          departureDate == other.departureDate &&
          bookingReference == other.bookingReference &&
          settings == other.settings;

  @override
  int get hashCode =>
      flightNumber.hashCode ^
      departureDate.hashCode ^
      bookingReference.hashCode ^
      settings.hashCode;
}

/// Parameters for stopping flight tracking
class StopTrackingParams {
  final String flightNumber;
  final DateTime departureDate;

  const StopTrackingParams({
    required this.flightNumber,
    required this.departureDate,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StopTrackingParams &&
          runtimeType == other.runtimeType &&
          flightNumber == other.flightNumber &&
          departureDate == other.departureDate;

  @override
  int get hashCode => flightNumber.hashCode ^ departureDate.hashCode;
}

/// Parameters for flight settings
class FlightSettingsParams {
  final String flightNumber;
  final DateTime departureDate;

  const FlightSettingsParams({
    required this.flightNumber,
    required this.departureDate,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FlightSettingsParams &&
          runtimeType == other.runtimeType &&
          flightNumber == other.flightNumber &&
          departureDate == other.departureDate;

  @override
  int get hashCode => flightNumber.hashCode ^ departureDate.hashCode;
}

/// Parameters for updating settings
class UpdateSettingsParams {
  final String flightNumber;
  final DateTime departureDate;
  final FlightNotificationSettings settings;

  const UpdateSettingsParams({
    required this.flightNumber,
    required this.departureDate,
    required this.settings,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UpdateSettingsParams &&
          runtimeType == other.runtimeType &&
          flightNumber == other.flightNumber &&
          departureDate == other.departureDate &&
          settings == other.settings;

  @override
  int get hashCode =>
      flightNumber.hashCode ^ departureDate.hashCode ^ settings.hashCode;
}
