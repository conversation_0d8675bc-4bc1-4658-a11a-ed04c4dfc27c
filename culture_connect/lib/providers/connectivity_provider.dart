import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/connectivity_service.dart';
import 'package:culture_connect/utils/connectivity_service.dart' as utils;

/// Provider for the connectivity service
final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  return ConnectivityService();
});

/// Provider for the utils connectivity service
final utilsConnectivityServiceProvider = Provider<utils.ConnectivityService>((ref) {
  return utils.ConnectivityService();
});

/// Provider for current connectivity status
final connectivityProvider = StreamProvider<bool>((ref) {
  final service = ref.watch(utilsConnectivityServiceProvider);
  return service.connectivityStream;
});

/// Provider for current connectivity status as a synchronous value
final isConnectedProvider = Provider<bool>((ref) {
  final connectivityAsync = ref.watch(connectivityProvider);
  return connectivityAsync.when(
    data: (isConnected) => isConnected,
    loading: () => true, // Assume connected while loading
    error: (_, __) => false, // Assume disconnected on error
  );
});

/// Provider for connectivity status with manual refresh capability
final connectivityStateProvider = StateNotifierProvider<ConnectivityNotifier, ConnectivityState>((ref) {
  final service = ref.watch(utilsConnectivityServiceProvider);
  return ConnectivityNotifier(service);
});

/// Connectivity state
class ConnectivityState {
  final bool isConnected;
  final bool isLoading;
  final String? error;
  final DateTime lastChecked;

  const ConnectivityState({
    required this.isConnected,
    this.isLoading = false,
    this.error,
    required this.lastChecked,
  });

  ConnectivityState copyWith({
    bool? isConnected,
    bool? isLoading,
    String? error,
    DateTime? lastChecked,
  }) {
    return ConnectivityState(
      isConnected: isConnected ?? this.isConnected,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastChecked: lastChecked ?? this.lastChecked,
    );
  }
}

/// Connectivity state notifier
class ConnectivityNotifier extends StateNotifier<ConnectivityState> {
  final utils.ConnectivityService _service;

  ConnectivityNotifier(this._service) : super(ConnectivityState(
    isConnected: true,
    lastChecked: DateTime.now(),
  )) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      // Initialize the service
      await _service.initialize();
      
      // Get initial connectivity status
      final isConnected = await _service.checkConnectivity();
      state = state.copyWith(
        isConnected: isConnected,
        lastChecked: DateTime.now(),
      );

      // Listen to connectivity changes
      _service.connectivityStream.listen((isConnected) {
        state = state.copyWith(
          isConnected: isConnected,
          lastChecked: DateTime.now(),
        );
      });
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        lastChecked: DateTime.now(),
      );
    }
  }

  /// Manually check connectivity
  Future<void> checkConnectivity() async {
    state = state.copyWith(isLoading: true);
    
    try {
      final isConnected = await _service.checkConnectivity();
      state = state.copyWith(
        isConnected: isConnected,
        isLoading: false,
        error: null,
        lastChecked: DateTime.now(),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        lastChecked: DateTime.now(),
      );
    }
  }
}
