import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/translation/translation_accuracy_feedback.dart';
import 'package:culture_connect/services/voice_translation/translation_accuracy_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/analytics_service.dart';

/// Provider for SharedPreferences
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be overridden');
});

/// Provider for TranslationAccuracyService
final translationAccuracyServiceProvider = Provider<TranslationAccuracyService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final analyticsService = ref.watch(analyticsServiceProvider);
  
  return TranslationAccuracyService(prefs, loggingService, analyticsService);
});

/// State class for translation accuracy feedback
class TranslationAccuracyState {
  final List<TranslationAccuracyFeedback> feedbackEntries;
  final AccuracyMetrics? metrics;
  final bool isLoading;
  final String? error;

  const TranslationAccuracyState({
    this.feedbackEntries = const [],
    this.metrics,
    this.isLoading = false,
    this.error,
  });

  TranslationAccuracyState copyWith({
    List<TranslationAccuracyFeedback>? feedbackEntries,
    AccuracyMetrics? metrics,
    bool? isLoading,
    String? error,
  }) {
    return TranslationAccuracyState(
      feedbackEntries: feedbackEntries ?? this.feedbackEntries,
      metrics: metrics ?? this.metrics,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// StateNotifier for managing translation accuracy feedback
class TranslationAccuracyNotifier extends StateNotifier<TranslationAccuracyState> {
  final TranslationAccuracyService _service;

  TranslationAccuracyNotifier(this._service) : super(const TranslationAccuracyState()) {
    _initialize();
  }

  /// Initialize the notifier and listen to service streams
  void _initialize() {
    // Listen to feedback updates
    _service.feedbackStream.listen(
      (feedbackEntries) {
        if (mounted) {
          state = state.copyWith(
            feedbackEntries: feedbackEntries,
            error: null,
          );
        }
      },
      onError: (error) {
        if (mounted) {
          state = state.copyWith(
            error: error.toString(),
            isLoading: false,
          );
        }
      },
    );

    // Listen to metrics updates
    _service.metricsStream.listen(
      (metrics) {
        if (mounted) {
          state = state.copyWith(
            metrics: metrics,
            error: null,
          );
        }
      },
      onError: (error) {
        if (mounted) {
          state = state.copyWith(
            error: error.toString(),
          );
        }
      },
    );

    // Set current user ID if available
    // This would typically come from an auth provider
    _service.setCurrentUserId('current_user_id');
  }

  /// Submit feedback for a translation
  Future<void> submitFeedback({
    required String translationId,
    required String originalText,
    required String translatedText,
    required String sourceLanguage,
    required String targetLanguage,
    required TranslationAccuracyRating accuracyRating,
    required TranslationFeedbackType feedbackType,
    required double userConfidence,
    String? comments,
    String? suggestedCorrection,
    List<TranslationIssue>? issues,
    List<ImprovementSuggestion>? improvements,
    double? originalConfidence,
    bool isVerifiedTranslator = false,
    LanguageProficiencyLevel? userProficiency,
    TranslationContext? context,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _service.submitFeedback(
        translationId: translationId,
        originalText: originalText,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        accuracyRating: accuracyRating,
        feedbackType: feedbackType,
        userConfidence: userConfidence,
        comments: comments,
        suggestedCorrection: suggestedCorrection,
        issues: issues,
        improvements: improvements,
        originalConfidence: originalConfidence,
        isVerifiedTranslator: isVerifiedTranslator,
        userProficiency: userProficiency,
        context: context,
      );

      state = state.copyWith(isLoading: false);
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
      rethrow;
    }
  }

  /// Get feedback for a specific translation
  List<TranslationAccuracyFeedback> getFeedbackForTranslation(String translationId) {
    return _service.getFeedbackForTranslation(translationId);
  }

  /// Get feedback by language pair
  List<TranslationAccuracyFeedback> getFeedbackByLanguagePair(
    String sourceLanguage,
    String targetLanguage,
  ) {
    return _service.getFeedbackByLanguagePair(sourceLanguage, targetLanguage);
  }

  /// Get feedback by accuracy rating
  List<TranslationAccuracyFeedback> getFeedbackByRating(TranslationAccuracyRating rating) {
    return _service.getFeedbackByRating(rating);
  }

  /// Get recent feedback (last 30 days)
  List<TranslationAccuracyFeedback> getRecentFeedback() {
    return _service.getRecentFeedback();
  }

  /// Get top issues across all feedback
  List<String> getTopIssues({int limit = 10}) {
    return _service.getTopIssues(limit: limit);
  }

  /// Get improvement suggestions for a language pair
  List<ImprovementSuggestion> getImprovementSuggestions(
    String sourceLanguage,
    String targetLanguage,
  ) {
    return _service.getImprovementSuggestions(sourceLanguage, targetLanguage);
  }

  /// Vote on feedback helpfulness
  Future<void> voteOnFeedback(String feedbackId, bool isHelpful) async {
    try {
      await _service.voteOnFeedback(feedbackId, isHelpful);
    } catch (error) {
      state = state.copyWith(error: error.toString());
      rethrow;
    }
  }

  /// Clean up old feedback entries
  Future<void> cleanupOldFeedback() async {
    try {
      await _service.cleanupOldFeedback();
    } catch (error) {
      state = state.copyWith(error: error.toString());
      rethrow;
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  @override
  void dispose() {
    _service.dispose();
    super.dispose();
  }
}

/// Provider for TranslationAccuracyNotifier
final translationAccuracyProvider = StateNotifierProvider<TranslationAccuracyNotifier, TranslationAccuracyState>((ref) {
  final service = ref.watch(translationAccuracyServiceProvider);
  return TranslationAccuracyNotifier(service);
});

/// Provider for feedback entries
final feedbackEntriesProvider = Provider<List<TranslationAccuracyFeedback>>((ref) {
  return ref.watch(translationAccuracyProvider).feedbackEntries;
});

/// Provider for accuracy metrics
final accuracyMetricsProvider = Provider<AccuracyMetrics?>((ref) {
  return ref.watch(translationAccuracyProvider).metrics;
});

/// Provider for feedback loading state
final feedbackLoadingProvider = Provider<bool>((ref) {
  return ref.watch(translationAccuracyProvider).isLoading;
});

/// Provider for feedback error state
final feedbackErrorProvider = Provider<String?>((ref) {
  return ref.watch(translationAccuracyProvider).error;
});

/// Provider for feedback by translation ID
final feedbackByTranslationProvider = Provider.family<List<TranslationAccuracyFeedback>, String>((ref, translationId) {
  final notifier = ref.watch(translationAccuracyProvider.notifier);
  return notifier.getFeedbackForTranslation(translationId);
});

/// Provider for feedback by language pair
final feedbackByLanguagePairProvider = Provider.family<List<TranslationAccuracyFeedback>, LanguagePair>((ref, languagePair) {
  final notifier = ref.watch(translationAccuracyProvider.notifier);
  return notifier.getFeedbackByLanguagePair(languagePair.source, languagePair.target);
});

/// Provider for feedback by rating
final feedbackByRatingProvider = Provider.family<List<TranslationAccuracyFeedback>, TranslationAccuracyRating>((ref, rating) {
  final notifier = ref.watch(translationAccuracyProvider.notifier);
  return notifier.getFeedbackByRating(rating);
});

/// Provider for recent feedback
final recentFeedbackProvider = Provider<List<TranslationAccuracyFeedback>>((ref) {
  final notifier = ref.watch(translationAccuracyProvider.notifier);
  return notifier.getRecentFeedback();
});

/// Provider for top issues
final topIssuesProvider = Provider.family<List<String>, int>((ref, limit) {
  final notifier = ref.watch(translationAccuracyProvider.notifier);
  return notifier.getTopIssues(limit: limit);
});

/// Provider for improvement suggestions by language pair
final improvementSuggestionsProvider = Provider.family<List<ImprovementSuggestion>, LanguagePair>((ref, languagePair) {
  final notifier = ref.watch(translationAccuracyProvider.notifier);
  return notifier.getImprovementSuggestions(languagePair.source, languagePair.target);
});

/// Helper class for language pair parameters
class LanguagePair {
  final String source;
  final String target;

  const LanguagePair({
    required this.source,
    required this.target,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LanguagePair && other.source == source && other.target == target;
  }

  @override
  int get hashCode => source.hashCode ^ target.hashCode;
}

/// Provider for feedback statistics
final feedbackStatisticsProvider = Provider<FeedbackStatistics>((ref) {
  final feedbackEntries = ref.watch(feedbackEntriesProvider);
  final metrics = ref.watch(accuracyMetricsProvider);

  return FeedbackStatistics(
    totalFeedback: feedbackEntries.length,
    averageAccuracy: metrics?.averageAccuracy ?? 0.0,
    recentFeedbackCount: metrics?.recentFeedbackCount ?? 0,
    improvementTrend: metrics?.improvementTrend ?? 0.0,
    topIssues: metrics?.topIssues ?? [],
    ratingDistribution: metrics?.ratingDistribution ?? {},
  );
});

/// Model for feedback statistics
class FeedbackStatistics {
  final int totalFeedback;
  final double averageAccuracy;
  final int recentFeedbackCount;
  final double improvementTrend;
  final List<String> topIssues;
  final Map<TranslationAccuracyRating, int> ratingDistribution;

  const FeedbackStatistics({
    required this.totalFeedback,
    required this.averageAccuracy,
    required this.recentFeedbackCount,
    required this.improvementTrend,
    required this.topIssues,
    required this.ratingDistribution,
  });

  /// Get the most common rating
  TranslationAccuracyRating? get mostCommonRating {
    if (ratingDistribution.isEmpty) return null;
    
    var maxCount = 0;
    TranslationAccuracyRating? mostCommon;
    
    for (final entry in ratingDistribution.entries) {
      if (entry.value > maxCount) {
        maxCount = entry.value;
        mostCommon = entry.key;
      }
    }
    
    return mostCommon;
  }

  /// Get accuracy percentage
  double get accuracyPercentage => averageAccuracy * 100;

  /// Check if improvement trend is positive
  bool get isImproving => improvementTrend > 0;

  /// Get improvement trend percentage
  double get improvementTrendPercentage => improvementTrend * 100;
}
