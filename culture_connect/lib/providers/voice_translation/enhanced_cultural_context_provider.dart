import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/enhanced_cultural_context.dart';
import 'package:culture_connect/services/voice_translation/enhanced_cultural_context_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/providers/voice_translation/translation_accuracy_provider.dart';

/// Provider for EnhancedCulturalContextService
final enhancedCulturalContextServiceProvider =
    Provider<EnhancedCulturalContextService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final analyticsService = ref.watch(analyticsServiceProvider);

  return EnhancedCulturalContextService(
      prefs, loggingService, analyticsService);
});

/// State class for enhanced cultural context
class EnhancedCulturalContextState {
  final Map<String, EnhancedCulturalContext> contexts;
  final EnhancedCulturalContext? currentContext;
  final bool isLoading;
  final String? error;

  const EnhancedCulturalContextState({
    this.contexts = const {},
    this.currentContext,
    this.isLoading = false,
    this.error,
  });

  EnhancedCulturalContextState copyWith({
    Map<String, EnhancedCulturalContext>? contexts,
    EnhancedCulturalContext? currentContext,
    bool? isLoading,
    String? error,
  }) {
    return EnhancedCulturalContextState(
      contexts: contexts ?? this.contexts,
      currentContext: currentContext ?? this.currentContext,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// StateNotifier for managing enhanced cultural context
class EnhancedCulturalContextNotifier
    extends StateNotifier<EnhancedCulturalContextState> {
  final EnhancedCulturalContextService _service;

  EnhancedCulturalContextNotifier(this._service)
      : super(const EnhancedCulturalContextState()) {
    _initialize();
  }

  /// Initialize the notifier and listen to service streams
  void _initialize() {
    // Listen to context updates
    _service.contextStream.listen(
      (context) {
        if (mounted) {
          final updatedContexts =
              Map<String, EnhancedCulturalContext>.from(state.contexts);
          updatedContexts[context.id] = context;

          state = state.copyWith(
            contexts: updatedContexts,
            currentContext: context,
            error: null,
          );
        }
      },
      onError: (error) {
        if (mounted) {
          state = state.copyWith(
            error: error.toString(),
            isLoading: false,
          );
        }
      },
    );
  }

  /// Generate cultural context for a translation
  Future<EnhancedCulturalContext> generateCulturalContext({
    required String originalText,
    required String translatedText,
    required String sourceLanguage,
    required String targetLanguage,
    String? sourceRegion,
    String? targetRegion,
    String? contextType,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final context = await _service.generateCulturalContext(
        originalText: originalText,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        sourceRegion: sourceRegion,
        targetRegion: targetRegion,
        contextType: contextType,
      );

      state = state.copyWith(isLoading: false);
      return context;
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
      rethrow;
    }
  }

  /// Get cultural context by ID
  EnhancedCulturalContext? getCulturalContext(String id) {
    return state.contexts[id];
  }

  /// Get contexts by language pair
  List<EnhancedCulturalContext> getContextsByLanguagePair(
    String sourceLanguage,
    String targetLanguage,
  ) {
    return state.contexts.values
        .where((context) =>
            context.sourceLanguage == sourceLanguage &&
            context.targetLanguage == targetLanguage)
        .toList();
  }

  /// Get contexts with sensitivity warnings
  List<EnhancedCulturalContext> getContextsWithWarnings() {
    return state.contexts.values
        .where((context) => context.sensitivityWarnings.isNotEmpty)
        .toList();
  }

  /// Get contexts by confidence score
  List<EnhancedCulturalContext> getContextsByConfidence({
    double minConfidence = 0.0,
    double maxConfidence = 1.0,
  }) {
    return state.contexts.values
        .where((context) =>
            context.confidenceScore >= minConfidence &&
            context.confidenceScore <= maxConfidence)
        .toList();
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear current context
  void clearCurrentContext() {
    state = state.copyWith(currentContext: null);
  }

  @override
  void dispose() {
    _service.dispose();
    super.dispose();
  }
}

/// Provider for EnhancedCulturalContextNotifier
final enhancedCulturalContextProvider = StateNotifierProvider<
    EnhancedCulturalContextNotifier, EnhancedCulturalContextState>((ref) {
  final service = ref.watch(enhancedCulturalContextServiceProvider);
  return EnhancedCulturalContextNotifier(service);
});

/// Provider for cultural contexts map
final culturalContextsProvider =
    Provider<Map<String, EnhancedCulturalContext>>((ref) {
  return ref.watch(enhancedCulturalContextProvider).contexts;
});

/// Provider for current cultural context
final currentCulturalContextProvider =
    Provider<EnhancedCulturalContext?>((ref) {
  return ref.watch(enhancedCulturalContextProvider).currentContext;
});

/// Provider for cultural context loading state
final culturalContextLoadingProvider = Provider<bool>((ref) {
  return ref.watch(enhancedCulturalContextProvider).isLoading;
});

/// Provider for cultural context error state
final culturalContextErrorProvider = Provider<String?>((ref) {
  return ref.watch(enhancedCulturalContextProvider).error;
});

/// Provider for cultural context by ID
final culturalContextByIdProvider =
    Provider.family<EnhancedCulturalContext?, String>((ref, id) {
  final notifier = ref.watch(enhancedCulturalContextProvider.notifier);
  return notifier.getCulturalContext(id);
});

/// Provider for contexts by language pair
final contextsByLanguagePairProvider =
    Provider.family<List<EnhancedCulturalContext>, LanguagePair>(
        (ref, languagePair) {
  final notifier = ref.watch(enhancedCulturalContextProvider.notifier);
  return notifier.getContextsByLanguagePair(
      languagePair.source, languagePair.target);
});

/// Provider for contexts with warnings
final contextsWithWarningsProvider =
    Provider<List<EnhancedCulturalContext>>((ref) {
  final notifier = ref.watch(enhancedCulturalContextProvider.notifier);
  return notifier.getContextsWithWarnings();
});

/// Provider for high confidence contexts
final highConfidenceContextsProvider =
    Provider<List<EnhancedCulturalContext>>((ref) {
  final notifier = ref.watch(enhancedCulturalContextProvider.notifier);
  return notifier.getContextsByConfidence(minConfidence: 0.7);
});

/// Provider for low confidence contexts
final lowConfidenceContextsProvider =
    Provider<List<EnhancedCulturalContext>>((ref) {
  final notifier = ref.watch(enhancedCulturalContextProvider.notifier);
  return notifier.getContextsByConfidence(maxConfidence: 0.5);
});

/// Provider for cultural context statistics
final culturalContextStatisticsProvider =
    Provider<CulturalContextStatistics>((ref) {
  final contexts = ref.watch(culturalContextsProvider);

  if (contexts.isEmpty) {
    return const CulturalContextStatistics(
      totalContexts: 0,
      averageConfidence: 0.0,
      contextsWithWarnings: 0,
      contextsWithAdaptations: 0,
      contextsWithRegionalVariations: 0,
      mostCommonSourceLanguage: null,
      mostCommonTargetLanguage: null,
    );
  }

  final contextList = contexts.values.toList();

  // Calculate average confidence
  final averageConfidence = contextList.fold<double>(
        0.0,
        (sum, context) => sum + context.confidenceScore,
      ) /
      contextList.length;

  // Count contexts with warnings
  final contextsWithWarnings = contextList
      .where((context) => context.sensitivityWarnings.isNotEmpty)
      .length;

  // Count contexts with adaptations
  final contextsWithAdaptations =
      contextList.where((context) => context.adaptations.isNotEmpty).length;

  // Count contexts with regional variations
  final contextsWithRegionalVariations = contextList
      .where((context) => context.regionalVariations.isNotEmpty)
      .length;

  // Find most common languages
  final sourceLanguageCounts = <String, int>{};
  final targetLanguageCounts = <String, int>{};

  for (final context in contextList) {
    sourceLanguageCounts[context.sourceLanguage] =
        (sourceLanguageCounts[context.sourceLanguage] ?? 0) + 1;
    targetLanguageCounts[context.targetLanguage] =
        (targetLanguageCounts[context.targetLanguage] ?? 0) + 1;
  }

  String? mostCommonSourceLanguage;
  String? mostCommonTargetLanguage;

  if (sourceLanguageCounts.isNotEmpty) {
    mostCommonSourceLanguage = sourceLanguageCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  if (targetLanguageCounts.isNotEmpty) {
    mostCommonTargetLanguage = targetLanguageCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  return CulturalContextStatistics(
    totalContexts: contextList.length,
    averageConfidence: averageConfidence,
    contextsWithWarnings: contextsWithWarnings,
    contextsWithAdaptations: contextsWithAdaptations,
    contextsWithRegionalVariations: contextsWithRegionalVariations,
    mostCommonSourceLanguage: mostCommonSourceLanguage,
    mostCommonTargetLanguage: mostCommonTargetLanguage,
  );
});

/// Provider for cultural sensitivity warnings summary
final culturalSensitivitySummaryProvider =
    Provider<CulturalSensitivitySummary>((ref) {
  final contexts = ref.watch(culturalContextsProvider);

  final allWarnings = <CulturalSensitivityWarning>[];
  final warningsByType = <String, int>{};
  final warningsByLanguage = <String, int>{};

  for (final context in contexts.values) {
    for (final warning in context.sensitivityWarnings) {
      allWarnings.add(warning);

      warningsByType[warning.type] = (warningsByType[warning.type] ?? 0) + 1;
      warningsByLanguage[context.targetLanguage] =
          (warningsByLanguage[context.targetLanguage] ?? 0) + 1;
    }
  }

  return CulturalSensitivitySummary(
    totalWarnings: allWarnings.length,
    warningsByType: warningsByType,
    warningsByLanguage: warningsByLanguage,
    mostCommonWarningType: warningsByType.isNotEmpty
        ? warningsByType.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : null,
    languageWithMostWarnings: warningsByLanguage.isNotEmpty
        ? warningsByLanguage.entries
            .reduce((a, b) => a.value > b.value ? a : b)
            .key
        : null,
  );
});

/// Model for cultural context statistics
class CulturalContextStatistics {
  final int totalContexts;
  final double averageConfidence;
  final int contextsWithWarnings;
  final int contextsWithAdaptations;
  final int contextsWithRegionalVariations;
  final String? mostCommonSourceLanguage;
  final String? mostCommonTargetLanguage;

  const CulturalContextStatistics({
    required this.totalContexts,
    required this.averageConfidence,
    required this.contextsWithWarnings,
    required this.contextsWithAdaptations,
    required this.contextsWithRegionalVariations,
    this.mostCommonSourceLanguage,
    this.mostCommonTargetLanguage,
  });

  /// Get confidence percentage
  double get confidencePercentage => averageConfidence * 100;

  /// Get warning percentage
  double get warningPercentage =>
      totalContexts > 0 ? (contextsWithWarnings / totalContexts) * 100 : 0.0;

  /// Get adaptation percentage
  double get adaptationPercentage =>
      totalContexts > 0 ? (contextsWithAdaptations / totalContexts) * 100 : 0.0;

  /// Get regional variation percentage
  double get regionalVariationPercentage => totalContexts > 0
      ? (contextsWithRegionalVariations / totalContexts) * 100
      : 0.0;
}

/// Model for cultural sensitivity warning summary
class CulturalSensitivitySummary {
  final int totalWarnings;
  final Map<String, int> warningsByType;
  final Map<String, int> warningsByLanguage;
  final String? mostCommonWarningType;
  final String? languageWithMostWarnings;

  const CulturalSensitivitySummary({
    required this.totalWarnings,
    required this.warningsByType,
    required this.warningsByLanguage,
    this.mostCommonWarningType,
    this.languageWithMostWarnings,
  });

  /// Get top warning types
  List<MapEntry<String, int>> getTopWarningTypes({int limit = 5}) {
    final entries = warningsByType.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    return entries.take(limit).toList();
  }

  /// Get languages with most warnings
  List<MapEntry<String, int>> getLanguagesWithMostWarnings({int limit = 5}) {
    final entries = warningsByLanguage.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    return entries.take(limit).toList();
  }
}
