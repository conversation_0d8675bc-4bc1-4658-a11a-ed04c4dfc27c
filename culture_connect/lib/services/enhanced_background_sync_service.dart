import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:workmanager/workmanager.dart' as wm;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';

import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/models/offline/content_priority.dart';
import 'package:culture_connect/models/offline/device_state.dart';
import 'package:culture_connect/models/offline/bandwidth_usage.dart'
    show NetworkType;
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/connectivity_service.dart';
import 'package:culture_connect/services/enhanced_offline_cache_service.dart';

/// Enhanced background sync service with intelligent scheduling
class EnhancedBackgroundSyncService {
  static final EnhancedBackgroundSyncService _instance =
      EnhancedBackgroundSyncService._internal();
  factory EnhancedBackgroundSyncService() => _instance;
  EnhancedBackgroundSyncService._internal();

  final LoggingService _loggingService = LoggingService();
  final ConnectivityService _connectivityService = ConnectivityService();
  final EnhancedOfflineCacheService _cacheService =
      EnhancedOfflineCacheService();
  final Battery _battery = Battery();
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  // Sync configuration
  static const String _syncTaskName = 'enhanced_background_sync';
  static const String _prioritySyncTaskName = 'priority_background_sync';
  static const Duration _defaultSyncInterval = Duration(hours: 2);
  static const Duration _prioritySyncInterval = Duration(minutes: 30);
  static const int _maxRetryAttempts = 3;
  static const Duration _retryBackoffBase = Duration(minutes: 5);

  // Sync state
  final StreamController<SyncEvent> _syncEventController =
      StreamController<SyncEvent>.broadcast();
  final Map<String, SyncTask> _pendingSyncTasks = {};
  final Map<String, int> _retryAttempts = {};

  bool _isInitialized = false;
  Timer? _syncTimer;
  SyncStatus _currentSyncStatus = SyncStatus.notSynced;

  /// Initialize the enhanced background sync service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await wm.Workmanager().initialize(
        _callbackDispatcher,
        isInDebugMode: kDebugMode,
      );

      await _scheduleBackgroundSync();
      await _schedulePrioritySync();

      _startPeriodicSync();
      _listenToConnectivityChanges();
      _listenToBatteryChanges();

      _isInitialized = true;
      _loggingService.info(
          'EnhancedBackgroundSyncService', 'Service initialized');
    } catch (e) {
      _loggingService.error(
          'EnhancedBackgroundSyncService', 'Error initializing service: $e');
      rethrow;
    }
  }

  /// Schedule background sync task
  Future<void> _scheduleBackgroundSync() async {
    await wm.Workmanager().registerPeriodicTask(
      _syncTaskName,
      _syncTaskName,
      frequency: _defaultSyncInterval,
      constraints: wm.Constraints(
        networkType: wm.NetworkType.connected,
        requiresBatteryNotLow: true,
        requiresCharging: false,
        requiresDeviceIdle: false,
        requiresStorageNotLow: true,
      ),
      backoffPolicy: wm.BackoffPolicy.exponential,
      backoffPolicyDelay: _retryBackoffBase,
    );
  }

  /// Schedule priority sync task
  Future<void> _schedulePrioritySync() async {
    await wm.Workmanager().registerPeriodicTask(
      _prioritySyncTaskName,
      _prioritySyncTaskName,
      frequency: _prioritySyncInterval,
      constraints: wm.Constraints(
        networkType: wm.NetworkType.connected,
        requiresBatteryNotLow: false,
        requiresCharging: false,
        requiresDeviceIdle: false,
        requiresStorageNotLow: false,
      ),
      backoffPolicy: wm.BackoffPolicy.linear,
      backoffPolicyDelay: Duration(minutes: 2),
    );
  }

  /// Start periodic sync timer
  void _startPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(Duration(minutes: 15), (timer) {
      _performIntelligentSync();
    });
  }

  /// Listen to connectivity changes
  void _listenToConnectivityChanges() {
    _connectivityService.connectivityStream.listen((connectivityResult) {
      final isConnected = connectivityResult != ConnectivityResult.none;
      if (isConnected && _currentSyncStatus != SyncStatus.syncing) {
        _performIntelligentSync();
      }
    });
  }

  /// Listen to battery changes
  void _listenToBatteryChanges() {
    _battery.onBatteryStateChanged.listen((batteryState) {
      if (batteryState == BatteryState.charging) {
        // Opportunistic sync when charging
        _performIntelligentSync(forceSync: true);
      }
    });
  }

  /// Perform intelligent sync based on device state and conditions
  Future<void> _performIntelligentSync({bool forceSync = false}) async {
    if (_currentSyncStatus == SyncStatus.syncing && !forceSync) return;

    try {
      _currentSyncStatus = SyncStatus.syncing;
      _emitSyncEvent(SyncEventType.started);

      final deviceState = await _getDeviceState();
      final syncDecision = await _makeSyncDecision(deviceState, forceSync);

      if (!syncDecision.shouldSync) {
        _currentSyncStatus = SyncStatus.pending;
        _emitSyncEvent(SyncEventType.skipped, message: syncDecision.reason);
        return;
      }

      // Perform sync based on priority and conditions
      final syncResult = await _performSync(syncDecision);

      if (syncResult.success) {
        _currentSyncStatus = SyncStatus.synced;
        _emitSyncEvent(SyncEventType.completed,
            message: 'Synced ${syncResult.itemsProcessed} items');
        _clearRetryAttempts();
      } else {
        _currentSyncStatus = SyncStatus.failed;
        _emitSyncEvent(SyncEventType.failed, message: syncResult.error);
        _handleSyncFailure(syncResult.error ?? 'Unknown error');
      }
    } catch (e) {
      _currentSyncStatus = SyncStatus.failed;
      _emitSyncEvent(SyncEventType.failed, message: e.toString());
      _handleSyncFailure(e.toString());
    }
  }

  /// Get current device state
  Future<DeviceState> _getDeviceState() async {
    final batteryLevel = await _battery.batteryLevel;
    final batteryState = await _battery.batteryState;
    final connectivityResult = await Connectivity().checkConnectivity();

    bool isWiFi = connectivityResult == ConnectivityResult.wifi;
    bool isCharging = batteryState == BatteryState.charging;
    bool isLowBattery = batteryLevel < 20;

    return DeviceState(
      isConnected: connectivityResult != ConnectivityResult.none,
      networkType: isWiFi
          ? NetworkType.wifi
          : (connectivityResult == ConnectivityResult.mobile
              ? NetworkType.mobile
              : NetworkType.unknown),
      isCharging: isCharging,
      batteryLevel: batteryLevel,
      isPowerSaveMode: isLowBattery,
      isIdle: false, // TODO: Implement idle detection
      availableStorage:
          1024 * 1024 * 1024, // TODO: Get actual available storage
    );
  }

  /// Make intelligent sync decision based on device state
  Future<SyncDecision> _makeSyncDecision(
      DeviceState deviceState, bool forceSync) async {
    if (forceSync) {
      return SyncDecision(
          shouldSync: true,
          priority: SyncPriority.high,
          reason: 'Force sync requested');
    }

    // Check connectivity
    if (!deviceState.isOnWifi && !deviceState.isOnMobileData) {
      return SyncDecision(shouldSync: false, reason: 'No network connectivity');
    }

    // Check battery conditions
    if (deviceState.hasLowBattery && !deviceState.isCharging) {
      return SyncDecision(
          shouldSync: false, reason: 'Low battery and not charging');
    }

    // Determine sync priority
    SyncPriority priority = SyncPriority.normal;
    String reason = 'Normal sync conditions';

    if (deviceState.isCharging && deviceState.isOnWifi) {
      priority = SyncPriority.high;
      reason = 'Optimal conditions (charging + WiFi)';
    } else if (deviceState.isOnWifi) {
      priority = SyncPriority.normal;
      reason = 'Good conditions (WiFi)';
    } else if (deviceState.batteryLevel > 50) {
      priority = SyncPriority.low;
      reason = 'Mobile data with good battery';
    } else {
      return SyncDecision(shouldSync: false, reason: 'Suboptimal conditions');
    }

    return SyncDecision(shouldSync: true, priority: priority, reason: reason);
  }

  /// Perform the actual sync operation
  Future<SyncResult> _performSync(SyncDecision decision) async {
    int itemsProcessed = 0;
    String? error;

    try {
      // Get pending sync tasks based on priority
      final tasksToSync = _getPendingSyncTasks(decision.priority);

      for (final task in tasksToSync) {
        try {
          await _processSyncTask(task);
          itemsProcessed++;
          _pendingSyncTasks.remove(task.id);
        } catch (e) {
          _loggingService.error('EnhancedBackgroundSyncService',
              'Error processing sync task ${task.id}: $e');
          // Continue with other tasks
        }
      }

      // Perform cache cleanup if conditions are optimal
      if (decision.priority == SyncPriority.high) {
        await _cacheService.cleanupExpiredContent();
      }

      return SyncResult(
        success: true,
        itemsProcessed: itemsProcessed,
        error: null,
      );
    } catch (e) {
      return SyncResult(
        success: false,
        itemsProcessed: itemsProcessed,
        error: e.toString(),
      );
    }
  }

  /// Get pending sync tasks based on priority
  List<SyncTask> _getPendingSyncTasks(SyncPriority syncPriority) {
    final tasks = _pendingSyncTasks.values.toList();

    // Filter and sort by priority
    tasks.sort((a, b) {
      // Higher priority tasks first
      final priorityComparison = b.priority.index.compareTo(a.priority.index);
      if (priorityComparison != 0) return priorityComparison;

      // Then by creation time (older first)
      return a.createdAt.compareTo(b.createdAt);
    });

    // Limit tasks based on sync priority
    int maxTasks;
    switch (syncPriority) {
      case SyncPriority.high:
        maxTasks = 50;
        break;
      case SyncPriority.normal:
        maxTasks = 20;
        break;
      case SyncPriority.low:
        maxTasks = 5;
        break;
    }

    return tasks.take(maxTasks).toList();
  }

  /// Process individual sync task
  Future<void> _processSyncTask(SyncTask task) async {
    // Simulate sync operation - in real implementation, this would
    // sync with the actual backend API
    await Future.delayed(Duration(milliseconds: 100 + Random().nextInt(400)));

    // Update cache if needed
    if (task.data != null) {
      await _cacheService.cacheContent(
        contentId: task.id,
        contentType: task.type,
        data: task.data!,
        priority: task.priority,
      );
    }
  }

  /// Handle sync failure with retry logic
  void _handleSyncFailure(String error) {
    final currentAttempts = _retryAttempts['sync'] ?? 0;

    if (currentAttempts < _maxRetryAttempts) {
      _retryAttempts['sync'] = currentAttempts + 1;

      // Schedule retry with exponential backoff
      final retryDelay = Duration(
        minutes: _retryBackoffBase.inMinutes * pow(2, currentAttempts).toInt(),
      );

      Timer(retryDelay, () {
        _performIntelligentSync();
      });

      _loggingService.info('EnhancedBackgroundSyncService',
          'Scheduling sync retry ${currentAttempts + 1}/$_maxRetryAttempts in ${retryDelay.inMinutes} minutes');
    } else {
      _loggingService.error('EnhancedBackgroundSyncService',
          'Max sync retry attempts reached. Error: $error');
      _clearRetryAttempts();
    }
  }

  /// Clear retry attempts
  void _clearRetryAttempts() {
    _retryAttempts.clear();
  }

  /// Add sync task to queue
  void addSyncTask(SyncTask task) {
    _pendingSyncTasks[task.id] = task;
    _emitSyncEvent(SyncEventType.taskAdded,
        message: 'Added sync task: ${task.id}');
  }

  /// Remove sync task from queue
  void removeSyncTask(String taskId) {
    _pendingSyncTasks.remove(taskId);
    _emitSyncEvent(SyncEventType.taskRemoved,
        message: 'Removed sync task: $taskId');
  }

  /// Get current sync status
  SyncStatus get currentSyncStatus => _currentSyncStatus;

  /// Get pending sync tasks count
  int get pendingSyncTasksCount => _pendingSyncTasks.length;

  /// Stream of sync events
  Stream<SyncEvent> get syncEventStream => _syncEventController.stream;

  /// Emit sync event
  void _emitSyncEvent(SyncEventType type, {String? message}) {
    _syncEventController.add(SyncEvent(
      type: type,
      timestamp: DateTime.now(),
      message: message,
    ));
  }

  /// Force immediate sync
  Future<void> forceSyncNow() async {
    await _performIntelligentSync(forceSync: true);
  }

  /// Dispose resources
  void dispose() {
    _syncTimer?.cancel();
    _syncEventController.close();
    wm.Workmanager().cancelAll();
  }
}

/// Background sync callback dispatcher
@pragma('vm:entry-point')
void _callbackDispatcher() {
  wm.Workmanager().executeTask((task, inputData) async {
    try {
      final syncService = EnhancedBackgroundSyncService();
      await syncService.initialize();
      await syncService.forceSyncNow();
      return Future.value(true);
    } catch (e) {
      // Use debugPrint instead of print for production code
      debugPrint('Background sync error: $e');
      return Future.value(false);
    }
  });
}

/// Sync decision class
class SyncDecision {
  final bool shouldSync;
  final SyncPriority priority;
  final String reason;

  const SyncDecision({
    required this.shouldSync,
    this.priority = SyncPriority.normal,
    required this.reason,
  });
}

/// Sync result class
class SyncResult {
  final bool success;
  final int itemsProcessed;
  final String? error;

  const SyncResult({
    required this.success,
    required this.itemsProcessed,
    this.error,
  });
}

/// Sync task class
class SyncTask {
  final String id;
  final String type;
  final ContentPriority priority;
  final DateTime createdAt;
  final Map<String, dynamic>? data;

  const SyncTask({
    required this.id,
    required this.type,
    required this.priority,
    required this.createdAt,
    this.data,
  });
}

/// Sync event class
class SyncEvent {
  final SyncEventType type;
  final DateTime timestamp;
  final String? message;

  const SyncEvent({
    required this.type,
    required this.timestamp,
    this.message,
  });
}

/// Sync event types
enum SyncEventType {
  started,
  completed,
  failed,
  skipped,
  taskAdded,
  taskRemoved,
}

/// Sync priority levels
enum SyncPriority {
  low,
  normal,
  high,
}
