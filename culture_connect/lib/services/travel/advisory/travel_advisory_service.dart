import 'dart:async';
import 'dart:convert';

// Package imports
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/models/travel/advisory/travel_advisory_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/notification_service.dart';

/// Provider for the travel advisory service
final travelAdvisoryServiceProvider = Provider<TravelAdvisoryService>((ref) {
  final firestore = FirebaseFirestore.instance;
  final loggingService = ref.read(loggingServiceProvider);
  final notificationService = NotificationService();

  final service = TravelAdvisoryService(
    firestore: firestore,
    loggingService: loggingService,
    notificationService: notificationService,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Service for managing travel advisories with real-time updates and offline capability
class TravelAdvisoryService {
  static const String _logTag = 'TravelAdvisoryService';
  static const String _cachePrefix = 'travel_advisory_';
  static const String _subscriptionCacheKey = 'advisory_subscriptions';
  static const String _documentChangesCacheKey = 'document_requirement_changes';

  final FirebaseFirestore _firestore;
  final LoggingService _loggingService;
  final NotificationService _notificationService;
  final Connectivity _connectivity = Connectivity();

  // Cache and connectivity
  SharedPreferences? _prefs;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  bool _isOnline = true;

  // Cache expiration times
  static const Duration _advisoryCacheExpiration = Duration(hours: 1);
  static const Duration _subscriptionCacheExpiration = Duration(days: 7);
  static const Duration _documentChangesCacheExpiration = Duration(hours: 6);

  // Background refresh intervals
  static const Duration _backgroundRefreshInterval = Duration(minutes: 30);
  static const Duration _criticalAdvisoryCheckInterval = Duration(minutes: 5);

  // Stream controllers for real-time updates
  final _advisoryUpdateController =
      StreamController<List<TravelAdvisory>>.broadcast();
  final _documentChangesController =
      StreamController<List<DocumentRequirementChange>>.broadcast();
  final _subscriptionUpdateController =
      StreamController<AdvisorySubscription>.broadcast();

  // Background refresh timers
  Timer? _backgroundRefreshTimer;
  Timer? _criticalAdvisoryTimer;

  // Cache storage
  List<TravelAdvisory> _cachedAdvisories = [];
  List<DocumentRequirementChange> _cachedDocumentChanges = [];
  Map<String, AdvisorySubscription> _cachedSubscriptions = {};

  TravelAdvisoryService({
    required FirebaseFirestore firestore,
    required LoggingService loggingService,
    required NotificationService notificationService,
  })  : _firestore = firestore,
        _loggingService = loggingService,
        _notificationService = notificationService;

  /// Initialize the service
  Future<void> initialize() async {
    try {
      _loggingService.info(_logTag, 'Initializing travel advisory service');

      // Initialize SharedPreferences
      _prefs = await SharedPreferences.getInstance();

      // Load cached data
      await _loadCachedData();

      // Initialize connectivity monitoring
      _initConnectivityListener();

      // Start background refresh timers
      _startBackgroundRefresh();

      _loggingService.info(
          _logTag, 'Travel advisory service initialized successfully');
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to initialize service', e, stackTrace);
    }
  }

  /// Initialize connectivity listener
  void _initConnectivityListener() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen((result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;

      _loggingService.debug(_logTag, 'Connectivity changed: $_isOnline');

      // If we just came back online, refresh data
      if (!wasOnline && _isOnline) {
        _refreshAllData();
      }
    });
  }

  /// Start background refresh timers
  void _startBackgroundRefresh() {
    // Regular background refresh
    _backgroundRefreshTimer = Timer.periodic(_backgroundRefreshInterval, (_) {
      if (_isOnline) {
        _refreshAllData();
      }
    });

    // Critical advisory check (more frequent)
    _criticalAdvisoryTimer =
        Timer.periodic(_criticalAdvisoryCheckInterval, (_) {
      if (_isOnline) {
        _checkCriticalAdvisories();
      }
    });
  }

  /// Load cached data from SharedPreferences
  Future<void> _loadCachedData() async {
    try {
      // Load cached advisories
      final advisoriesJson = _prefs?.getString('${_cachePrefix}advisories');
      if (advisoriesJson != null) {
        final List<dynamic> advisoriesList = jsonDecode(advisoriesJson);
        _cachedAdvisories = advisoriesList
            .map((json) => TravelAdvisory.fromJson(json))
            .toList();
      }

      // Load cached document changes
      final changesJson = _prefs?.getString(_documentChangesCacheKey);
      if (changesJson != null) {
        final List<dynamic> changesList = jsonDecode(changesJson);
        _cachedDocumentChanges = changesList
            .map((json) => DocumentRequirementChange.fromJson(json))
            .toList();
      }

      // Load cached subscriptions
      final subscriptionsJson = _prefs?.getString(_subscriptionCacheKey);
      if (subscriptionsJson != null) {
        final Map<String, dynamic> subscriptionsMap =
            jsonDecode(subscriptionsJson);
        _cachedSubscriptions = subscriptionsMap.map(
          (key, value) => MapEntry(key, AdvisorySubscription.fromJson(value)),
        );
      }

      _loggingService.debug(
          _logTag,
          'Loaded cached data: '
          '${_cachedAdvisories.length} advisories, '
          '${_cachedDocumentChanges.length} document changes, '
          '${_cachedSubscriptions.length} subscriptions');
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to load cached data', e, stackTrace);
    }
  }

  /// Get travel advisories for specific countries
  Future<List<TravelAdvisory>> getAdvisoriesForCountries(
    List<String> countryCodes, {
    List<AdvisoryType>? types,
    List<AdvisorySeverity>? severities,
    bool activeOnly = true,
  }) async {
    try {
      _loggingService.debug(
          _logTag, 'Getting advisories for countries: $countryCodes');

      // Check cache first
      List<TravelAdvisory> advisories = _getFilteredCachedAdvisories(
        countryCodes: countryCodes,
        types: types,
        severities: severities,
        activeOnly: activeOnly,
      );

      // If online and cache is stale, refresh from Firestore
      if (_isOnline && _isCacheStale('${_cachePrefix}advisories')) {
        advisories = await _fetchAdvisoriesFromFirestore(
          countryCodes: countryCodes,
          types: types,
          severities: severities,
          activeOnly: activeOnly,
        );
      }

      return advisories;
    } catch (e, stackTrace) {
      _loggingService.error(_logTag, 'Failed to get advisories', e, stackTrace);

      // Return cached data as fallback
      return _getFilteredCachedAdvisories(
        countryCodes: countryCodes,
        types: types,
        severities: severities,
        activeOnly: activeOnly,
      );
    }
  }

  /// Get all active travel advisories
  Future<List<TravelAdvisory>> getAllActiveAdvisories() async {
    try {
      _loggingService.debug(_logTag, 'Getting all active advisories');

      // Check cache first
      if (!_isOnline || !_isCacheStale('${_cachePrefix}advisories')) {
        return _cachedAdvisories
            .where((advisory) => advisory.isActive)
            .toList();
      }

      // Fetch from Firestore
      final query = _firestore
          .collection('travel_advisories')
          .where('status', isEqualTo: 'active')
          .orderBy('severity', descending: true)
          .orderBy('issuedAt', descending: true);

      final snapshot = await query.get();
      final advisories = snapshot.docs
          .map((doc) => TravelAdvisory.fromJson({...doc.data(), 'id': doc.id}))
          .where((advisory) => advisory.isActive)
          .toList();

      // Update cache
      await _cacheAdvisories(advisories);

      return advisories;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to get all active advisories', e, stackTrace);
      return _cachedAdvisories.where((advisory) => advisory.isActive).toList();
    }
  }

  /// Get document requirement changes for countries
  Future<List<DocumentRequirementChange>> getDocumentChangesForCountries(
    List<String> countryCodes, {
    bool urgentOnly = false,
    bool upcomingOnly = false,
  }) async {
    try {
      _loggingService.debug(
          _logTag, 'Getting document changes for countries: $countryCodes');

      // Check cache first
      List<DocumentRequirementChange> changes =
          _getFilteredCachedDocumentChanges(
        countryCodes: countryCodes,
        urgentOnly: urgentOnly,
        upcomingOnly: upcomingOnly,
      );

      // If online and cache is stale, refresh from Firestore
      if (_isOnline && _isCacheStale(_documentChangesCacheKey)) {
        changes = await _fetchDocumentChangesFromFirestore(
          countryCodes: countryCodes,
          urgentOnly: urgentOnly,
          upcomingOnly: upcomingOnly,
        );
      }

      return changes;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to get document changes', e, stackTrace);

      // Return cached data as fallback
      return _getFilteredCachedDocumentChanges(
        countryCodes: countryCodes,
        urgentOnly: urgentOnly,
        upcomingOnly: upcomingOnly,
      );
    }
  }

  /// Subscribe to travel advisories for specific countries
  Future<AdvisorySubscription> subscribeToAdvisories({
    required String userId,
    required List<String> countryCodes,
    List<AdvisoryType>? advisoryTypes,
    List<AdvisorySeverity>? severityLevels,
    bool emailNotifications = true,
    bool pushNotifications = true,
    bool smsNotifications = false,
    Map<String, dynamic>? preferences,
  }) async {
    try {
      _loggingService.info(
          _logTag, 'Creating advisory subscription for user: $userId');

      final subscription = AdvisorySubscription(
        id: _firestore.collection('advisory_subscriptions').doc().id,
        userId: userId,
        countryCodes: countryCodes,
        advisoryTypes: advisoryTypes ?? AdvisoryType.values,
        severityLevels: severityLevels ?? AdvisorySeverity.values,
        emailNotifications: emailNotifications,
        pushNotifications: pushNotifications,
        smsNotifications: smsNotifications,
        createdAt: DateTime.now(),
        isActive: true,
        preferences: preferences ?? {},
      );

      // Save to Firestore
      await _firestore
          .collection('advisory_subscriptions')
          .doc(subscription.id)
          .set(subscription.toJson());

      // Update cache
      _cachedSubscriptions[subscription.id] = subscription;
      await _cacheSubscriptions();

      // Notify listeners
      _subscriptionUpdateController.add(subscription);

      _loggingService.info(
          _logTag, 'Advisory subscription created: ${subscription.id}');
      return subscription;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to create subscription', e, stackTrace);
      rethrow;
    }
  }

  /// Update advisory subscription
  Future<AdvisorySubscription> updateSubscription(
    String subscriptionId,
    AdvisorySubscription updatedSubscription,
  ) async {
    try {
      _loggingService.info(
          _logTag, 'Updating advisory subscription: $subscriptionId');

      // Update in Firestore
      await _firestore
          .collection('advisory_subscriptions')
          .doc(subscriptionId)
          .update(updatedSubscription.toJson());

      // Update cache
      _cachedSubscriptions[subscriptionId] = updatedSubscription;
      await _cacheSubscriptions();

      // Notify listeners
      _subscriptionUpdateController.add(updatedSubscription);

      _loggingService.info(
          _logTag, 'Advisory subscription updated: $subscriptionId');
      return updatedSubscription;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to update subscription', e, stackTrace);
      rethrow;
    }
  }

  /// Get user's advisory subscriptions
  Future<List<AdvisorySubscription>> getUserSubscriptions(String userId) async {
    try {
      _loggingService.debug(_logTag, 'Getting subscriptions for user: $userId');

      // Check cache first
      final cachedUserSubscriptions = _cachedSubscriptions.values
          .where((sub) => sub.userId == userId)
          .toList();

      if (!_isOnline || !_isCacheStale(_subscriptionCacheKey)) {
        return cachedUserSubscriptions;
      }

      // Fetch from Firestore
      final query = _firestore
          .collection('advisory_subscriptions')
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true);

      final snapshot = await query.get();
      final subscriptions = snapshot.docs
          .map((doc) =>
              AdvisorySubscription.fromJson({...doc.data(), 'id': doc.id}))
          .toList();

      // Update cache
      for (final subscription in subscriptions) {
        _cachedSubscriptions[subscription.id] = subscription;
      }
      await _cacheSubscriptions();

      return subscriptions;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to get user subscriptions', e, stackTrace);
      return _cachedSubscriptions.values
          .where((sub) => sub.userId == userId)
          .toList();
    }
  }

  /// Check for critical advisories and send notifications
  Future<void> _checkCriticalAdvisories() async {
    try {
      final criticalAdvisories = await getAllActiveAdvisories();
      final critical = criticalAdvisories
          .where((advisory) => advisory.severity == AdvisorySeverity.critical)
          .toList();

      if (critical.isNotEmpty) {
        _loggingService.warning(
            _logTag, 'Found ${critical.length} critical advisories');

        // Send notifications for critical advisories
        for (final advisory in critical) {
          await _sendAdvisoryNotification(advisory);
        }
      }
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to check critical advisories', e, stackTrace);
    }
  }

  /// Send notification for advisory
  Future<void> _sendAdvisoryNotification(TravelAdvisory advisory) async {
    try {
      await _notificationService.showNotification(
        title: '${advisory.severity.displayName} - ${advisory.countryName}',
        body: advisory.title,
        payload: {
          'type': 'travel_advisory',
          'advisoryId': advisory.id,
          'countryCode': advisory.countryCode,
          'severity': advisory.severity.toString().split('.').last,
        },
      );

      _loggingService.debug(
          _logTag, 'Sent notification for advisory: ${advisory.id}');
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to send advisory notification', e, stackTrace);
    }
  }

  /// Refresh all data from Firestore
  Future<void> _refreshAllData() async {
    try {
      _loggingService.debug(_logTag, 'Refreshing all advisory data');

      // Refresh advisories
      final advisories = await _fetchAdvisoriesFromFirestore();
      await _cacheAdvisories(advisories);
      _advisoryUpdateController.add(advisories);

      // Refresh document changes
      final documentChanges = await _fetchDocumentChangesFromFirestore();
      await _cacheDocumentChanges(documentChanges);
      _documentChangesController.add(documentChanges);

      _loggingService.debug(
          _logTag, 'All advisory data refreshed successfully');
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to refresh all data', e, stackTrace);
    }
  }

  /// Fetch advisories from Firestore
  Future<List<TravelAdvisory>> _fetchAdvisoriesFromFirestore({
    List<String>? countryCodes,
    List<AdvisoryType>? types,
    List<AdvisorySeverity>? severities,
    bool activeOnly = true,
  }) async {
    try {
      Query query = _firestore.collection('travel_advisories');

      if (activeOnly) {
        query = query.where('status', isEqualTo: 'active');
      }

      if (countryCodes != null && countryCodes.isNotEmpty) {
        query = query.where('countryCode', whereIn: countryCodes);
      }

      final snapshot = await query
          .orderBy('severity', descending: true)
          .orderBy('issuedAt', descending: true)
          .get();

      final advisories = snapshot.docs
          .map((doc) => TravelAdvisory.fromJson(
              {...doc.data() as Map<String, dynamic>, 'id': doc.id}))
          .where((advisory) {
        if (types != null &&
            types.isNotEmpty &&
            !types.contains(advisory.type)) {
          return false;
        }
        if (severities != null &&
            severities.isNotEmpty &&
            !severities.contains(advisory.severity)) {
          return false;
        }
        return activeOnly ? advisory.isActive : true;
      }).toList();

      await _cacheAdvisories(advisories);
      return advisories;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to fetch advisories from Firestore', e, stackTrace);
      rethrow;
    }
  }

  /// Fetch document changes from Firestore
  Future<List<DocumentRequirementChange>> _fetchDocumentChangesFromFirestore({
    List<String>? countryCodes,
    bool urgentOnly = false,
    bool upcomingOnly = false,
  }) async {
    try {
      Query query = _firestore.collection('document_requirement_changes');

      if (countryCodes != null && countryCodes.isNotEmpty) {
        query = query.where('countryCode', whereIn: countryCodes);
      }

      if (urgentOnly) {
        query = query.where('isUrgent', isEqualTo: true);
      }

      final snapshot = await query
          .orderBy('effectiveDate', descending: false)
          .orderBy('announcedAt', descending: true)
          .get();

      final changes = snapshot.docs
          .map((doc) => DocumentRequirementChange.fromJson(
              {...doc.data() as Map<String, dynamic>, 'id': doc.id}))
          .where((change) {
        if (upcomingOnly && !change.isUpcoming) return false;
        return true;
      }).toList();

      await _cacheDocumentChanges(changes);
      return changes;
    } catch (e, stackTrace) {
      _loggingService.error(_logTag,
          'Failed to fetch document changes from Firestore', e, stackTrace);
      rethrow;
    }
  }

  /// Get filtered cached advisories
  List<TravelAdvisory> _getFilteredCachedAdvisories({
    List<String>? countryCodes,
    List<AdvisoryType>? types,
    List<AdvisorySeverity>? severities,
    bool activeOnly = true,
  }) {
    return _cachedAdvisories.where((advisory) {
      if (activeOnly && !advisory.isActive) return false;
      if (countryCodes != null &&
          countryCodes.isNotEmpty &&
          !countryCodes.contains(advisory.countryCode)) {
        return false;
      }
      if (types != null && types.isNotEmpty && !types.contains(advisory.type)) {
        return false;
      }
      if (severities != null &&
          severities.isNotEmpty &&
          !severities.contains(advisory.severity)) {
        return false;
      }
      return true;
    }).toList();
  }

  /// Get filtered cached document changes
  List<DocumentRequirementChange> _getFilteredCachedDocumentChanges({
    List<String>? countryCodes,
    bool urgentOnly = false,
    bool upcomingOnly = false,
  }) {
    return _cachedDocumentChanges.where((change) {
      if (countryCodes != null &&
          countryCodes.isNotEmpty &&
          !countryCodes.contains(change.countryCode)) {
        return false;
      }
      if (urgentOnly && !change.isUrgent) return false;
      if (upcomingOnly && !change.isUpcoming) return false;
      return true;
    }).toList();
  }

  /// Cache advisories to SharedPreferences
  Future<void> _cacheAdvisories(List<TravelAdvisory> advisories) async {
    try {
      _cachedAdvisories = advisories;
      final advisoriesJson =
          jsonEncode(advisories.map((a) => a.toJson()).toList());
      await _prefs?.setString('${_cachePrefix}advisories', advisoriesJson);
      await _prefs?.setInt('${_cachePrefix}advisories_timestamp',
          DateTime.now().millisecondsSinceEpoch);
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to cache advisories', e, stackTrace);
    }
  }

  /// Cache document changes to SharedPreferences
  Future<void> _cacheDocumentChanges(
      List<DocumentRequirementChange> changes) async {
    try {
      _cachedDocumentChanges = changes;
      final changesJson = jsonEncode(changes.map((c) => c.toJson()).toList());
      await _prefs?.setString(_documentChangesCacheKey, changesJson);
      await _prefs?.setInt('${_documentChangesCacheKey}_timestamp',
          DateTime.now().millisecondsSinceEpoch);
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to cache document changes', e, stackTrace);
    }
  }

  /// Cache subscriptions to SharedPreferences
  Future<void> _cacheSubscriptions() async {
    try {
      final subscriptionsJson = jsonEncode(_cachedSubscriptions
          .map((key, value) => MapEntry(key, value.toJson())));
      await _prefs?.setString(_subscriptionCacheKey, subscriptionsJson);
      await _prefs?.setInt('${_subscriptionCacheKey}_timestamp',
          DateTime.now().millisecondsSinceEpoch);
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to cache subscriptions', e, stackTrace);
    }
  }

  /// Check if cache is stale
  bool _isCacheStale(String cacheKey) {
    final timestamp = _prefs?.getInt('${cacheKey}_timestamp');
    if (timestamp == null) return true;

    final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final now = DateTime.now();

    Duration expiration;
    if (cacheKey.contains('advisories')) {
      expiration = _advisoryCacheExpiration;
    } else if (cacheKey.contains('subscriptions')) {
      expiration = _subscriptionCacheExpiration;
    } else {
      expiration = _documentChangesCacheExpiration;
    }

    return now.difference(cacheTime) > expiration;
  }

  /// Stream of advisory updates
  Stream<List<TravelAdvisory>> get advisoryUpdates =>
      _advisoryUpdateController.stream;

  /// Stream of document changes updates
  Stream<List<DocumentRequirementChange>> get documentChangesUpdates =>
      _documentChangesController.stream;

  /// Stream of subscription updates
  Stream<AdvisorySubscription> get subscriptionUpdates =>
      _subscriptionUpdateController.stream;

  /// Check if device is online
  bool get isOnline => _isOnline;

  /// Get cached advisories count
  int get cachedAdvisoriesCount => _cachedAdvisories.length;

  /// Get cached document changes count
  int get cachedDocumentChangesCount => _cachedDocumentChanges.length;

  /// Get cached subscriptions count
  int get cachedSubscriptionsCount => _cachedSubscriptions.length;

  /// Manually refresh advisories for specific countries
  Future<List<TravelAdvisory>> refreshAdvisoriesForCountries(
      List<String> countryCodes) async {
    try {
      _loggingService.info(_logTag,
          'Manually refreshing advisories for countries: $countryCodes');

      final advisories =
          await _fetchAdvisoriesFromFirestore(countryCodes: countryCodes);
      _advisoryUpdateController.add(advisories);

      return advisories;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to refresh advisories for countries', e, stackTrace);
      return _getFilteredCachedAdvisories(countryCodes: countryCodes);
    }
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    try {
      _loggingService.info(_logTag, 'Clearing all cached advisory data');

      // Clear in-memory cache
      _cachedAdvisories.clear();
      _cachedDocumentChanges.clear();
      _cachedSubscriptions.clear();

      // Clear SharedPreferences cache
      await _prefs?.remove('${_cachePrefix}advisories');
      await _prefs?.remove('${_cachePrefix}advisories_timestamp');
      await _prefs?.remove(_documentChangesCacheKey);
      await _prefs?.remove('${_documentChangesCacheKey}_timestamp');
      await _prefs?.remove(_subscriptionCacheKey);
      await _prefs?.remove('${_subscriptionCacheKey}_timestamp');

      _loggingService.info(_logTag, 'All cached advisory data cleared');
    } catch (e, stackTrace) {
      _loggingService.error(_logTag, 'Failed to clear cache', e, stackTrace);
    }
  }

  /// Dispose of resources
  void dispose() {
    _loggingService.info(_logTag, 'Disposing travel advisory service');

    // Cancel timers
    _backgroundRefreshTimer?.cancel();
    _criticalAdvisoryTimer?.cancel();

    // Cancel connectivity subscription
    _connectivitySubscription?.cancel();

    // Close stream controllers
    _advisoryUpdateController.close();
    _documentChangesController.close();
    _subscriptionUpdateController.close();
  }
}

/// Provider for advisories by country codes
final advisoriesByCountriesProvider =
    FutureProvider.family<List<TravelAdvisory>, List<String>>(
        (ref, countryCodes) {
  final service = ref.watch(travelAdvisoryServiceProvider);
  return service.getAdvisoriesForCountries(countryCodes);
});

/// Provider for all active advisories
final allActiveAdvisoriesProvider = FutureProvider<List<TravelAdvisory>>((ref) {
  final service = ref.watch(travelAdvisoryServiceProvider);
  return service.getAllActiveAdvisories();
});

/// Provider for document changes by country codes
final documentChangesByCountriesProvider =
    FutureProvider.family<List<DocumentRequirementChange>, List<String>>(
        (ref, countryCodes) {
  final service = ref.watch(travelAdvisoryServiceProvider);
  return service.getDocumentChangesForCountries(countryCodes);
});

/// Provider for user's advisory subscriptions
final userAdvisorySubscriptionsProvider =
    FutureProvider.family<List<AdvisorySubscription>, String>((ref, userId) {
  final service = ref.watch(travelAdvisoryServiceProvider);
  return service.getUserSubscriptions(userId);
});

/// Provider for advisory updates stream
final advisoryUpdatesProvider = StreamProvider<List<TravelAdvisory>>((ref) {
  final service = ref.watch(travelAdvisoryServiceProvider);
  return service.advisoryUpdates;
});

/// Provider for document changes updates stream
final documentChangesUpdatesProvider =
    StreamProvider<List<DocumentRequirementChange>>((ref) {
  final service = ref.watch(travelAdvisoryServiceProvider);
  return service.documentChangesUpdates;
});

/// Provider for subscription updates stream
final subscriptionUpdatesProvider = StreamProvider<AdvisorySubscription>((ref) {
  final service = ref.watch(travelAdvisoryServiceProvider);
  return service.subscriptionUpdates;
});
