import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/visa/visa_models.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/services/enhanced_payment_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/providers/enhanced_payment_provider.dart';

/// Service for handling visa-related payment operations
class VisaPaymentService {
  final EnhancedPaymentService _paymentService;
  final LoggingService _loggingService;

  VisaPaymentService({
    required EnhancedPaymentService paymentService,
    required LoggingService loggingService,
  })  : _paymentService = paymentService,
        _loggingService = loggingService;

  /// Process payment for visa application
  Future<VisaPaymentResult> processVisaApplicationPayment({
    required BuildContext context,
    required VisaApplication application,
    required String userEmail,
    required String userName,
    String? userPhone,
    PaymentMethodType? preferredMethod,
  }) async {
    try {
      _loggingService.info(
        'VisaPaymentService',
        'Processing visa application payment',
        {
          'applicationId': application.id,
          'amount': application.totalFees,
          'currency': application.currency,
          'preferredMethod': preferredMethod?.name,
        },
      );

      // Create booking object for payment processing
      final booking = _createBookingFromApplication(application);

      // Process payment using enhanced payment service
      final paymentResult = await _paymentService.processPayment(
        context: context,
        booking: booking,
        userEmail: userEmail,
        userName: userName,
        userPhone: userPhone,
        preferredMethod: preferredMethod,
      );

      if (paymentResult.success) {
        // Update application status
        final updatedApplication = application.copyWith(
          paymentStatus: VisaPaymentStatus.paid,
          paymentReference: paymentResult.transactionReference,
          paidAt: DateTime.now(),
        );

        _loggingService.info(
          'VisaPaymentService',
          'Visa application payment successful',
          {
            'applicationId': application.id,
            'transactionReference': paymentResult.transactionReference,
            'amount': application.totalFees,
          },
        );

        return VisaPaymentResult(
          success: true,
          application: updatedApplication,
          transactionReference: paymentResult.transactionReference,
          receiptUrl: paymentResult.receiptId,
        );
      } else {
        _loggingService.warning(
          'VisaPaymentService',
          'Visa application payment failed',
          {
            'applicationId': application.id,
            'error': paymentResult.error,
          },
        );

        return VisaPaymentResult(
          success: false,
          application: application,
          errorMessage: paymentResult.error,
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaPaymentService',
        'Error processing visa application payment',
        {
          'applicationId': application.id,
          'error': e.toString(),
        },
        stackTrace,
      );

      return VisaPaymentResult(
        success: false,
        application: application,
        errorMessage: 'Payment processing failed: $e',
      );
    }
  }

  /// Process payment for visa service provider
  Future<VisaPaymentResult> processServiceProviderPayment({
    required BuildContext context,
    required VisaServiceProvider provider,
    required VisaServiceBooking booking,
    required String userEmail,
    required String userName,
    String? userPhone,
    PaymentMethodType? preferredMethod,
  }) async {
    try {
      _loggingService.info(
        'VisaPaymentService',
        'Processing service provider payment',
        {
          'providerId': provider.id,
          'bookingId': booking.id,
          'amount': booking.totalAmount,
          'currency': booking.currency,
        },
      );

      // Create booking object for payment processing
      final paymentBooking =
          _createBookingFromServiceBooking(booking, provider);

      // Process payment using enhanced payment service
      final paymentResult = await _paymentService.processPayment(
        context: context,
        booking: paymentBooking,
        userEmail: userEmail,
        userName: userName,
        userPhone: userPhone,
        preferredMethod: preferredMethod,
      );

      if (paymentResult.success) {
        // Update booking status
        final updatedBooking = booking.copyWith(
          status: VisaServiceBookingStatus.confirmed,
          paymentReference: paymentResult.transactionReference,
          paidAt: DateTime.now(),
        );

        _loggingService.info(
          'VisaPaymentService',
          'Service provider payment successful',
          {
            'providerId': provider.id,
            'bookingId': booking.id,
            'transactionReference': paymentResult.transactionReference,
          },
        );

        return VisaPaymentResult(
          success: true,
          serviceBooking: updatedBooking,
          transactionReference: paymentResult.transactionReference,
          receiptUrl: paymentResult.receiptId,
        );
      } else {
        _loggingService.warning(
          'VisaPaymentService',
          'Service provider payment failed',
          {
            'providerId': provider.id,
            'bookingId': booking.id,
            'error': paymentResult.error,
          },
        );

        return VisaPaymentResult(
          success: false,
          serviceBooking: booking,
          errorMessage: paymentResult.error,
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaPaymentService',
        'Error processing service provider payment',
        {
          'providerId': provider.id,
          'bookingId': booking.id,
          'error': e.toString(),
        },
        stackTrace,
      );

      return VisaPaymentResult(
        success: false,
        serviceBooking: booking,
        errorMessage: 'Payment processing failed: $e',
      );
    }
  }

  /// Process payment for document verification service
  Future<VisaPaymentResult> processDocumentVerificationPayment({
    required BuildContext context,
    required DocumentVerificationService service,
    required String userEmail,
    required String userName,
    String? userPhone,
    PaymentMethodType? preferredMethod,
  }) async {
    try {
      _loggingService.info(
        'VisaPaymentService',
        'Processing document verification payment',
        {
          'serviceId': service.id,
          'amount': service.price,
          'currency': service.currency,
        },
      );

      // Create booking object for payment processing
      final booking = _createBookingFromDocumentService(service);

      // Process payment using enhanced payment service
      final paymentResult = await _paymentService.processPayment(
        context: context,
        booking: booking,
        userEmail: userEmail,
        userName: userName,
        userPhone: userPhone,
        preferredMethod: preferredMethod,
      );

      if (paymentResult.success) {
        _loggingService.info(
          'VisaPaymentService',
          'Document verification payment successful',
          {
            'serviceId': service.id,
            'transactionReference': paymentResult.transactionReference,
          },
        );

        return VisaPaymentResult(
          success: true,
          transactionReference: paymentResult.transactionReference,
          receiptUrl: paymentResult.receiptId,
        );
      } else {
        _loggingService.warning(
          'VisaPaymentService',
          'Document verification payment failed',
          {
            'serviceId': service.id,
            'error': paymentResult.error,
          },
        );

        return VisaPaymentResult(
          success: false,
          errorMessage: paymentResult.error,
        );
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaPaymentService',
        'Error processing document verification payment',
        {
          'serviceId': service.id,
          'error': e.toString(),
        },
        stackTrace,
      );

      return VisaPaymentResult(
        success: false,
        errorMessage: 'Payment processing failed: $e',
      );
    }
  }

  /// Get payment methods available for visa services
  List<PaymentMethodType> getAvailablePaymentMethods() {
    return [
      PaymentMethodType.card,
      PaymentMethodType.bankTransfer,
      PaymentMethodType.mobileMoney,
      PaymentMethodType.crypto,
    ];
  }

  /// Calculate total fees for visa application
  double calculateApplicationFees({
    required VisaApplication application,
    bool includeServiceFee = true,
    bool includeProcessingFee = true,
  }) {
    double total = application.baseFee;

    if (includeServiceFee && application.serviceFee > 0) {
      total += application.serviceFee;
    }

    if (includeProcessingFee && application.processingFee > 0) {
      total += application.processingFee;
    }

    return total;
  }

  /// Create booking object from visa application
  Booking _createBookingFromApplication(VisaApplication application) {
    final now = DateTime.now();
    return Booking(
      id: 'visa_app_${application.id}',
      experienceId: 'visa_application',
      date: now,
      timeSlot: TimeSlot(
        startTime: now,
        endTime: now.add(const Duration(hours: 1)),
      ),
      participantCount: 1,
      totalAmount: application.totalFees,
      status: BookingStatus.pending,
      specialRequirements:
          'Visa application for ${application.destinationCountry}',
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Create booking object from service booking
  Booking _createBookingFromServiceBooking(
      VisaServiceBooking serviceBooking, VisaServiceProvider provider) {
    final now = DateTime.now();
    return Booking(
      id: 'visa_service_${serviceBooking.id}',
      experienceId: 'visa_service',
      date: serviceBooking.bookingDate,
      timeSlot: TimeSlot(
        startTime: serviceBooking.bookingDate,
        endTime: serviceBooking.bookingDate.add(const Duration(hours: 2)),
      ),
      participantCount: 1,
      totalAmount: serviceBooking.totalAmount,
      status: BookingStatus.pending,
      specialRequirements: '${provider.name} - ${serviceBooking.serviceName}',
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Create booking object from document verification service
  Booking _createBookingFromDocumentService(
      DocumentVerificationService service) {
    final now = DateTime.now();
    return Booking(
      id: 'doc_verification_${service.id}',
      experienceId: 'document_verification',
      date: now,
      timeSlot: TimeSlot(
        startTime: now,
        endTime: now.add(const Duration(hours: 1)),
      ),
      participantCount: 1,
      totalAmount: service.price,
      status: BookingStatus.pending,
      specialRequirements: 'Document Verification - ${service.name}',
      createdAt: now,
      updatedAt: now,
    );
  }
}

/// Result of visa payment processing
class VisaPaymentResult {
  final bool success;
  final VisaApplication? application;
  final VisaServiceBooking? serviceBooking;
  final String? transactionReference;
  final String? receiptUrl;
  final String? errorMessage;

  const VisaPaymentResult({
    required this.success,
    this.application,
    this.serviceBooking,
    this.transactionReference,
    this.receiptUrl,
    this.errorMessage,
  });
}

/// Provider for visa payment service
final visaPaymentServiceProvider = Provider<VisaPaymentService>((ref) {
  final paymentService = ref.watch(enhancedPaymentServiceProvider);
  final loggingService = ref.watch(loggingServiceProvider);

  return VisaPaymentService(
    paymentService: paymentService,
    loggingService: loggingService,
  );
});
