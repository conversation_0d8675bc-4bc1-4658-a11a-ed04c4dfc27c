// Dart imports
import 'dart:convert';

// Package imports
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/appointment/embassy_appointment_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/notification_service.dart';
import 'package:culture_connect/services/notification_service_extension.dart';

/// Provider for the embassy appointment service
final embassyAppointmentServiceProvider =
    Provider<EmbassyAppointmentService>((ref) {
  return EmbassyAppointmentService(
    firestore: FirebaseFirestore.instance,
    loggingService: ref.read(loggingServiceProvider),
    notificationService: NotificationService(),
  );
});

/// Service for managing embassy appointments
class EmbassyAppointmentService {
  static const String _logTag = 'EmbassyAppointmentService';

  final FirebaseFirestore _firestore;
  final LoggingService _loggingService;
  final NotificationService _notificationService;

  const EmbassyAppointmentService({
    required FirebaseFirestore firestore,
    required LoggingService loggingService,
    required NotificationService notificationService,
  })  : _firestore = firestore,
        _loggingService = loggingService,
        _notificationService = notificationService;

  /// Get embassy availability for a specific date range
  Future<List<EmbassyAvailability>> getEmbassyAvailability({
    required String embassyId,
    required DateTime startDate,
    required DateTime endDate,
    List<AppointmentType>? appointmentTypes,
  }) async {
    try {
      _loggingService.info(_logTag,
          'Getting embassy availability for $embassyId from $startDate to $endDate');

      // Query Firestore for embassy availability
      final query = _firestore
          .collection('embassy_availability')
          .where('embassyId', isEqualTo: embassyId)
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('date');

      final snapshot = await query.get();

      final availabilities = snapshot.docs.map((doc) {
        final data = doc.data();
        return EmbassyAvailability.fromJson(data);
      }).toList();

      // Filter by appointment types if specified
      if (appointmentTypes != null && appointmentTypes.isNotEmpty) {
        return availabilities.map((availability) {
          final filteredSlots = availability.timeSlots.where((slot) {
            return slot.allowedTypes
                .any((type) => appointmentTypes.contains(type));
          }).toList();

          return EmbassyAvailability(
            embassyId: availability.embassyId,
            date: availability.date,
            timeSlots: filteredSlots,
            notes: availability.notes,
            isClosed: availability.isClosed,
            closureReason: availability.closureReason,
          );
        }).toList();
      }

      _loggingService.info(
          _logTag, 'Found ${availabilities.length} availability records');
      return availabilities;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to get embassy availability', e, stackTrace);
      return [];
    }
  }

  /// Book an embassy appointment
  Future<EmbassyAppointment?> bookAppointment({
    required String userId,
    required String applicationId,
    required String embassyId,
    required AppointmentType type,
    required DateTime scheduledDateTime,
    required Duration duration,
    required String purpose,
    required List<String> requiredDocuments,
    String? notes,
  }) async {
    try {
      _loggingService.info(_logTag,
          'Booking appointment for user $userId at embassy $embassyId');

      // Check availability first
      final isAvailable = await _checkTimeSlotAvailability(
        embassyId: embassyId,
        dateTime: scheduledDateTime,
        duration: duration,
        appointmentType: type,
      );

      if (!isAvailable) {
        _loggingService.warning(_logTag, 'Time slot not available for booking');
        return null;
      }

      // Generate appointment ID
      final appointmentId = DateTime.now().millisecondsSinceEpoch.toString();
      final now = DateTime.now();

      // Create appointment
      final appointment = EmbassyAppointment(
        id: appointmentId,
        userId: userId,
        applicationId: applicationId,
        embassyId: embassyId,
        type: type,
        status: AppointmentStatus.scheduled,
        scheduledDateTime: scheduledDateTime,
        duration: duration,
        purpose: purpose,
        requiredDocuments: requiredDocuments,
        notes: notes,
        confirmationNumber: _generateConfirmationNumber(),
        cancellationReason: null,
        rescheduleHistory: const [],
        reminders: _createDefaultReminders(appointmentId, scheduledDateTime),
        createdAt: now,
        updatedAt: now,
      );

      // Save to Firestore
      await _firestore
          .collection('embassy_appointments')
          .doc(appointmentId)
          .set(appointment.toJson());

      // Update availability (reduce available slots)
      await _updateAvailabilityAfterBooking(
        embassyId: embassyId,
        dateTime: scheduledDateTime,
        duration: duration,
      );

      // Schedule reminder notifications
      await _scheduleAppointmentReminders(appointment);

      _loggingService.info(
          _logTag, 'Successfully booked appointment $appointmentId');
      return appointment;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to book appointment', e, stackTrace);
      return null;
    }
  }

  /// Get user's appointments
  Future<List<EmbassyAppointment>> getUserAppointments({
    required String userId,
    AppointmentStatus? status,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      _loggingService.info(_logTag, 'Getting appointments for user $userId');

      var query = _firestore
          .collection('embassy_appointments')
          .where('userId', isEqualTo: userId);

      // Add status filter if specified
      if (status != null) {
        query =
            query.where('status', isEqualTo: status.toString().split('.').last);
      }

      // Add date range filters if specified
      if (fromDate != null) {
        query = query.where('scheduledDateTime',
            isGreaterThanOrEqualTo: Timestamp.fromDate(fromDate));
      }
      if (toDate != null) {
        query = query.where('scheduledDateTime',
            isLessThanOrEqualTo: Timestamp.fromDate(toDate));
      }

      query = query.orderBy('scheduledDateTime', descending: false);

      final snapshot = await query.get();

      final appointments = snapshot.docs.map((doc) {
        final data = doc.data();
        return EmbassyAppointment.fromJson(data);
      }).toList();

      _loggingService.info(
          _logTag, 'Found ${appointments.length} appointments for user');
      return appointments;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to get user appointments', e, stackTrace);
      return [];
    }
  }

  /// Cancel an appointment
  Future<bool> cancelAppointment({
    required String appointmentId,
    required String cancellationReason,
  }) async {
    try {
      _loggingService.info(_logTag, 'Cancelling appointment $appointmentId');

      // Get current appointment
      final doc = await _firestore
          .collection('embassy_appointments')
          .doc(appointmentId)
          .get();

      if (!doc.exists) {
        _loggingService.warning(
            _logTag, 'Appointment not found: $appointmentId');
        return false;
      }

      final appointment = EmbassyAppointment.fromJson(doc.data()!);

      if (!appointment.canBeCancelled) {
        _loggingService.warning(
            _logTag, 'Appointment cannot be cancelled: ${appointment.status}');
        return false;
      }

      // Update appointment status
      final updatedAppointment = appointment.copyWith(
        status: AppointmentStatus.cancelled,
        cancellationReason: cancellationReason,
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('embassy_appointments')
          .doc(appointmentId)
          .update(updatedAppointment.toJson());

      // Restore availability
      await _restoreAvailabilityAfterCancellation(
        embassyId: appointment.embassyId,
        dateTime: appointment.scheduledDateTime,
        duration: appointment.duration,
      );

      // Cancel reminder notifications
      await _cancelAppointmentReminders(appointment);

      _loggingService.info(
          _logTag, 'Successfully cancelled appointment $appointmentId');
      return true;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to cancel appointment', e, stackTrace);
      return false;
    }
  }

  /// Reschedule an appointment
  Future<EmbassyAppointment?> rescheduleAppointment({
    required String appointmentId,
    required DateTime newDateTime,
    required String reason,
  }) async {
    try {
      _loggingService.info(
          _logTag, 'Rescheduling appointment $appointmentId to $newDateTime');

      // Get current appointment
      final doc = await _firestore
          .collection('embassy_appointments')
          .doc(appointmentId)
          .get();

      if (!doc.exists) {
        _loggingService.warning(
            _logTag, 'Appointment not found: $appointmentId');
        return null;
      }

      final appointment = EmbassyAppointment.fromJson(doc.data()!);

      if (!appointment.canBeRescheduled) {
        _loggingService.warning(_logTag,
            'Appointment cannot be rescheduled: ${appointment.status}');
        return null;
      }

      // Check new time slot availability
      final isAvailable = await _checkTimeSlotAvailability(
        embassyId: appointment.embassyId,
        dateTime: newDateTime,
        duration: appointment.duration,
        appointmentType: appointment.type,
      );

      if (!isAvailable) {
        _loggingService.warning(
            _logTag, 'New time slot not available for rescheduling');
        return null;
      }

      // Create reschedule record
      final reschedule = AppointmentReschedule(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        originalDateTime: appointment.scheduledDateTime,
        newDateTime: newDateTime,
        reason: reason,
        initiatedBy: 'user',
        rescheduledAt: DateTime.now(),
      );

      // Update appointment
      final updatedAppointment = appointment.copyWith(
        scheduledDateTime: newDateTime,
        status: AppointmentStatus.rescheduled,
        rescheduleHistory: [...appointment.rescheduleHistory, reschedule],
        reminders: _createDefaultReminders(appointmentId, newDateTime),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('embassy_appointments')
          .doc(appointmentId)
          .update(updatedAppointment.toJson());

      // Update availability (restore old slot, book new slot)
      await _restoreAvailabilityAfterCancellation(
        embassyId: appointment.embassyId,
        dateTime: appointment.scheduledDateTime,
        duration: appointment.duration,
      );

      await _updateAvailabilityAfterBooking(
        embassyId: appointment.embassyId,
        dateTime: newDateTime,
        duration: appointment.duration,
      );

      // Cancel old reminders and schedule new ones
      await _cancelAppointmentReminders(appointment);
      await _scheduleAppointmentReminders(updatedAppointment);

      _loggingService.info(
          _logTag, 'Successfully rescheduled appointment $appointmentId');
      return updatedAppointment;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to reschedule appointment', e, stackTrace);
      return null;
    }
  }

  /// Check if a time slot is available for booking
  Future<bool> _checkTimeSlotAvailability({
    required String embassyId,
    required DateTime dateTime,
    required Duration duration,
    required AppointmentType appointmentType,
  }) async {
    try {
      // Get availability for the specific date
      final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
      final availabilityDoc = await _firestore
          .collection('embassy_availability')
          .where('embassyId', isEqualTo: embassyId)
          .where('date', isEqualTo: Timestamp.fromDate(date))
          .limit(1)
          .get();

      if (availabilityDoc.docs.isEmpty) {
        return false;
      }

      final availability =
          EmbassyAvailability.fromJson(availabilityDoc.docs.first.data());

      if (availability.isClosed) {
        return false;
      }

      // Find matching time slot
      final endTime = dateTime.add(duration);
      for (final slot in availability.timeSlots) {
        if (slot.startTime.isBefore(dateTime.add(const Duration(minutes: 1))) &&
            slot.endTime
                .isAfter(endTime.subtract(const Duration(minutes: 1))) &&
            slot.allowedTypes.contains(appointmentType) &&
            slot.hasAvailability) {
          return true;
        }
      }

      return false;
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to check time slot availability', e, stackTrace);
      return false;
    }
  }

  /// Update availability after booking an appointment
  Future<void> _updateAvailabilityAfterBooking({
    required String embassyId,
    required DateTime dateTime,
    required Duration duration,
  }) async {
    try {
      final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
      final availabilityQuery = await _firestore
          .collection('embassy_availability')
          .where('embassyId', isEqualTo: embassyId)
          .where('date', isEqualTo: Timestamp.fromDate(date))
          .limit(1)
          .get();

      if (availabilityQuery.docs.isEmpty) return;

      final doc = availabilityQuery.docs.first;
      final availability = EmbassyAvailability.fromJson(doc.data());

      // Update the matching time slot
      final endTime = dateTime.add(duration);
      final updatedSlots = availability.timeSlots.map((slot) {
        if (slot.startTime.isBefore(dateTime.add(const Duration(minutes: 1))) &&
            slot.endTime
                .isAfter(endTime.subtract(const Duration(minutes: 1)))) {
          return slot.copyWith(bookedAppointments: slot.bookedAppointments + 1);
        }
        return slot;
      }).toList();

      await doc.reference.update({
        'timeSlots': updatedSlots.map((slot) => slot.toJson()).toList(),
      });
    } catch (e, stackTrace) {
      _loggingService.error(_logTag,
          'Failed to update availability after booking', e, stackTrace);
    }
  }

  /// Restore availability after cancelling an appointment
  Future<void> _restoreAvailabilityAfterCancellation({
    required String embassyId,
    required DateTime dateTime,
    required Duration duration,
  }) async {
    try {
      final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
      final availabilityQuery = await _firestore
          .collection('embassy_availability')
          .where('embassyId', isEqualTo: embassyId)
          .where('date', isEqualTo: Timestamp.fromDate(date))
          .limit(1)
          .get();

      if (availabilityQuery.docs.isEmpty) return;

      final doc = availabilityQuery.docs.first;
      final availability = EmbassyAvailability.fromJson(doc.data());

      // Update the matching time slot
      final endTime = dateTime.add(duration);
      final updatedSlots = availability.timeSlots.map((slot) {
        if (slot.startTime.isBefore(dateTime.add(const Duration(minutes: 1))) &&
            slot.endTime
                .isAfter(endTime.subtract(const Duration(minutes: 1)))) {
          return slot.copyWith(
              bookedAppointments:
                  (slot.bookedAppointments - 1).clamp(0, slot.maxAppointments));
        }
        return slot;
      }).toList();

      await doc.reference.update({
        'timeSlots': updatedSlots.map((slot) => slot.toJson()).toList(),
      });
    } catch (e, stackTrace) {
      _loggingService.error(_logTag,
          'Failed to restore availability after cancellation', e, stackTrace);
    }
  }

  /// Generate a confirmation number for the appointment
  String _generateConfirmationNumber() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'EMB$random';
  }

  /// Create default reminder notifications for an appointment
  List<AppointmentReminder> _createDefaultReminders(
      String appointmentId, DateTime scheduledDateTime) {
    return [
      AppointmentReminder(
        id: '${appointmentId}_24h',
        beforeAppointment: const Duration(hours: 24),
        message: 'Your embassy appointment is tomorrow',
        isSent: false,
        notificationMethod: 'push',
      ),
      AppointmentReminder(
        id: '${appointmentId}_2h',
        beforeAppointment: const Duration(hours: 2),
        message: 'Your embassy appointment is in 2 hours',
        isSent: false,
        notificationMethod: 'push',
      ),
      AppointmentReminder(
        id: '${appointmentId}_30m',
        beforeAppointment: const Duration(minutes: 30),
        message: 'Your embassy appointment is in 30 minutes',
        isSent: false,
        notificationMethod: 'push',
      ),
    ];
  }

  /// Schedule reminder notifications for an appointment
  Future<void> _scheduleAppointmentReminders(
      EmbassyAppointment appointment) async {
    try {
      for (final reminder in appointment.reminders) {
        if (!reminder.isSent) {
          final notificationTime = appointment.scheduledDateTime
              .subtract(reminder.beforeAppointment);

          if (notificationTime.isAfter(DateTime.now())) {
            await _notificationService.scheduleNotification(
              id: reminder.id,
              title: 'Embassy Appointment Reminder',
              body: reminder.message,
              scheduledDate: notificationTime,
              payload: jsonEncode({
                'type': 'embassy_appointment',
                'appointmentId': appointment.id,
                'reminderId': reminder.id,
              }),
            );
          }
        }
      }
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to schedule appointment reminders', e, stackTrace);
    }
  }

  /// Cancel reminder notifications for an appointment
  Future<void> _cancelAppointmentReminders(
      EmbassyAppointment appointment) async {
    try {
      for (final reminder in appointment.reminders) {
        if (!reminder.isSent) {
          await _notificationService.cancelNotification(reminder.id);
        }
      }
    } catch (e, stackTrace) {
      _loggingService.error(
          _logTag, 'Failed to cancel appointment reminders', e, stackTrace);
    }
  }
}
