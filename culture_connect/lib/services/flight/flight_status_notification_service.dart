// Flutter imports
import 'dart:async';
import 'dart:convert';

// Package imports
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/models/travel/flight/flight_info.dart';
import 'package:culture_connect/services/flight/flight_management_service.dart';
import 'package:culture_connect/services/notification_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for managing flight status notifications and real-time updates
class FlightStatusNotificationService {
  static const String _trackedFlightsKey = 'tracked_flights';
  static const Duration _statusCheckInterval = Duration(minutes: 5);
  static const Duration _criticalStatusCheckInterval = Duration(minutes: 2);

  final FlightManagementService _flightService;
  final NotificationService _notificationService;
  final LoggingService _loggingService;
  final SharedPreferences _prefs;

  Timer? _statusCheckTimer;
  final Map<String, FlightInfo> _lastKnownStatus = {};
  final Map<String, Timer> _criticalFlightTimers = {};
  bool _isInitialized = false;

  FlightStatusNotificationService({
    required FlightManagementService flightService,
    required NotificationService notificationService,
    required LoggingService loggingService,
    required SharedPreferences prefs,
  })  : _flightService = flightService,
        _notificationService = notificationService,
        _loggingService = loggingService,
        _prefs = prefs;

  /// Initialize the flight status notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _notificationService.initialize();
      await _loadTrackedFlights();
      _startStatusMonitoring();
      _isInitialized = true;

      _loggingService.info(
        'FlightStatusNotificationService',
        'Service initialized successfully',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'FlightStatusNotificationService',
        'Failed to initialize service',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Add a flight for status monitoring
  Future<void> trackFlight({
    required String flightNumber,
    required DateTime departureDate,
    required String bookingReference,
    FlightNotificationSettings? settings,
  }) async {
    try {
      final flightKey = _getFlightKey(flightNumber, departureDate);
      final trackedFlights = await _getTrackedFlights();

      final flightTracking = TrackedFlight(
        flightNumber: flightNumber,
        departureDate: departureDate,
        bookingReference: bookingReference,
        settings: settings ?? FlightNotificationSettings.defaultSettings(),
        addedAt: DateTime.now(),
      );

      trackedFlights[flightKey] = flightTracking;
      await _saveTrackedFlights(trackedFlights);

      // Get initial status
      final initialStatus = await _flightService.getFlightStatus(
        flightNumber,
        departureDate,
      );

      if (initialStatus != null) {
        _lastKnownStatus[flightKey] = initialStatus;

        // Set up critical monitoring if flight is within 24 hours
        if (_isFlightCritical(initialStatus)) {
          _setupCriticalMonitoring(flightKey);
        }
      }

      _loggingService.info(
        'FlightStatusNotificationService',
        'Started tracking flight: $flightNumber',
        {'bookingReference': bookingReference},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'FlightStatusNotificationService',
        'Failed to track flight',
        e,
        stackTrace,
      );
    }
  }

  /// Remove a flight from status monitoring
  Future<void> stopTrackingFlight(
      String flightNumber, DateTime departureDate) async {
    try {
      final flightKey = _getFlightKey(flightNumber, departureDate);
      final trackedFlights = await _getTrackedFlights();

      trackedFlights.remove(flightKey);
      await _saveTrackedFlights(trackedFlights);

      _lastKnownStatus.remove(flightKey);
      _criticalFlightTimers[flightKey]?.cancel();
      _criticalFlightTimers.remove(flightKey);

      _loggingService.info(
        'FlightStatusNotificationService',
        'Stopped tracking flight: $flightNumber',
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'FlightStatusNotificationService',
        'Failed to stop tracking flight',
        e,
        stackTrace,
      );
    }
  }

  /// Update notification settings for a specific flight
  Future<void> updateFlightNotificationSettings({
    required String flightNumber,
    required DateTime departureDate,
    required FlightNotificationSettings settings,
  }) async {
    try {
      final flightKey = _getFlightKey(flightNumber, departureDate);
      final trackedFlights = await _getTrackedFlights();

      if (trackedFlights.containsKey(flightKey)) {
        trackedFlights[flightKey] = trackedFlights[flightKey]!.copyWith(
          settings: settings,
        );
        await _saveTrackedFlights(trackedFlights);
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'FlightStatusNotificationService',
        'Failed to update notification settings',
        e,
        stackTrace,
      );
    }
  }

  /// Get current notification settings for a flight
  Future<FlightNotificationSettings?> getFlightNotificationSettings(
    String flightNumber,
    DateTime departureDate,
  ) async {
    try {
      final flightKey = _getFlightKey(flightNumber, departureDate);
      final trackedFlights = await _getTrackedFlights();
      return trackedFlights[flightKey]?.settings;
    } catch (e) {
      _loggingService.error(
        'FlightStatusNotificationService',
        'Failed to get notification settings',
        e,
      );
      return null;
    }
  }

  /// Start monitoring flight status changes
  void _startStatusMonitoring() {
    _statusCheckTimer?.cancel();
    _statusCheckTimer = Timer.periodic(_statusCheckInterval, (_) {
      _checkAllFlightStatuses();
    });
  }

  /// Check status for all tracked flights
  Future<void> _checkAllFlightStatuses() async {
    try {
      final trackedFlights = await _getTrackedFlights();

      for (final entry in trackedFlights.entries) {
        final flightKey = entry.key;
        final trackedFlight = entry.value;

        await _checkFlightStatus(flightKey, trackedFlight);
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'FlightStatusNotificationService',
        'Error checking flight statuses',
        e,
        stackTrace,
      );
    }
  }

  /// Check status for a specific flight
  Future<void> _checkFlightStatus(
      String flightKey, TrackedFlight trackedFlight) async {
    try {
      final currentStatus = await _flightService.getFlightStatus(
        trackedFlight.flightNumber,
        trackedFlight.departureDate,
      );

      if (currentStatus == null) return;

      final previousStatus = _lastKnownStatus[flightKey];

      if (previousStatus != null &&
          _hasStatusChanged(previousStatus, currentStatus)) {
        await _handleStatusChange(trackedFlight, previousStatus, currentStatus);
      }

      _lastKnownStatus[flightKey] = currentStatus;

      // Setup or update critical monitoring
      if (_isFlightCritical(currentStatus)) {
        _setupCriticalMonitoring(flightKey);
      } else {
        _criticalFlightTimers[flightKey]?.cancel();
        _criticalFlightTimers.remove(flightKey);
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'FlightStatusNotificationService',
        'Error checking flight status for ${trackedFlight.flightNumber}',
        e,
        stackTrace,
      );
    }
  }

  /// Handle flight status changes and send notifications
  Future<void> _handleStatusChange(
    TrackedFlight trackedFlight,
    FlightInfo previousStatus,
    FlightInfo currentStatus,
  ) async {
    final settings = trackedFlight.settings;

    // Status change notification
    if (settings.statusChanges &&
        previousStatus.status != currentStatus.status) {
      await _sendStatusChangeNotification(
          trackedFlight, previousStatus, currentStatus);
    }

    // Gate change notification
    if (settings.gateChanges &&
        previousStatus.departureGate != currentStatus.departureGate &&
        currentStatus.departureGate != null) {
      await _sendGateChangeNotification(trackedFlight, currentStatus);
    }

    // Delay notification
    if (settings.statusChanges &&
        currentStatus.status == FlightStatus.delayed &&
        previousStatus.status != FlightStatus.delayed) {
      await _sendDelayNotification(trackedFlight, currentStatus);
    }

    // Boarding notification
    if (settings.boarding &&
        currentStatus.status == FlightStatus.boarding &&
        previousStatus.status != FlightStatus.boarding) {
      await _sendBoardingNotification(trackedFlight, currentStatus);
    }
  }

  /// Send status change notification
  Future<void> _sendStatusChangeNotification(
    TrackedFlight trackedFlight,
    FlightInfo previousStatus,
    FlightInfo currentStatus,
  ) async {
    final title = 'Flight ${trackedFlight.flightNumber} Status Update';
    final body =
        'Status changed from ${previousStatus.status.displayName} to ${currentStatus.status.displayName}';

    await _notificationService.showNotification(
      title: title,
      body: body,
      payload: {
        'type': 'flight_status_change',
        'flightNumber': trackedFlight.flightNumber,
        'bookingReference': trackedFlight.bookingReference,
        'newStatus': currentStatus.status.name,
      },
    );
  }

  /// Send gate change notification
  Future<void> _sendGateChangeNotification(
    TrackedFlight trackedFlight,
    FlightInfo currentStatus,
  ) async {
    final title = 'Gate Change - Flight ${trackedFlight.flightNumber}';
    final body = 'New departure gate: ${currentStatus.departureGate}';

    await _notificationService.showNotification(
      title: title,
      body: body,
      payload: {
        'type': 'gate_change',
        'flightNumber': trackedFlight.flightNumber,
        'bookingReference': trackedFlight.bookingReference,
        'gate': currentStatus.departureGate,
      },
    );
  }

  /// Send delay notification
  Future<void> _sendDelayNotification(
    TrackedFlight trackedFlight,
    FlightInfo currentStatus,
  ) async {
    final delayText = currentStatus.delayMinutes != null
        ? '${currentStatus.delayMinutes} minutes'
        : 'Unknown duration';

    final title = 'Flight Delayed - ${trackedFlight.flightNumber}';
    final body = 'Your flight is delayed by $delayText';

    await _notificationService.showNotification(
      title: title,
      body: body,
      payload: {
        'type': 'flight_delay',
        'flightNumber': trackedFlight.flightNumber,
        'bookingReference': trackedFlight.bookingReference,
        'delayMinutes': currentStatus.delayMinutes?.toString(),
      },
    );
  }

  /// Send boarding notification
  Future<void> _sendBoardingNotification(
    TrackedFlight trackedFlight,
    FlightInfo currentStatus,
  ) async {
    final title = 'Now Boarding - Flight ${trackedFlight.flightNumber}';
    final gateInfo = currentStatus.departureGate != null
        ? ' at gate ${currentStatus.departureGate}'
        : '';
    final body = 'Your flight is now boarding$gateInfo';

    await _notificationService.showNotification(
      title: title,
      body: body,
      payload: {
        'type': 'boarding',
        'flightNumber': trackedFlight.flightNumber,
        'bookingReference': trackedFlight.bookingReference,
        'gate': currentStatus.departureGate,
      },
    );
  }

  /// Setup critical monitoring for flights within 24 hours
  void _setupCriticalMonitoring(String flightKey) {
    _criticalFlightTimers[flightKey]?.cancel();
    _criticalFlightTimers[flightKey] = Timer.periodic(
      _criticalStatusCheckInterval,
      (_) => _checkCriticalFlightStatus(flightKey),
    );
  }

  /// Check status for critical flights more frequently
  Future<void> _checkCriticalFlightStatus(String flightKey) async {
    try {
      final trackedFlights = await _getTrackedFlights();
      final trackedFlight = trackedFlights[flightKey];

      if (trackedFlight != null) {
        await _checkFlightStatus(flightKey, trackedFlight);
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'FlightStatusNotificationService',
        'Error in critical flight status check',
        e,
        stackTrace,
      );
    }
  }

  /// Check if flight status has changed significantly
  bool _hasStatusChanged(FlightInfo previous, FlightInfo current) {
    return previous.status != current.status ||
        previous.departureGate != current.departureGate ||
        previous.delayMinutes != current.delayMinutes ||
        previous.actualDepartureTime != current.actualDepartureTime;
  }

  /// Check if flight is within critical time window (24 hours)
  bool _isFlightCritical(FlightInfo flightInfo) {
    final now = DateTime.now();
    final departureTime =
        flightInfo.actualDepartureTime ?? flightInfo.scheduledDepartureTime;
    final timeDifference = departureTime.difference(now);

    return timeDifference.inHours <= 24 && timeDifference.inHours >= -2;
  }

  /// Generate flight key for tracking
  String _getFlightKey(String flightNumber, DateTime departureDate) {
    return '${flightNumber}_${departureDate.year}-${departureDate.month}-${departureDate.day}';
  }

  /// Load tracked flights from storage
  Future<void> _loadTrackedFlights() async {
    try {
      final trackedFlights = await _getTrackedFlights();

      // Load last known statuses
      for (final entry in trackedFlights.entries) {
        final flightKey = entry.key;
        final trackedFlight = entry.value;

        final status = await _flightService.getFlightStatus(
          trackedFlight.flightNumber,
          trackedFlight.departureDate,
        );

        if (status != null) {
          _lastKnownStatus[flightKey] = status;
        }
      }
    } catch (e, stackTrace) {
      _loggingService.error(
        'FlightStatusNotificationService',
        'Error loading tracked flights',
        e,
        stackTrace,
      );
    }
  }

  /// Get tracked flights from storage
  Future<Map<String, TrackedFlight>> _getTrackedFlights() async {
    try {
      final data = _prefs.getString(_trackedFlightsKey);
      if (data == null) return {};

      final Map<String, dynamic> json = jsonDecode(data);
      return json.map((key, value) => MapEntry(
            key,
            TrackedFlight.fromJson(value as Map<String, dynamic>),
          ));
    } catch (e) {
      _loggingService.error(
        'FlightStatusNotificationService',
        'Error getting tracked flights',
        e,
      );
      return {};
    }
  }

  /// Save tracked flights to storage
  Future<void> _saveTrackedFlights(
      Map<String, TrackedFlight> trackedFlights) async {
    try {
      final json =
          trackedFlights.map((key, value) => MapEntry(key, value.toJson()));
      await _prefs.setString(_trackedFlightsKey, jsonEncode(json));
    } catch (e, stackTrace) {
      _loggingService.error(
        'FlightStatusNotificationService',
        'Error saving tracked flights',
        e,
        stackTrace,
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _statusCheckTimer?.cancel();
    for (final timer in _criticalFlightTimers.values) {
      timer.cancel();
    }
    _criticalFlightTimers.clear();
    _lastKnownStatus.clear();
    _isInitialized = false;
  }
}

/// Model for flight notification settings
class FlightNotificationSettings {
  final bool notificationsEnabled;
  final bool statusChanges;
  final bool gateChanges;
  final bool boarding;
  final bool departure;
  final bool arrival;
  final int reminderMinutes;

  const FlightNotificationSettings({
    required this.notificationsEnabled,
    required this.statusChanges,
    required this.gateChanges,
    required this.boarding,
    required this.departure,
    required this.arrival,
    required this.reminderMinutes,
  });

  /// Default notification settings
  factory FlightNotificationSettings.defaultSettings() {
    return const FlightNotificationSettings(
      notificationsEnabled: true,
      statusChanges: true,
      gateChanges: true,
      boarding: true,
      departure: false,
      arrival: false,
      reminderMinutes: 60,
    );
  }

  /// Create from JSON
  factory FlightNotificationSettings.fromJson(Map<String, dynamic> json) {
    return FlightNotificationSettings(
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      statusChanges: json['statusChanges'] as bool? ?? true,
      gateChanges: json['gateChanges'] as bool? ?? true,
      boarding: json['boarding'] as bool? ?? true,
      departure: json['departure'] as bool? ?? false,
      arrival: json['arrival'] as bool? ?? false,
      reminderMinutes: json['reminderMinutes'] as int? ?? 60,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'notificationsEnabled': notificationsEnabled,
      'statusChanges': statusChanges,
      'gateChanges': gateChanges,
      'boarding': boarding,
      'departure': departure,
      'arrival': arrival,
      'reminderMinutes': reminderMinutes,
    };
  }

  /// Copy with modifications
  FlightNotificationSettings copyWith({
    bool? notificationsEnabled,
    bool? statusChanges,
    bool? gateChanges,
    bool? boarding,
    bool? departure,
    bool? arrival,
    int? reminderMinutes,
  }) {
    return FlightNotificationSettings(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      statusChanges: statusChanges ?? this.statusChanges,
      gateChanges: gateChanges ?? this.gateChanges,
      boarding: boarding ?? this.boarding,
      departure: departure ?? this.departure,
      arrival: arrival ?? this.arrival,
      reminderMinutes: reminderMinutes ?? this.reminderMinutes,
    );
  }
}

/// Model for tracked flight information
class TrackedFlight {
  final String flightNumber;
  final DateTime departureDate;
  final String bookingReference;
  final FlightNotificationSettings settings;
  final DateTime addedAt;

  const TrackedFlight({
    required this.flightNumber,
    required this.departureDate,
    required this.bookingReference,
    required this.settings,
    required this.addedAt,
  });

  /// Create from JSON
  factory TrackedFlight.fromJson(Map<String, dynamic> json) {
    return TrackedFlight(
      flightNumber: json['flightNumber'] as String,
      departureDate: DateTime.parse(json['departureDate'] as String),
      bookingReference: json['bookingReference'] as String,
      settings: FlightNotificationSettings.fromJson(
        json['settings'] as Map<String, dynamic>,
      ),
      addedAt: DateTime.parse(json['addedAt'] as String),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'flightNumber': flightNumber,
      'departureDate': departureDate.toIso8601String(),
      'bookingReference': bookingReference,
      'settings': settings.toJson(),
      'addedAt': addedAt.toIso8601String(),
    };
  }

  /// Copy with modifications
  TrackedFlight copyWith({
    String? flightNumber,
    DateTime? departureDate,
    String? bookingReference,
    FlightNotificationSettings? settings,
    DateTime? addedAt,
  }) {
    return TrackedFlight(
      flightNumber: flightNumber ?? this.flightNumber,
      departureDate: departureDate ?? this.departureDate,
      bookingReference: bookingReference ?? this.bookingReference,
      settings: settings ?? this.settings,
      addedAt: addedAt ?? this.addedAt,
    );
  }
}
