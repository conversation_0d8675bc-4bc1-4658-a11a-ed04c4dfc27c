import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for managing interactive features and animations
class InteractiveFeaturesService {
  final LoggingService _loggingService;

  InteractiveFeaturesService(this._loggingService);

  /// Trigger contextual haptic feedback based on interaction type
  Future<void> triggerHapticFeedback(HapticFeedbackType type) async {
    try {
      switch (type) {
        case HapticFeedbackType.light:
          await HapticFeedback.lightImpact();
          break;
        case HapticFeedbackType.medium:
          await HapticFeedback.mediumImpact();
          break;
        case HapticFeedbackType.heavy:
          await HapticFeedback.heavyImpact();
          break;
        case HapticFeedbackType.selection:
          await HapticFeedback.selectionClick();
          break;
        case HapticFeedbackType.vibrate:
          await HapticFeedback.vibrate();
          break;
      }
    } catch (e) {
      _loggingService.warning(
        'InteractiveFeaturesService',
        'Failed to trigger haptic feedback: $e',
      );
    }
  }

  /// Trigger haptic feedback for button interactions
  Future<void> triggerButtonFeedback({
    required ButtonType buttonType,
    required ButtonAction action,
  }) async {
    HapticFeedbackType feedbackType;

    switch (buttonType) {
      case ButtonType.primary:
        feedbackType = action == ButtonAction.press
            ? HapticFeedbackType.medium
            : HapticFeedbackType.light;
        break;
      case ButtonType.secondary:
        feedbackType = HapticFeedbackType.light;
        break;
      case ButtonType.destructive:
        feedbackType = action == ButtonAction.press
            ? HapticFeedbackType.heavy
            : HapticFeedbackType.medium;
        break;
      case ButtonType.floating:
        feedbackType = HapticFeedbackType.medium;
        break;
    }

    await triggerHapticFeedback(feedbackType);
  }

  /// Trigger haptic feedback for gesture interactions
  Future<void> triggerGestureFeedback(GestureType gestureType) async {
    HapticFeedbackType feedbackType;

    switch (gestureType) {
      case GestureType.swipe:
        feedbackType = HapticFeedbackType.light;
        break;
      case GestureType.longPress:
        feedbackType = HapticFeedbackType.medium;
        break;
      case GestureType.pullToRefresh:
        feedbackType = HapticFeedbackType.medium;
        break;
      case GestureType.pinch:
        feedbackType = HapticFeedbackType.light;
        break;
      case GestureType.doubleTap:
        feedbackType = HapticFeedbackType.selection;
        break;
    }

    await triggerHapticFeedback(feedbackType);
  }

  /// Trigger haptic feedback for success/error states
  Future<void> triggerStateFeedback(StateType stateType) async {
    HapticFeedbackType feedbackType;

    switch (stateType) {
      case StateType.success:
        feedbackType = HapticFeedbackType.medium;
        break;
      case StateType.error:
        feedbackType = HapticFeedbackType.heavy;
        break;
      case StateType.warning:
        feedbackType = HapticFeedbackType.light;
        break;
      case StateType.loading:
        feedbackType = HapticFeedbackType.selection;
        break;
    }

    await triggerHapticFeedback(feedbackType);
  }
}

/// Types of haptic feedback
enum HapticFeedbackType {
  light,
  medium,
  heavy,
  selection,
  vibrate,
}

/// Types of buttons for contextual feedback
enum ButtonType {
  primary,
  secondary,
  destructive,
  floating,
}

/// Button actions
enum ButtonAction {
  press,
  release,
}

/// Types of gestures
enum GestureType {
  swipe,
  longPress,
  pullToRefresh,
  pinch,
  doubleTap,
}

/// Types of states
enum StateType {
  success,
  error,
  warning,
  loading,
}

/// Provider for interactive features service
final interactiveFeaturesServiceProvider = Provider<InteractiveFeaturesService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  return InteractiveFeaturesService(loggingService);
});
