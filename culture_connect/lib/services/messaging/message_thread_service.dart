import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/messaging/message_thread_model.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for managing message threading and replies
class MessageThreadService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LoggingService _loggingService = LoggingService();

  bool _isInitialized = false;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _loggingService.info('MessageThreadService', 'Initializing service');
      _isInitialized = true;
    } catch (e) {
      _loggingService.error('MessageThreadService', 'Failed to initialize: $e');
      rethrow;
    }
  }

  /// Create a new thread for a message
  Future<MessageThreadModel?> createThread({
    required String parentMessageId,
    required String chatId,
  }) async {
    await initialize();

    try {
      final threadId = DateTime.now().millisecondsSinceEpoch.toString();
      final now = DateTime.now();

      final thread = MessageThreadModel(
        id: threadId,
        parentMessageId: parentMessageId,
        chatId: chatId,
        replyMessageIds: [],
        replyCount: 0,
        createdAt: now,
        lastReplyAt: now,
        lastReplyUserId: '',
      );

      await _firestore
          .collection('message_threads')
          .doc(threadId)
          .set(thread.toJson());

      _loggingService.info(
        'MessageThreadService',
        'Thread created',
        {'threadId': threadId, 'parentMessageId': parentMessageId},
      );

      return thread;
    } catch (e) {
      _loggingService.error(
        'MessageThreadService',
        'Failed to create thread',
        {'parentMessageId': parentMessageId, 'error': e.toString()},
      );
      return null;
    }
  }

  /// Add a reply to a thread
  Future<MessageReplyModel?> addReply({
    required String threadId,
    required String parentMessageId,
    required String replyToMessageId,
    required String chatId,
    required String senderId,
    required String text,
    required MessageType type,
    String? mediaUrl,
    Map<String, dynamic>? metadata,
  }) async {
    await initialize();

    try {
      final replyId = DateTime.now().millisecondsSinceEpoch.toString();
      final now = DateTime.now();

      final reply = MessageReplyModel(
        id: replyId,
        threadId: threadId,
        parentMessageId: parentMessageId,
        replyToMessageId: replyToMessageId,
        chatId: chatId,
        senderId: senderId,
        text: text,
        timestamp: now,
        status: MessageStatus.sent,
        type: type,
        mediaUrl: mediaUrl,
        metadata: metadata,
      );

      // Add reply to collection
      await _firestore
          .collection('message_replies')
          .doc(replyId)
          .set(reply.toJson());

      // Update thread with new reply
      await _firestore.collection('message_threads').doc(threadId).update({
        'replyMessageIds': FieldValue.arrayUnion([replyId]),
        'replyCount': FieldValue.increment(1),
        'lastReplyAt': Timestamp.fromDate(now),
        'lastReplyUserId': senderId,
      });

      _loggingService.info(
        'MessageThreadService',
        'Reply added to thread',
        {'threadId': threadId, 'replyId': replyId},
      );

      return reply;
    } catch (e) {
      _loggingService.error(
        'MessageThreadService',
        'Failed to add reply',
        {'threadId': threadId, 'error': e.toString()},
      );
      return null;
    }
  }

  /// Get thread for a message
  Future<MessageThreadModel?> getThread(String parentMessageId) async {
    await initialize();

    try {
      final querySnapshot = await _firestore
          .collection('message_threads')
          .where('parentMessageId', isEqualTo: parentMessageId)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) return null;

      final doc = querySnapshot.docs.first;
      return MessageThreadModel.fromJson({
        ...doc.data(),
        'id': doc.id,
      });
    } catch (e) {
      _loggingService.error(
        'MessageThreadService',
        'Failed to get thread',
        {'parentMessageId': parentMessageId, 'error': e.toString()},
      );
      return null;
    }
  }

  /// Get replies for a thread
  Future<List<MessageReplyModel>> getThreadReplies(String threadId) async {
    await initialize();

    try {
      final querySnapshot = await _firestore
          .collection('message_replies')
          .where('threadId', isEqualTo: threadId)
          .orderBy('timestamp', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => MessageReplyModel.fromJson({
                ...doc.data(),
                'id': doc.id,
              }))
          .toList();
    } catch (e) {
      _loggingService.error(
        'MessageThreadService',
        'Failed to get thread replies',
        {'threadId': threadId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// Stream thread replies
  Stream<List<MessageReplyModel>> streamThreadReplies(String threadId) {
    return _firestore
        .collection('message_replies')
        .where('threadId', isEqualTo: threadId)
        .orderBy('timestamp', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => MessageReplyModel.fromJson({
                  ...doc.data(),
                  'id': doc.id,
                }))
            .toList());
  }

  /// Delete a reply
  Future<bool> deleteReply(String replyId) async {
    await initialize();

    try {
      // Get reply to update thread count
      final replyDoc =
          await _firestore.collection('message_replies').doc(replyId).get();

      if (!replyDoc.exists) return false;

      final reply = MessageReplyModel.fromJson({
        ...replyDoc.data()!,
        'id': replyDoc.id,
      });

      // Delete reply
      await _firestore.collection('message_replies').doc(replyId).delete();

      // Update thread count
      await _firestore
          .collection('message_threads')
          .doc(reply.threadId)
          .update({
        'replyMessageIds': FieldValue.arrayRemove([replyId]),
        'replyCount': FieldValue.increment(-1),
      });

      _loggingService.info(
        'MessageThreadService',
        'Reply deleted',
        {'replyId': replyId, 'threadId': reply.threadId},
      );

      return true;
    } catch (e) {
      _loggingService.error(
        'MessageThreadService',
        'Failed to delete reply',
        {'replyId': replyId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// Edit a reply
  Future<bool> editReply({
    required String replyId,
    required String newText,
    String? editReason,
  }) async {
    await initialize();

    try {
      await _firestore.collection('message_replies').doc(replyId).update({
        'text': newText,
        'isEdited': true,
        'editedAt': Timestamp.fromDate(DateTime.now()),
        'metadata.editReason': editReason,
      });

      _loggingService.info(
        'MessageThreadService',
        'Reply edited',
        {'replyId': replyId},
      );

      return true;
    } catch (e) {
      _loggingService.error(
        'MessageThreadService',
        'Failed to edit reply',
        {'replyId': replyId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// Get thread statistics
  Future<Map<String, dynamic>> getThreadStats(String threadId) async {
    await initialize();

    try {
      final thread =
          await _firestore.collection('message_threads').doc(threadId).get();

      if (!thread.exists) {
        return {'exists': false};
      }

      final threadData = MessageThreadModel.fromJson({
        ...thread.data()!,
        'id': thread.id,
      });

      final replies = await getThreadReplies(threadId);
      final uniqueParticipants = replies.map((r) => r.senderId).toSet().length;

      return {
        'exists': true,
        'replyCount': threadData.replyCount,
        'uniqueParticipants': uniqueParticipants,
        'lastReplyAt': threadData.lastReplyAt,
        'createdAt': threadData.createdAt,
      };
    } catch (e) {
      _loggingService.error(
        'MessageThreadService',
        'Failed to get thread stats',
        {'threadId': threadId, 'error': e.toString()},
      );
      return {'exists': false, 'error': e.toString()};
    }
  }

  /// Clean up old threads (maintenance)
  Future<void> cleanupOldThreads({int daysOld = 30}) async {
    await initialize();

    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));

      final oldThreads = await _firestore
          .collection('message_threads')
          .where('lastReplyAt', isLessThan: Timestamp.fromDate(cutoffDate))
          .where('isActive', isEqualTo: false)
          .get();

      final batch = _firestore.batch();

      for (final doc in oldThreads.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();

      _loggingService.info(
        'MessageThreadService',
        'Cleaned up old threads',
        {'count': oldThreads.docs.length, 'daysOld': daysOld},
      );
    } catch (e) {
      _loggingService.error(
        'MessageThreadService',
        'Failed to cleanup old threads',
        {'error': e.toString()},
      );
    }
  }

  /// Check if message has thread
  Future<bool> hasThread(String parentMessageId) async {
    await initialize();

    try {
      final querySnapshot = await _firestore
          .collection('message_threads')
          .where('parentMessageId', isEqualTo: parentMessageId)
          .limit(1)
          .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      _loggingService.error(
        'MessageThreadService',
        'Failed to check thread existence',
        {'parentMessageId': parentMessageId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// Get thread count for message
  Future<int> getThreadReplyCount(String parentMessageId) async {
    await initialize();

    try {
      final thread = await getThread(parentMessageId);
      return thread?.replyCount ?? 0;
    } catch (e) {
      _loggingService.error(
        'MessageThreadService',
        'Failed to get thread reply count',
        {'parentMessageId': parentMessageId, 'error': e.toString()},
      );
      return 0;
    }
  }
}
