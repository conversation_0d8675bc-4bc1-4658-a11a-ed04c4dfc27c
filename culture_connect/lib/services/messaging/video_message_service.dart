import 'dart:io';
import 'dart:async';
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:camera/camera.dart';
import 'package:video_player/video_player.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/messaging/video_message_model.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for handling video messages in chat
class VideoMessageService {
  static final VideoMessageService _instance = VideoMessageService._internal();
  factory VideoMessageService() => _instance;
  VideoMessageService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final LoggingService _loggingService = LoggingService();

  CameraController? _cameraController;
  VideoPlayerController? _videoPlayerController;

  bool _isInitialized = false;
  bool _isRecording = false;
  Timer? _recordingTimer;
  Duration _recordingDuration = Duration.zero;

  // Stream controllers
  final StreamController<VideoPlaybackState> _playbackStateController =
      StreamController<VideoPlaybackState>.broadcast();
  final StreamController<Duration> _recordingDurationController =
      StreamController<Duration>.broadcast();
  final StreamController<bool> _recordingStateController =
      StreamController<bool>.broadcast();

  // Getters for streams
  Stream<VideoPlaybackState> get playbackStateStream =>
      _playbackStateController.stream;
  Stream<Duration> get recordingDurationStream =>
      _recordingDurationController.stream;
  Stream<bool> get recordingStateStream => _recordingStateController.stream;

  /// Initialize the service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Get available cameras
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        _loggingService.warning('VideoMessageService', 'No cameras available');
        return false;
      }

      // Initialize camera controller with front camera (preferred for messaging)
      final frontCamera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.front,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        frontCamera,
        ResolutionPreset.medium,
        enableAudio: true,
      );

      await _cameraController!.initialize();
      _isInitialized = true;

      _loggingService.info('VideoMessageService', 'Initialized successfully');
      return true;
    } catch (e) {
      _loggingService.error('VideoMessageService', 'Failed to initialize: $e');
      return false;
    }
  }

  /// Start recording a video message
  Future<String?> startRecording({
    VideoRecordingConfig config = const VideoRecordingConfig(),
  }) async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return null;
    }

    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      _loggingService.error('VideoMessageService', 'Camera not initialized');
      return null;
    }

    try {
      if (_isRecording) {
        _loggingService.warning('VideoMessageService', 'Already recording');
        return null;
      }

      // Start recording
      await _cameraController!.startVideoRecording();
      _isRecording = true;
      _recordingDuration = Duration.zero;

      // Notify recording state
      _recordingStateController.add(true);

      // Start recording timer
      _startRecordingTimer(config.maxDuration);

      _loggingService.info('VideoMessageService', 'Started video recording');
      return 'recording'; // Placeholder path
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to start recording: $e');
      _isRecording = false;
      _recordingStateController.add(false);
      return null;
    }
  }

  /// Stop recording and return the file path
  Future<String?> stopRecording() async {
    if (!_isRecording || _cameraController == null) {
      _loggingService.warning('VideoMessageService', 'Not currently recording');
      return null;
    }

    try {
      // Stop recording
      final videoFile = await _cameraController!.stopVideoRecording();
      _isRecording = false;

      // Stop timer
      _recordingTimer?.cancel();
      _recordingTimer = null;

      // Notify recording state
      _recordingStateController.add(false);

      _loggingService.info(
          'VideoMessageService', 'Stopped recording: ${videoFile.path}');
      return videoFile.path;
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to stop recording: $e');
      _isRecording = false;
      _recordingStateController.add(false);
      return null;
    }
  }

  /// Upload video message to Firebase Storage
  Future<String?> uploadVideoMessage(String filePath, String messageId) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        _loggingService.error(
            'VideoMessageService', 'File not found: $filePath');
        return null;
      }

      // Create storage reference
      final ref = _storage.ref().child('video_messages/$messageId.mp4');

      // Upload file
      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;

      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();

      _loggingService.info(
          'VideoMessageService', 'Uploaded video message: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to upload video message: $e');
      return null;
    }
  }

  /// Generate and upload thumbnail
  Future<String?> generateAndUploadThumbnail(
      String videoPath, String messageId) async {
    try {
      // Create temporary video player to generate thumbnail
      final controller = VideoPlayerController.file(File(videoPath));
      await controller.initialize();

      // Seek to middle of video for thumbnail
      final duration = controller.value.duration;
      await controller
          .seekTo(Duration(milliseconds: duration.inMilliseconds ~/ 2));

      // This is a placeholder - in a real implementation, you'd use a proper
      // video thumbnail generation library
      final thumbnailPath =
          await _generateThumbnailPlaceholder(videoPath, messageId);

      await controller.dispose();

      if (thumbnailPath == null) return null;

      // Upload thumbnail
      final file = File(thumbnailPath);
      final ref = _storage.ref().child('video_thumbnails/$messageId.jpg');
      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;

      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to generate thumbnail: $e');
      return null;
    }
  }

  /// Download video message from URL
  Future<String?> downloadVideoMessage(String url, String messageId) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/video_messages/video_$messageId.mp4';

      // Create directory if it doesn't exist
      final file = File(filePath);
      await file.parent.create(recursive: true);

      // Download file
      final ref = _storage.refFromURL(url);
      await ref.writeToFile(file);

      _loggingService.info(
          'VideoMessageService', 'Downloaded video message: $filePath');
      return filePath;
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to download video message: $e');
      return null;
    }
  }

  /// Play video message
  Future<bool> playVideoMessage(VideoMessageModel videoMessage) async {
    try {
      String? videoPath = videoMessage.localPath;

      // Download if not available locally
      if (videoPath == null || !await File(videoPath).exists()) {
        videoPath =
            await downloadVideoMessage(videoMessage.videoUrl, videoMessage.id);
        if (videoPath == null) return false;
      }

      // Dispose previous controller
      await _videoPlayerController?.dispose();

      // Create new video player controller
      _videoPlayerController = VideoPlayerController.file(File(videoPath));
      await _videoPlayerController!.initialize();

      // Start playback
      await _videoPlayerController!.play();

      // Update playback state
      _playbackStateController.add(VideoPlaybackState(
        currentMessageId: videoMessage.id,
        duration: videoMessage.duration,
        isPlaying: true,
      ));

      // Listen to position changes
      _videoPlayerController!.addListener(() {
        _playbackStateController.add(VideoPlaybackState(
          currentMessageId: videoMessage.id,
          position: _videoPlayerController!.value.position,
          duration: videoMessage.duration,
          isPlaying: _videoPlayerController!.value.isPlaying,
          isLoading: _videoPlayerController!.value.isBuffering,
          volume: _videoPlayerController!.value.volume,
        ));
      });

      return true;
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to play video message: $e');
      return false;
    }
  }

  /// Pause video message playback
  Future<void> pauseVideoMessage() async {
    try {
      await _videoPlayerController?.pause();
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to pause video message: $e');
    }
  }

  /// Resume video message playback
  Future<void> resumeVideoMessage() async {
    try {
      await _videoPlayerController?.play();
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to resume video message: $e');
    }
  }

  /// Stop video message playback
  Future<void> stopVideoMessage() async {
    try {
      await _videoPlayerController?.pause();
      await _videoPlayerController?.seekTo(Duration.zero);
      _playbackStateController.add(const VideoPlaybackState());
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to stop video message: $e');
    }
  }

  /// Seek to position in video message
  Future<void> seekToPosition(Duration position) async {
    try {
      await _videoPlayerController?.seekTo(position);
    } catch (e) {
      _loggingService.error('VideoMessageService', 'Failed to seek: $e');
    }
  }

  /// Set video volume
  Future<void> setVolume(double volume) async {
    try {
      await _videoPlayerController?.setVolume(volume.clamp(0.0, 1.0));
    } catch (e) {
      _loggingService.error('VideoMessageService', 'Failed to set volume: $e');
    }
  }

  /// Get video duration from file
  Future<Duration?> getVideoDuration(String filePath) async {
    try {
      final controller = VideoPlayerController.file(File(filePath));
      await controller.initialize();
      final duration = controller.value.duration;
      await controller.dispose();
      return duration;
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to get video duration: $e');
      return null;
    }
  }

  /// Get video resolution from file
  Future<VideoResolution?> getVideoResolution(String filePath) async {
    try {
      final controller = VideoPlayerController.file(File(filePath));
      await controller.initialize();
      final size = controller.value.size;
      await controller.dispose();
      return VideoResolution(
        width: size.width.toInt(),
        height: size.height.toInt(),
      );
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to get video resolution: $e');
      return null;
    }
  }

  /// Get camera controller for preview
  CameraController? get cameraController => _cameraController;

  /// Check if currently recording
  bool get isRecording => _isRecording;

  /// Start recording timer
  void _startRecordingTimer(Duration maxDuration) {
    _recordingTimer =
        Timer.periodic(const Duration(milliseconds: 100), (timer) {
      _recordingDuration =
          Duration(milliseconds: _recordingDuration.inMilliseconds + 100);
      _recordingDurationController.add(_recordingDuration);

      // Stop recording if max duration reached
      if (_recordingDuration >= maxDuration) {
        stopRecording();
      }
    });
  }

  /// Generate placeholder thumbnail (simplified implementation)
  Future<String?> _generateThumbnailPlaceholder(
      String videoPath, String messageId) async {
    try {
      // This is a placeholder implementation
      // In a real app, you'd use a proper video thumbnail generation library
      final directory = await getTemporaryDirectory();
      final thumbnailPath = '${directory.path}/thumbnail_$messageId.jpg';

      // Create a simple placeholder file
      final file = File(thumbnailPath);
      await file.writeAsBytes(
          Uint8List.fromList([0xFF, 0xD8, 0xFF, 0xE0])); // JPEG header

      return thumbnailPath;
    } catch (e) {
      _loggingService.error('VideoMessageService',
          'Failed to generate thumbnail placeholder: $e');
      return null;
    }
  }

  /// Create video message model from recorded file
  Future<VideoMessageModel?> createVideoMessage({
    required String messageId,
    required String filePath,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Get video duration
      final duration = await getVideoDuration(filePath);
      if (duration == null) return null;

      // Get video resolution
      final resolution = await getVideoResolution(filePath) ??
          const VideoResolution(width: 720, height: 1280);

      // Get file size
      final file = File(filePath);
      final fileSize = await file.length();

      // Create video message model
      final videoMessage = VideoMessageModel(
        id: const Uuid().v4(),
        messageId: messageId,
        videoUrl: '', // Will be set after upload
        localPath: filePath,
        duration: duration,
        resolution: resolution,
        fileSize: fileSize,
        status: VideoMessageStatus.pending,
        createdAt: DateTime.now(),
        metadata: metadata,
      );

      return videoMessage;
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to create video message: $e');
      return null;
    }
  }

  /// Save video message to Firestore
  Future<bool> saveVideoMessage(VideoMessageModel videoMessage) async {
    try {
      await _firestore
          .collection('video_messages')
          .doc(videoMessage.id)
          .set(videoMessage.toJson());

      _loggingService.info(
          'VideoMessageService', 'Saved video message: ${videoMessage.id}');
      return true;
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to save video message: $e');
      return false;
    }
  }

  /// Get video message by ID
  Future<VideoMessageModel?> getVideoMessage(String videoMessageId) async {
    try {
      final doc = await _firestore
          .collection('video_messages')
          .doc(videoMessageId)
          .get();

      if (!doc.exists) return null;

      return VideoMessageModel.fromJson(doc.data()!);
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to get video message: $e');
      return null;
    }
  }

  /// Update video message status
  Future<bool> updateVideoMessageStatus(
    String videoMessageId,
    VideoMessageStatus status,
  ) async {
    try {
      await _firestore.collection('video_messages').doc(videoMessageId).update({
        'status': status.toString().split('.').last,
      });

      return true;
    } catch (e) {
      _loggingService.error(
          'VideoMessageService', 'Failed to update status: $e');
      return false;
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    try {
      await _cameraController?.dispose();
      await _videoPlayerController?.dispose();
      _recordingTimer?.cancel();
      await _playbackStateController.close();
      await _recordingDurationController.close();
      await _recordingStateController.close();
    } catch (e) {
      _loggingService.error('VideoMessageService', 'Error disposing: $e');
    }
  }
}
