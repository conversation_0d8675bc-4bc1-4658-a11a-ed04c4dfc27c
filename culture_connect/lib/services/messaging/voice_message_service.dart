import 'dart:io';
import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';
import 'package:just_audio/just_audio.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/messaging/voice_message_model.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for handling voice messages in chat
class VoiceMessageService {
  static final VoiceMessageService _instance = VoiceMessageService._internal();
  factory VoiceMessageService() => _instance;
  VoiceMessageService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final LoggingService _loggingService = LoggingService();

  late final AudioRecorder _recorder;
  late final AudioPlayer _player;

  bool _isInitialized = false;
  String? _currentRecordingPath;
  Timer? _recordingTimer;
  Duration _recordingDuration = Duration.zero;

  // Stream controllers
  final StreamController<VoicePlaybackState> _playbackStateController =
      StreamController<VoicePlaybackState>.broadcast();
  final StreamController<Duration> _recordingDurationController =
      StreamController<Duration>.broadcast();
  final StreamController<List<double>> _amplitudeController =
      StreamController<List<double>>.broadcast();

  // Getters for streams
  Stream<VoicePlaybackState> get playbackStateStream =>
      _playbackStateController.stream;
  Stream<Duration> get recordingDurationStream =>
      _recordingDurationController.stream;
  Stream<List<double>> get amplitudeStream => _amplitudeController.stream;

  /// Initialize the service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      _recorder = AudioRecorder();
      _player = AudioPlayer();

      // Check permissions
      if (await _recorder.hasPermission()) {
        _isInitialized = true;
        _loggingService.info('VoiceMessageService', 'Initialized successfully');
        return true;
      } else {
        _loggingService.warning(
            'VoiceMessageService', 'Microphone permission denied');
        return false;
      }
    } catch (e) {
      _loggingService.error('VoiceMessageService', 'Failed to initialize: $e');
      return false;
    }
  }

  /// Start recording a voice message
  Future<String?> startRecording({
    VoiceRecordingConfig config = const VoiceRecordingConfig(),
  }) async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return null;
    }

    try {
      // Check if already recording
      if (await _recorder.isRecording()) {
        _loggingService.warning('VoiceMessageService', 'Already recording');
        return _currentRecordingPath;
      }

      // Create unique file path
      final uuid = const Uuid().v4();
      final directory = await getTemporaryDirectory();
      final path = '${directory.path}/voice_message_$uuid.m4a';

      // Start recording
      await _recorder.start(
        RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: config.bitRate,
          sampleRate: config.sampleRate,
        ),
        path: path,
      );

      _currentRecordingPath = path;
      _recordingDuration = Duration.zero;

      // Start recording timer
      _startRecordingTimer(config.maxDuration);

      // Start amplitude monitoring if enabled
      if (config.enableAmplitude) {
        _startAmplitudeMonitoring();
      }

      _loggingService.info('VoiceMessageService', 'Started recording: $path');
      return path;
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to start recording: $e');
      return null;
    }
  }

  /// Stop recording and return the file path
  Future<String?> stopRecording() async {
    try {
      if (!await _recorder.isRecording()) {
        _loggingService.warning(
            'VoiceMessageService', 'Not currently recording');
        return null;
      }

      // Stop recording
      final path = await _recorder.stop();

      // Stop timers
      _recordingTimer?.cancel();
      _recordingTimer = null;

      _loggingService.info('VoiceMessageService', 'Stopped recording: $path');
      return path;
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to stop recording: $e');
      return null;
    }
  }

  /// Upload voice message to Firebase Storage
  Future<String?> uploadVoiceMessage(String filePath, String messageId) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        _loggingService.error(
            'VoiceMessageService', 'File not found: $filePath');
        return null;
      }

      // Create storage reference
      final ref = _storage.ref().child('voice_messages/$messageId.m4a');

      // Upload file
      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;

      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();

      _loggingService.info(
          'VoiceMessageService', 'Uploaded voice message: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to upload voice message: $e');
      return null;
    }
  }

  /// Download voice message from URL
  Future<String?> downloadVoiceMessage(String url, String messageId) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/voice_messages/voice_$messageId.m4a';

      // Create directory if it doesn't exist
      final file = File(filePath);
      await file.parent.create(recursive: true);

      // Download file
      final ref = _storage.refFromURL(url);
      await ref.writeToFile(file);

      _loggingService.info(
          'VoiceMessageService', 'Downloaded voice message: $filePath');
      return filePath;
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to download voice message: $e');
      return null;
    }
  }

  /// Play voice message
  Future<bool> playVoiceMessage(VoiceMessageModel voiceMessage) async {
    try {
      String? audioPath = voiceMessage.localPath;

      // Download if not available locally
      if (audioPath == null || !await File(audioPath).exists()) {
        audioPath =
            await downloadVoiceMessage(voiceMessage.audioUrl, voiceMessage.id);
        if (audioPath == null) return false;
      }

      // Load and play audio
      await _player.setFilePath(audioPath);
      await _player.play();

      // Update playback state
      _playbackStateController.add(VoicePlaybackState(
        currentMessageId: voiceMessage.id,
        duration: voiceMessage.duration,
        isPlaying: true,
      ));

      // Listen to position changes
      _player.positionStream.listen((position) {
        _playbackStateController.add(VoicePlaybackState(
          currentMessageId: voiceMessage.id,
          position: position,
          duration: voiceMessage.duration,
          isPlaying: _player.playing,
        ));
      });

      // Listen to player state changes
      _player.playerStateStream.listen((state) {
        _playbackStateController.add(VoicePlaybackState(
          currentMessageId: voiceMessage.id,
          position: _player.position,
          duration: voiceMessage.duration,
          isPlaying: state.playing,
          isLoading: state.processingState == ProcessingState.loading,
        ));
      });

      return true;
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to play voice message: $e');
      return false;
    }
  }

  /// Pause voice message playback
  Future<void> pauseVoiceMessage() async {
    try {
      await _player.pause();
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to pause voice message: $e');
    }
  }

  /// Resume voice message playback
  Future<void> resumeVoiceMessage() async {
    try {
      await _player.play();
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to resume voice message: $e');
    }
  }

  /// Stop voice message playback
  Future<void> stopVoiceMessage() async {
    try {
      await _player.stop();
      _playbackStateController.add(const VoicePlaybackState());
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to stop voice message: $e');
    }
  }

  /// Seek to position in voice message
  Future<void> seekToPosition(Duration position) async {
    try {
      await _player.seek(position);
    } catch (e) {
      _loggingService.error('VoiceMessageService', 'Failed to seek: $e');
    }
  }

  /// Set playback speed
  Future<void> setPlaybackSpeed(double speed) async {
    try {
      await _player.setSpeed(speed);
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to set playback speed: $e');
    }
  }

  /// Get audio duration from file
  Future<Duration?> getAudioDuration(String filePath) async {
    try {
      final tempPlayer = AudioPlayer();
      final duration = await tempPlayer.setFilePath(filePath);
      await tempPlayer.dispose();
      return duration;
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to get audio duration: $e');
      return null;
    }
  }

  /// Generate waveform data from audio file
  Future<List<double>?> generateWaveform(String filePath) async {
    try {
      // This is a simplified implementation
      // In a real app, you'd use a proper audio analysis library
      final file = File(filePath);
      final bytes = await file.readAsBytes();

      // Generate mock waveform data based on file size
      final waveform = <double>[];
      final sampleCount = (bytes.length / 1000).clamp(50, 200).toInt();

      for (int i = 0; i < sampleCount; i++) {
        // Generate pseudo-random amplitude values
        final amplitude = (bytes[i % bytes.length] / 255.0) * 0.8 + 0.1;
        waveform.add(amplitude);
      }

      return waveform;
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to generate waveform: $e');
      return null;
    }
  }

  /// Create voice message model from recorded file
  Future<VoiceMessageModel?> createVoiceMessage({
    required String messageId,
    required String filePath,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Get audio duration
      final duration = await getAudioDuration(filePath);
      if (duration == null) return null;

      // Generate waveform
      final waveform = await generateWaveform(filePath);

      // Create voice message model
      final voiceMessage = VoiceMessageModel(
        id: const Uuid().v4(),
        messageId: messageId,
        audioUrl: '', // Will be set after upload
        localPath: filePath,
        duration: duration,
        waveform: waveform,
        status: VoiceMessageStatus.pending,
        createdAt: DateTime.now(),
        metadata: metadata,
      );

      return voiceMessage;
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to create voice message: $e');
      return null;
    }
  }

  /// Save voice message to Firestore
  Future<bool> saveVoiceMessage(VoiceMessageModel voiceMessage) async {
    try {
      await _firestore
          .collection('voice_messages')
          .doc(voiceMessage.id)
          .set(voiceMessage.toJson());

      _loggingService.info(
          'VoiceMessageService', 'Saved voice message: ${voiceMessage.id}');
      return true;
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to save voice message: $e');
      return false;
    }
  }

  /// Get voice message by ID
  Future<VoiceMessageModel?> getVoiceMessage(String voiceMessageId) async {
    try {
      final doc = await _firestore
          .collection('voice_messages')
          .doc(voiceMessageId)
          .get();

      if (!doc.exists) return null;

      return VoiceMessageModel.fromJson(doc.data()!);
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to get voice message: $e');
      return null;
    }
  }

  /// Update voice message status
  Future<bool> updateVoiceMessageStatus(
    String voiceMessageId,
    VoiceMessageStatus status,
  ) async {
    try {
      await _firestore.collection('voice_messages').doc(voiceMessageId).update({
        'status': status.toString().split('.').last,
      });

      return true;
    } catch (e) {
      _loggingService.error(
          'VoiceMessageService', 'Failed to update status: $e');
      return false;
    }
  }

  /// Start recording timer
  void _startRecordingTimer(Duration maxDuration) {
    _recordingTimer =
        Timer.periodic(const Duration(milliseconds: 100), (timer) {
      _recordingDuration =
          Duration(milliseconds: _recordingDuration.inMilliseconds + 100);
      _recordingDurationController.add(_recordingDuration);

      // Stop recording if max duration reached
      if (_recordingDuration >= maxDuration) {
        stopRecording();
      }
    });
  }

  /// Start amplitude monitoring
  void _startAmplitudeMonitoring() {
    Timer.periodic(const Duration(milliseconds: 50), (timer) async {
      if (!await _recorder.isRecording()) {
        timer.cancel();
        return;
      }

      try {
        final amplitude = await _recorder.getAmplitude();
        final amplitudeValue = amplitude.current.clamp(0.0, 1.0);
        _amplitudeController.add([amplitudeValue]);
      } catch (e) {
        // Ignore amplitude errors
      }
    });
  }

  /// Dispose resources
  Future<void> dispose() async {
    try {
      await _recorder.dispose();
      await _player.dispose();
      _recordingTimer?.cancel();
      await _playbackStateController.close();
      await _recordingDurationController.close();
      await _amplitudeController.close();
    } catch (e) {
      _loggingService.error('VoiceMessageService', 'Error disposing: $e');
    }
  }
}
