import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/messaging/message_template_model.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for managing message templates and quick replies
class MessageTemplateService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LoggingService _loggingService = LoggingService();
  
  bool _isInitialized = false;
  List<QuickReplyModel> _defaultQuickReplies = [];

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _loggingService.info('MessageTemplateService', 'Initializing service');
      await _loadDefaultQuickReplies();
      _isInitialized = true;
    } catch (e) {
      _loggingService.error('MessageTemplateService', 'Failed to initialize: $e');
      rethrow;
    }
  }

  /// Load default quick replies
  Future<void> _loadDefaultQuickReplies() async {
    _defaultQuickReplies = [
      // Greeting quick replies
      const QuickReplyModel(
        id: 'greeting_hello',
        text: 'Hello! How can I help you?',
        emoji: '👋',
        category: MessageTemplateCategory.greeting,
        priority: 10,
      ),
      const QuickReplyModel(
        id: 'greeting_welcome',
        text: 'Welcome to CultureConnect!',
        emoji: '🎉',
        category: MessageTemplateCategory.greeting,
        priority: 9,
      ),
      
      // Travel quick replies
      const QuickReplyModel(
        id: 'travel_directions',
        text: 'I can help you with directions',
        emoji: '🗺️',
        category: MessageTemplateCategory.travel,
        priority: 8,
        isContextual: true,
        contextKeywords: ['directions', 'location', 'where', 'how to get'],
      ),
      const QuickReplyModel(
        id: 'travel_recommendations',
        text: 'Let me suggest some local attractions',
        emoji: '🏛️',
        category: MessageTemplateCategory.travel,
        priority: 7,
        isContextual: true,
        contextKeywords: ['attractions', 'places', 'visit', 'see'],
      ),
      
      // Business quick replies
      const QuickReplyModel(
        id: 'business_booking',
        text: 'I can help you make a booking',
        emoji: '📅',
        category: MessageTemplateCategory.business,
        priority: 6,
        isContextual: true,
        contextKeywords: ['book', 'reservation', 'appointment'],
      ),
      
      // General quick replies
      const QuickReplyModel(
        id: 'general_thanks',
        text: 'Thank you!',
        emoji: '🙏',
        category: MessageTemplateCategory.general,
        priority: 5,
      ),
      const QuickReplyModel(
        id: 'general_yes',
        text: 'Yes, please',
        emoji: '✅',
        category: MessageTemplateCategory.general,
        priority: 4,
      ),
      const QuickReplyModel(
        id: 'general_no',
        text: 'No, thank you',
        emoji: '❌',
        category: MessageTemplateCategory.general,
        priority: 3,
      ),
      
      // Farewell quick replies
      const QuickReplyModel(
        id: 'farewell_goodbye',
        text: 'Goodbye! Have a great day!',
        emoji: '👋',
        category: MessageTemplateCategory.farewell,
        priority: 2,
      ),
    ];
  }

  /// Create a new message template
  Future<MessageTemplateModel?> createTemplate({
    required String userId,
    required String name,
    required String content,
    String? description,
    required MessageTemplateCategory category,
    List<String> tags = const [],
    bool isPublic = false,
    Map<String, String>? variables,
    Map<String, dynamic>? metadata,
  }) async {
    await initialize();
    
    try {
      final templateId = DateTime.now().millisecondsSinceEpoch.toString();
      final now = DateTime.now();
      
      final template = MessageTemplateModel(
        id: templateId,
        userId: userId,
        name: name,
        content: content,
        description: description,
        category: category,
        tags: tags,
        createdAt: now,
        lastUsedAt: now,
        isPublic: isPublic,
        variables: variables,
        metadata: metadata,
      );

      await _firestore
          .collection('message_templates')
          .doc(templateId)
          .set(template.toJson());

      _loggingService.info(
        'MessageTemplateService',
        'Template created successfully',
        {'templateId': templateId, 'name': name},
      );

      return template;
    } catch (e) {
      _loggingService.error(
        'MessageTemplateService',
        'Failed to create template',
        {'name': name, 'error': e.toString()},
      );
      return null;
    }
  }

  /// Update a message template
  Future<bool> updateTemplate({
    required String templateId,
    String? name,
    String? content,
    String? description,
    MessageTemplateCategory? category,
    List<String>? tags,
    bool? isActive,
    bool? isPublic,
    Map<String, String>? variables,
    Map<String, dynamic>? metadata,
  }) async {
    await initialize();
    
    try {
      final updateData = <String, dynamic>{};
      
      if (name != null) updateData['name'] = name;
      if (content != null) updateData['content'] = content;
      if (description != null) updateData['description'] = description;
      if (category != null) updateData['category'] = category.toString().split('.').last;
      if (tags != null) updateData['tags'] = tags;
      if (isActive != null) updateData['isActive'] = isActive;
      if (isPublic != null) updateData['isPublic'] = isPublic;
      if (variables != null) updateData['variables'] = variables;
      if (metadata != null) updateData['metadata'] = metadata;

      if (updateData.isEmpty) return true;

      await _firestore
          .collection('message_templates')
          .doc(templateId)
          .update(updateData);

      _loggingService.info(
        'MessageTemplateService',
        'Template updated successfully',
        {'templateId': templateId},
      );

      return true;
    } catch (e) {
      _loggingService.error(
        'MessageTemplateService',
        'Failed to update template',
        {'templateId': templateId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// Delete a message template
  Future<bool> deleteTemplate(String templateId) async {
    await initialize();
    
    try {
      await _firestore
          .collection('message_templates')
          .doc(templateId)
          .delete();

      _loggingService.info(
        'MessageTemplateService',
        'Template deleted successfully',
        {'templateId': templateId},
      );

      return true;
    } catch (e) {
      _loggingService.error(
        'MessageTemplateService',
        'Failed to delete template',
        {'templateId': templateId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// Get user templates
  Future<List<MessageTemplateModel>> getUserTemplates(String userId) async {
    await initialize();
    
    try {
      final querySnapshot = await _firestore
          .collection('message_templates')
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('lastUsedAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => MessageTemplateModel.fromJson({
                ...doc.data(),
                'id': doc.id,
              }))
          .toList();
    } catch (e) {
      _loggingService.error(
        'MessageTemplateService',
        'Failed to get user templates',
        {'userId': userId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// Get templates by category
  Future<List<MessageTemplateModel>> getTemplatesByCategory({
    required String userId,
    required MessageTemplateCategory category,
  }) async {
    await initialize();
    
    try {
      final querySnapshot = await _firestore
          .collection('message_templates')
          .where('userId', isEqualTo: userId)
          .where('category', isEqualTo: category.toString().split('.').last)
          .where('isActive', isEqualTo: true)
          .orderBy('usageCount', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => MessageTemplateModel.fromJson({
                ...doc.data(),
                'id': doc.id,
              }))
          .toList();
    } catch (e) {
      _loggingService.error(
        'MessageTemplateService',
        'Failed to get templates by category',
        {'userId': userId, 'category': category.toString(), 'error': e.toString()},
      );
      return [];
    }
  }

  /// Search templates
  Future<List<MessageTemplateModel>> searchTemplates({
    required String userId,
    required String query,
    MessageTemplateCategory? category,
  }) async {
    await initialize();
    
    try {
      Query<Map<String, dynamic>> queryRef = _firestore
          .collection('message_templates')
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true);

      if (category != null) {
        queryRef = queryRef.where('category', isEqualTo: category.toString().split('.').last);
      }

      final querySnapshot = await queryRef.get();

      // Filter by search query (client-side filtering for now)
      final results = querySnapshot.docs
          .map((doc) => MessageTemplateModel.fromJson({
                ...doc.data(),
                'id': doc.id,
              }))
          .where((template) =>
              template.name.toLowerCase().contains(query.toLowerCase()) ||
              template.content.toLowerCase().contains(query.toLowerCase()) ||
              template.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase())))
          .toList();

      // Sort by relevance (usage count and name match)
      results.sort((a, b) {
        final aNameMatch = a.name.toLowerCase().contains(query.toLowerCase()) ? 1 : 0;
        final bNameMatch = b.name.toLowerCase().contains(query.toLowerCase()) ? 1 : 0;
        
        if (aNameMatch != bNameMatch) {
          return bNameMatch.compareTo(aNameMatch);
        }
        
        return b.usageCount.compareTo(a.usageCount);
      });

      return results;
    } catch (e) {
      _loggingService.error(
        'MessageTemplateService',
        'Failed to search templates',
        {'userId': userId, 'query': query, 'error': e.toString()},
      );
      return [];
    }
  }

  /// Use a template (increment usage count)
  Future<void> useTemplate(String templateId) async {
    await initialize();
    
    try {
      await _firestore
          .collection('message_templates')
          .doc(templateId)
          .update({
        'usageCount': FieldValue.increment(1),
        'lastUsedAt': Timestamp.fromDate(DateTime.now()),
      });

      _loggingService.info(
        'MessageTemplateService',
        'Template usage recorded',
        {'templateId': templateId},
      );
    } catch (e) {
      _loggingService.error(
        'MessageTemplateService',
        'Failed to record template usage',
        {'templateId': templateId, 'error': e.toString()},
      );
    }
  }

  /// Get quick replies
  List<QuickReplyModel> getQuickReplies({
    MessageTemplateCategory? category,
    String? contextMessage,
  }) {
    List<QuickReplyModel> replies = List.from(_defaultQuickReplies);

    // Filter by category if specified
    if (category != null) {
      replies = replies.where((reply) => reply.category == category).toList();
    }

    // Filter contextual replies based on message content
    if (contextMessage != null && contextMessage.isNotEmpty) {
      final contextualReplies = replies.where((reply) {
        if (!reply.isContextual) return true;
        
        final lowerMessage = contextMessage.toLowerCase();
        return reply.contextKeywords.any((keyword) => 
            lowerMessage.contains(keyword.toLowerCase()));
      }).toList();
      
      if (contextualReplies.isNotEmpty) {
        replies = contextualReplies;
      }
    }

    // Sort by priority
    replies.sort((a, b) => b.priority.compareTo(a.priority));

    return replies.take(6).toList(); // Return top 6 quick replies
  }

  /// Get contextual quick replies based on message content
  List<QuickReplyModel> getContextualQuickReplies(String messageContent) {
    return getQuickReplies(contextMessage: messageContent);
  }
}
