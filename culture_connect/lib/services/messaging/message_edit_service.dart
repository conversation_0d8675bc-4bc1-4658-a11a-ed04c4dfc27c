import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:culture_connect/models/messaging/message_edit_model.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for managing message editing and deletion
class MessageEditService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LoggingService _loggingService = LoggingService();
  
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _loggingService.info('MessageEditService', 'Initializing service');
      _isInitialized = true;
    } catch (e) {
      _loggingService.error('MessageEditService', 'Failed to initialize: $e');
      rethrow;
    }
  }

  /// Edit a message
  Future<bool> editMessage({
    required String messageId,
    required String chatId,
    required String editorUserId,
    required String newText,
    String? editReason,
  }) async {
    await initialize();
    
    try {
      // Get original message
      final messageDoc = await _firestore
          .collection('messages')
          .doc(messageId)
          .get();

      if (!messageDoc.exists) {
        throw Exception('Message not found');
      }

      final originalMessage = MessageModel.fromJson({
        ...messageDoc.data()!,
        'id': messageDoc.id,
      });

      // Check if user can edit this message
      if (originalMessage.senderId != editorUserId) {
        throw Exception('User not authorized to edit this message');
      }

      // Check if message is too old to edit (24 hours limit)
      final timeDifference = DateTime.now().difference(originalMessage.timestamp);
      if (timeDifference.inHours > 24) {
        throw Exception('Message is too old to edit');
      }

      // Create edit record
      final editId = DateTime.now().millisecondsSinceEpoch.toString();
      final editRecord = MessageEditModel(
        id: editId,
        messageId: messageId,
        chatId: chatId,
        editorUserId: editorUserId,
        originalText: originalMessage.text,
        editedText: newText,
        editedAt: DateTime.now(),
        editReason: editReason,
      );

      // Save edit record
      await _firestore
          .collection('message_edits')
          .doc(editId)
          .set(editRecord.toJson());

      // Update original message
      await _firestore
          .collection('messages')
          .doc(messageId)
          .update({
        'text': newText,
        'isEdited': true,
        'editedAt': Timestamp.fromDate(DateTime.now()),
        'metadata.lastEditId': editId,
      });

      _loggingService.info(
        'MessageEditService',
        'Message edited successfully',
        {'messageId': messageId, 'editId': editId},
      );

      return true;
    } catch (e) {
      _loggingService.error(
        'MessageEditService',
        'Failed to edit message',
        {'messageId': messageId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// Delete a message
  Future<bool> deleteMessage({
    required String messageId,
    required String chatId,
    required String deletedByUserId,
    MessageDeletionType deletionType = MessageDeletionType.soft,
    String? deletionReason,
    bool isRecoverable = true,
    Duration? recoverableFor,
  }) async {
    await initialize();
    
    try {
      // Get original message
      final messageDoc = await _firestore
          .collection('messages')
          .doc(messageId)
          .get();

      if (!messageDoc.exists) {
        throw Exception('Message not found');
      }

      final originalMessage = MessageModel.fromJson({
        ...messageDoc.data()!,
        'id': messageDoc.id,
      });

      // Check if user can delete this message
      if (originalMessage.senderId != deletedByUserId) {
        throw Exception('User not authorized to delete this message');
      }

      // Create deletion record
      final deletionId = DateTime.now().millisecondsSinceEpoch.toString();
      final now = DateTime.now();
      final recoverableUntil = recoverableFor != null 
          ? now.add(recoverableFor) 
          : null;

      final deletionRecord = MessageDeletionModel(
        id: deletionId,
        messageId: messageId,
        chatId: chatId,
        deletedByUserId: deletedByUserId,
        deletedAt: now,
        deletionType: deletionType,
        deletionReason: deletionReason,
        isRecoverable: isRecoverable,
        recoverableUntil: recoverableUntil,
      );

      // Save deletion record
      await _firestore
          .collection('message_deletions')
          .doc(deletionId)
          .set(deletionRecord.toJson());

      if (deletionType == MessageDeletionType.hard) {
        // Hard delete - remove completely
        await _firestore
            .collection('messages')
            .doc(messageId)
            .delete();
      } else {
        // Soft delete - mark as deleted
        await _firestore
            .collection('messages')
            .doc(messageId)
            .update({
          'isDeleted': true,
          'deletedAt': Timestamp.fromDate(now),
          'deletionType': deletionType.toString().split('.').last,
          'metadata.deletionId': deletionId,
        });
      }

      _loggingService.info(
        'MessageEditService',
        'Message deleted successfully',
        {
          'messageId': messageId,
          'deletionId': deletionId,
          'deletionType': deletionType.toString(),
        },
      );

      return true;
    } catch (e) {
      _loggingService.error(
        'MessageEditService',
        'Failed to delete message',
        {'messageId': messageId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// Recover a deleted message
  Future<bool> recoverMessage(String messageId) async {
    await initialize();
    
    try {
      // Get deletion record
      final deletionQuery = await _firestore
          .collection('message_deletions')
          .where('messageId', isEqualTo: messageId)
          .orderBy('deletedAt', descending: true)
          .limit(1)
          .get();

      if (deletionQuery.docs.isEmpty) {
        throw Exception('Deletion record not found');
      }

      final deletionRecord = MessageDeletionModel.fromJson({
        ...deletionQuery.docs.first.data(),
        'id': deletionQuery.docs.first.id,
      });

      // Check if message can be recovered
      if (!deletionRecord.canRecover) {
        throw Exception('Message cannot be recovered');
      }

      // Restore message
      await _firestore
          .collection('messages')
          .doc(messageId)
          .update({
        'isDeleted': false,
        'deletedAt': FieldValue.delete(),
        'deletionType': FieldValue.delete(),
        'metadata.deletionId': FieldValue.delete(),
        'metadata.recoveredAt': Timestamp.fromDate(DateTime.now()),
      });

      _loggingService.info(
        'MessageEditService',
        'Message recovered successfully',
        {'messageId': messageId},
      );

      return true;
    } catch (e) {
      _loggingService.error(
        'MessageEditService',
        'Failed to recover message',
        {'messageId': messageId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// Get edit history for a message
  Future<List<MessageEditModel>> getEditHistory(String messageId) async {
    await initialize();
    
    try {
      final querySnapshot = await _firestore
          .collection('message_edits')
          .where('messageId', isEqualTo: messageId)
          .orderBy('editedAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => MessageEditModel.fromJson({
                ...doc.data(),
                'id': doc.id,
              }))
          .toList();
    } catch (e) {
      _loggingService.error(
        'MessageEditService',
        'Failed to get edit history',
        {'messageId': messageId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// Get deletion record for a message
  Future<MessageDeletionModel?> getDeletionRecord(String messageId) async {
    await initialize();
    
    try {
      final querySnapshot = await _firestore
          .collection('message_deletions')
          .where('messageId', isEqualTo: messageId)
          .orderBy('deletedAt', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) return null;

      final doc = querySnapshot.docs.first;
      return MessageDeletionModel.fromJson({
        ...doc.data(),
        'id': doc.id,
      });
    } catch (e) {
      _loggingService.error(
        'MessageEditService',
        'Failed to get deletion record',
        {'messageId': messageId, 'error': e.toString()},
      );
      return null;
    }
  }

  /// Check if message can be edited
  Future<bool> canEditMessage(String messageId, String userId) async {
    await initialize();
    
    try {
      final messageDoc = await _firestore
          .collection('messages')
          .doc(messageId)
          .get();

      if (!messageDoc.exists) return false;

      final message = MessageModel.fromJson({
        ...messageDoc.data()!,
        'id': messageDoc.id,
      });

      // Check ownership
      if (message.senderId != userId) return false;

      // Check time limit (24 hours)
      final timeDifference = DateTime.now().difference(message.timestamp);
      if (timeDifference.inHours > 24) return false;

      // Check if already deleted
      final messageData = messageDoc.data()!;
      if (messageData['isDeleted'] == true) return false;

      return true;
    } catch (e) {
      _loggingService.error(
        'MessageEditService',
        'Failed to check edit permission',
        {'messageId': messageId, 'error': e.toString()},
      );
      return false;
    }
  }

  /// Check if message can be deleted
  Future<bool> canDeleteMessage(String messageId, String userId) async {
    await initialize();
    
    try {
      final messageDoc = await _firestore
          .collection('messages')
          .doc(messageId)
          .get();

      if (!messageDoc.exists) return false;

      final message = MessageModel.fromJson({
        ...messageDoc.data()!,
        'id': messageDoc.id,
      });

      // Check ownership
      if (message.senderId != userId) return false;

      // Check if already deleted
      final messageData = messageDoc.data()!;
      if (messageData['isDeleted'] == true) return false;

      return true;
    } catch (e) {
      _loggingService.error(
        'MessageEditService',
        'Failed to check delete permission',
        {'messageId': messageId, 'error': e.toString()},
      );
      return false;
    }
  }
}
