import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hive/hive.dart';
import 'package:mime/mime.dart';
import 'package:culture_connect/models/messaging/file_message_model.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/utils/media_compression_utils.dart'
    as compression;

/// Service for handling file sharing in messages
class FileSharingService {
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LoggingService _loggingService = LoggingService();

  late Box<Map<String, dynamic>> _fileCache;
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _fileCache = await Hive.openBox<Map<String, dynamic>>('file_cache');
      _isInitialized = true;
      _loggingService.info(
          'FileSharingService', 'Service initialized successfully');
    } catch (e) {
      _loggingService.error(
          'FileSharingService', 'Failed to initialize service: $e');
      rethrow;
    }
  }

  /// Upload a file and create file message
  Future<FileMessageModel?> uploadFile({
    required File file,
    required String messageId,
    required FilePermissions permissions,
    Function(double)? onProgress,
    bool compressFile = true,
  }) async {
    await initialize();

    try {
      final fileName = path.basename(file.path);
      final mimeType = lookupMimeType(file.path) ?? 'application/octet-stream';
      final fileType = FileTypeUtils.getFileTypeFromFilename(fileName);

      _loggingService.info(
          'FileSharingService', 'Starting file upload: $fileName');

      // Compress file if needed and supported
      File fileToUpload = file;
      if (compressFile) {
        final compressedFile = await _compressFileIfNeeded(file, fileType);
        if (compressedFile != null) {
          fileToUpload = compressedFile;
          _loggingService.info(
              'FileSharingService', 'File compressed successfully');
        }
      }

      // Generate file ID and upload path
      final fileId = '${messageId}_${DateTime.now().millisecondsSinceEpoch}';
      final uploadPath = 'file_messages/$messageId/$fileName';

      // Create initial file message model
      final fileMessage = FileMessageModel(
        id: fileId,
        messageId: messageId,
        fileName: fileName,
        fileUrl: '',
        localPath: fileToUpload.path,
        fileSize: await fileToUpload.length(),
        mimeType: mimeType,
        fileType: fileType,
        status: FileMessageStatus.uploading,
        createdAt: DateTime.now(),
        permissions: permissions,
      );

      // Upload file to Firebase Storage
      final ref = _storage.ref().child(uploadPath);
      final uploadTask = ref.putFile(fileToUpload);

      // Monitor upload progress
      uploadTask.snapshotEvents.listen((snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      // Generate thumbnail if applicable
      String? thumbnailUrl;
      if (fileType == FileType.image || fileType == FileType.video) {
        thumbnailUrl = await _generateThumbnail(fileToUpload, fileId, fileType);
      }

      // Update file message with download URL
      final updatedFileMessage = fileMessage.copyWith(
        fileUrl: downloadUrl,
        thumbnailUrl: thumbnailUrl,
        status: FileMessageStatus.uploaded,
      );

      // Save to Firestore
      await _firestore
          .collection('file_messages')
          .doc(fileId)
          .set(updatedFileMessage.toJson());

      // Cache locally
      await _cacheFileMessage(updatedFileMessage);

      _loggingService.info(
          'FileSharingService', 'File uploaded successfully: $fileName');
      return updatedFileMessage;
    } catch (e) {
      _loggingService.error('FileSharingService', 'Failed to upload file: $e');
      return null;
    }
  }

  /// Download a file message
  Future<String?> downloadFile({
    required FileMessageModel fileMessage,
    Function(double)? onProgress,
  }) async {
    await initialize();

    try {
      // Check permissions
      if (!fileMessage.permissions.canDownload) {
        throw Exception('Download not permitted for this file');
      }

      if (fileMessage.permissions.isExpired) {
        throw Exception('File has expired');
      }

      // Check if already downloaded
      if (fileMessage.isLocallyAvailable) {
        final localFile = File(fileMessage.localPath!);
        if (await localFile.exists()) {
          return fileMessage.localPath;
        }
      }

      _loggingService.info('FileSharingService',
          'Starting file download: ${fileMessage.fileName}');

      // Create download directory
      final directory = await getApplicationDocumentsDirectory();
      final downloadDir = Directory('${directory.path}/downloads');
      await downloadDir.create(recursive: true);

      // Generate local file path
      final localPath = '${downloadDir.path}/${fileMessage.fileName}';
      final localFile = File(localPath);

      // Download from Firebase Storage
      final ref = _storage.refFromURL(fileMessage.fileUrl);

      // Use download task for progress monitoring
      final downloadTask = ref.writeToFile(localFile);

      // Monitor download progress (if supported)
      onProgress?.call(0.0);

      await downloadTask;
      onProgress?.call(1.0);

      // Update file message with local path
      final updatedFileMessage = fileMessage.copyWith(
        localPath: localPath,
        status: FileMessageStatus.downloaded,
        downloadedAt: DateTime.now(),
        downloadCount: fileMessage.downloadCount + 1,
      );

      // Update in Firestore
      await _firestore.collection('file_messages').doc(fileMessage.id).update({
        'localPath': localPath,
        'status': FileMessageStatus.downloaded.toString().split('.').last,
        'downloadedAt': DateTime.now().millisecondsSinceEpoch,
        'downloadCount': updatedFileMessage.downloadCount,
      });

      // Update cache
      await _cacheFileMessage(updatedFileMessage);

      _loggingService.info('FileSharingService',
          'File downloaded successfully: ${fileMessage.fileName}');
      return localPath;
    } catch (e) {
      _loggingService.error(
          'FileSharingService', 'Failed to download file: $e');
      return null;
    }
  }

  /// Get file message by ID
  Future<FileMessageModel?> getFileMessage(String fileId) async {
    await initialize();

    try {
      // Check cache first
      final cachedData = _fileCache.get(fileId);
      if (cachedData != null) {
        return FileMessageModel.fromJson(Map<String, dynamic>.from(cachedData));
      }

      // Fetch from Firestore
      final doc =
          await _firestore.collection('file_messages').doc(fileId).get();
      if (doc.exists) {
        final fileMessage = FileMessageModel.fromJson(doc.data()!);
        await _cacheFileMessage(fileMessage);
        return fileMessage;
      }

      return null;
    } catch (e) {
      _loggingService.error(
          'FileSharingService', 'Failed to get file message: $e');
      return null;
    }
  }

  /// Get file messages for a message
  Future<List<FileMessageModel>> getFileMessagesForMessage(
      String messageId) async {
    await initialize();

    try {
      final query = await _firestore
          .collection('file_messages')
          .where('messageId', isEqualTo: messageId)
          .get();

      final fileMessages = query.docs
          .map((doc) => FileMessageModel.fromJson(doc.data()))
          .toList();

      // Cache all file messages
      for (final fileMessage in fileMessages) {
        await _cacheFileMessage(fileMessage);
      }

      return fileMessages;
    } catch (e) {
      _loggingService.error(
          'FileSharingService', 'Failed to get file messages: $e');
      return [];
    }
  }

  /// Delete a file message
  Future<bool> deleteFileMessage(String fileId) async {
    await initialize();

    try {
      final fileMessage = await getFileMessage(fileId);
      if (fileMessage == null) return false;

      // Check permissions
      if (!fileMessage.permissions.canDelete) {
        throw Exception('Delete not permitted for this file');
      }

      // Delete from Firebase Storage
      try {
        final ref = _storage.refFromURL(fileMessage.fileUrl);
        await ref.delete();
      } catch (e) {
        _loggingService.warning(
            'FileSharingService', 'Failed to delete file from storage: $e');
      }

      // Delete thumbnail if exists
      if (fileMessage.thumbnailUrl != null) {
        try {
          final thumbnailRef = _storage.refFromURL(fileMessage.thumbnailUrl!);
          await thumbnailRef.delete();
        } catch (e) {
          _loggingService.warning(
              'FileSharingService', 'Failed to delete thumbnail: $e');
        }
      }

      // Delete local file if exists
      if (fileMessage.isLocallyAvailable) {
        try {
          final localFile = File(fileMessage.localPath!);
          if (await localFile.exists()) {
            await localFile.delete();
          }
        } catch (e) {
          _loggingService.warning(
              'FileSharingService', 'Failed to delete local file: $e');
        }
      }

      // Delete from Firestore
      await _firestore.collection('file_messages').doc(fileId).delete();

      // Remove from cache
      await _fileCache.delete(fileId);

      _loggingService.info(
          'FileSharingService', 'File message deleted successfully: $fileId');
      return true;
    } catch (e) {
      _loggingService.error(
          'FileSharingService', 'Failed to delete file message: $e');
      return false;
    }
  }

  /// Update file permissions
  Future<bool> updateFilePermissions(
      String fileId, FilePermissions permissions) async {
    await initialize();

    try {
      await _firestore.collection('file_messages').doc(fileId).update({
        'permissions': permissions.toJson(),
      });

      // Update cache
      final cachedData = _fileCache.get(fileId);
      if (cachedData != null) {
        final fileMessage =
            FileMessageModel.fromJson(Map<String, dynamic>.from(cachedData));
        final updatedFileMessage =
            fileMessage.copyWith(permissions: permissions);
        await _cacheFileMessage(updatedFileMessage);
      }

      _loggingService.info(
          'FileSharingService', 'File permissions updated: $fileId');
      return true;
    } catch (e) {
      _loggingService.error(
          'FileSharingService', 'Failed to update file permissions: $e');
      return false;
    }
  }

  /// Compress file if needed
  Future<File?> _compressFileIfNeeded(File file, FileType fileType) async {
    try {
      switch (fileType) {
        case FileType.image:
          return File(await compression.MediaCompressionUtils.compressImage(
                inputPath: file.path,
                quality: 85,
              ) ??
              file.path);
        case FileType.video:
          return File(await compression.MediaCompressionUtils.compressVideo(
                inputPath: file.path,
              ) ??
              file.path);
        case FileType.audio:
          return File(await compression.MediaCompressionUtils.compressAudio(
                inputPath: file.path,
              ) ??
              file.path);
        default:
          return null;
      }
    } catch (e) {
      _loggingService.warning(
          'FileSharingService', 'File compression failed: $e');
      return null;
    }
  }

  /// Generate thumbnail for file
  Future<String?> _generateThumbnail(
      File file, String fileId, FileType fileType) async {
    try {
      String? thumbnailPath;

      if (fileType == FileType.video) {
        thumbnailPath =
            await compression.MediaCompressionUtils.generateVideoThumbnail(
          videoPath: file.path,
        );
      } else if (fileType == FileType.image) {
        // For images, create a smaller version as thumbnail
        thumbnailPath = await compression.MediaCompressionUtils.compressImage(
          inputPath: file.path,
          quality: 70,
          minWidth: 200,
          minHeight: 200,
        );
      }

      if (thumbnailPath != null) {
        // Upload thumbnail to Firebase Storage
        final thumbnailFile = File(thumbnailPath);
        final uploadPath = 'thumbnails/$fileId/thumbnail.jpg';
        final ref = _storage.ref().child(uploadPath);
        await ref.putFile(thumbnailFile);
        return await ref.getDownloadURL();
      }

      return null;
    } catch (e) {
      _loggingService.warning(
          'FileSharingService', 'Thumbnail generation failed: $e');
      return null;
    }
  }

  /// Cache file message locally
  Future<void> _cacheFileMessage(FileMessageModel fileMessage) async {
    try {
      await _fileCache.put(fileMessage.id, fileMessage.toJson());
    } catch (e) {
      _loggingService.warning(
          'FileSharingService', 'Failed to cache file message: $e');
    }
  }

  /// Clear cache
  Future<void> clearCache() async {
    await initialize();

    try {
      await _fileCache.clear();
      _loggingService.info('FileSharingService', 'Cache cleared successfully');
    } catch (e) {
      _loggingService.error('FileSharingService', 'Failed to clear cache: $e');
    }
  }

  /// Get cache size
  Future<int> getCacheSize() async {
    await initialize();
    return _fileCache.length;
  }
}
